/**
 * Advanced Settings Form Section
 * 
 * Advanced widget configuration options
 */

import { UseFormReturn } from 'react-hook-form'
import { Widget } from '../../types'
import { FormField, FormLabel, FormControl, FormItem, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertTriangleIcon } from 'lucide-react'

interface AdvancedSettingsProps {
    form: UseFormReturn<Widget>
}

export function AdvancedSettings({ form }: AdvancedSettingsProps) {
    return (
        <div className="space-y-6">
            <h2 className="text-lg font-medium">Advanced Settings</h2>
            <p className="text-sm text-gray-500">
                Configure advanced options for your widget
            </p>

            <Alert variant="warning" className="mb-6">
                <AlertTriangleIcon className="h-4 w-4" />
                <AlertTitle>Warning</AlertTitle>
                <AlertDescription>
                    These settings are for advanced users. Incorrect configuration may break your widget functionality.
                </AlertDescription>
            </Alert>

            <div className="space-y-8">
                {/* Custom Code Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Custom Code</h3>

                    <Tabs defaultValue="css" className="w-full">
                        <TabsList className="grid grid-cols-2">
                            <TabsTrigger value="css">Custom CSS</TabsTrigger>
                            <TabsTrigger value="js">Custom JavaScript</TabsTrigger>
                        </TabsList>

                        <TabsContent value="css">
                            <FormField
                                control={form.control}
                                name="advanced.customCSS"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Custom CSS</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder=".chat-widget { /* your custom styles */ }"
                                                rows={8}
                                                className="font-mono text-sm"
                                                {...field}
                                                value={field.value || ''}
                                            />
                                        </FormControl>
                                        <p className="text-xs text-gray-500 mt-2">
                                            Custom CSS will be applied to the widget container. Use classes starting with .chat-widget-* to target elements.
                                        </p>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </TabsContent>

                        <TabsContent value="js">
                            <FormField
                                control={form.control}
                                name="advanced.customJS"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Custom JavaScript</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="// Custom widget behavior"
                                                rows={8}
                                                className="font-mono text-sm"
                                                {...field}
                                                value={field.value || ''}
                                            />
                                        </FormControl>
                                        <p className="text-xs text-gray-500 mt-2">
                                            Custom JavaScript will run after widget initialization. Use window.chatWidget.* to interact with the widget API.
                                        </p>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </TabsContent>
                    </Tabs>
                </div>

                {/* SEO Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">SEO</h3>

                    <FormField
                        control={form.control}
                        name="advanced.seo.noIndex"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">No Index</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Prevent search engines from indexing the widget content
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Webhook Integration */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Webhook</h3>

                    <p className="text-sm text-gray-500">
                        Configure webhook integration in the Integrations section
                    </p>
                </div>
            </div>
        </div>
    )
}

export default AdvancedSettings 