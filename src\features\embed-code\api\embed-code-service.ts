/**
 * Embed Code Service
 * 
 * Service for generating and managing widget embed codes
 */

import api from '@/utils/api'
import { EmbedConfiguration, EmbedCode, EmbedStats } from '../types'

export const embedCodeService = {
  /**
   * Generate embed code for a widget
   */
  generateEmbedCode: async (widgetId: number, config: EmbedConfiguration): Promise<EmbedCode> => {
    try {
      const response = await api.post(`/widgets/${widgetId}/embed-code`, config)
      return response.data.data
    } catch (error) {
      console.error('Error generating embed code:', error)
      throw error
    }
  },

  /**
   * Get embed configuration for a widget
   */
  getEmbedConfiguration: async (widgetId: number): Promise<EmbedConfiguration> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/embed-configuration`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching embed configuration:', error)
      throw error
    }
  },

  /**
   * Update embed configuration
   */
  updateEmbedConfiguration: async (widgetId: number, config: Partial<EmbedConfiguration>): Promise<EmbedConfiguration> => {
    try {
      const response = await api.put(`/widgets/${widgetId}/embed-configuration`, config)
      return response.data.data
    } catch (error) {
      console.error('Error updating embed configuration:', error)
      throw error
    }
  },

  /**
   * Get embed statistics
   */
  getEmbedStats: async (widgetId: number): Promise<EmbedStats> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/embed-stats`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching embed stats:', error)
      throw error
    }
  },

  /**
   * Validate embed domain
   */
  validateDomain: async (domain: string): Promise<boolean> => {
    try {
      const response = await api.post('/embed/validate-domain', { domain })
      return response.data.valid
    } catch (error) {
      console.error('Error validating domain:', error)
      throw error
    }
  },

  /**
   * Get embed preview
   */
  getEmbedPreview: async (widgetId: number): Promise<string> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/embed-preview`)
      return response.data.html
    } catch (error) {
      console.error('Error fetching embed preview:', error)
      throw error
    }
  }
}
