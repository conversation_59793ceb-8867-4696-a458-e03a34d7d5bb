// Import the API instance from the modular file
import api from "./api";

export interface KnowledgeDocument {
  id: number;
  file_name: string;
  file_type: string;
  size: number;
  status: string;
  is_active_source: boolean;
  category?: string;
  project_id?: number;
  has_embeddings?: boolean;
  embeddings_count?: number;
  embeddings_provider?: string;
  embeddings_model?: string;
  embeddings_generated_at?: string;
  created_at: string;
  source_id: number;
}

export interface KnowledgeSource {
  id: number;
  type: string;
  name: string;
  description?: string;
  priority: number;
  status: string;
  created_at: string;
  documents_count?: number;
}

export interface ScrapedUrl {
  id: number;
  url: string;
  title?: string;
  status: string;
  project_id?: number;
  table_name?: string;
  created_at: string;
  source_id: number;
}

export interface Project {
  id: number;
  name: string;
  description?: string;
}

export interface Table {
  name: string;
  description?: string;
  schema?: string;
  row_count?: number;
  created_at?: string;
  updated_at?: string;
}

export interface ContextSettings {
  id: number;
  project_id: number;
  priority: {
    documents: number;
    database: number;
    web: number;
  };
  context_retention: string;
  relevance_threshold: number;
  max_sources_per_query: number;
  enabled_sources: {
    documents: boolean;
    database: boolean;
    web: boolean;
  };
}

export interface ContextRule {
  id: number;
  project_id: number;
  name: string;
  description?: string;
  sources: string[];
  keywords: string[];
  priority: number;
  active: boolean;
  match_score?: number;
}

export const knowledgeBaseService = {
  // Vector embedding operations
  generateEmbeddings: async (documentId: number, options?: {
    provider?: 'openai' | 'huggingface';
    chunk_size?: number;
    chunk_overlap?: number;
  }) => {
    try {
      return await api.post(`knowledge-base/documents/${documentId}/embeddings`, options);
    } catch (error) {
      console.error(`Error generating embeddings for document ${documentId}:`, error);
      throw error;
    }
  },

  batchGenerateEmbeddings: async (documentIds: number[], options?: {
    provider?: 'openai' | 'huggingface';
    chunk_size?: number;
    chunk_overlap?: number;
  }) => {
    try {
      return await api.post('knowledge-base/batch-embeddings', {
        document_ids: documentIds,
        ...options
      });
    } catch (error) {
      console.error('Error batch generating embeddings:', error);
      throw error;
    }
  },

  getEmbeddingStatus: async (documentIds: number[]) => {
    try {
      return await api.post('knowledge-base/embedding-status', {
        document_ids: documentIds
      });
    } catch (error) {
      console.error('Error getting embedding status:', error);
      throw error;
    }
  },

  getEmbeddingModels: async () => {
    try {
      return await api.get('knowledge-base/embedding-models');
    } catch (error) {
      console.error('Error getting embedding models:', error);
      throw error;
    }
  },
  // Project operations
  getProjects: async () => {
    try {
      return await api.get('projects');
    } catch (error) {
      console.error("Error fetching projects:", error);
      throw error;
    }
  },

  createProject: async (data: { name: string; description?: string }) => {
    try {
      // Ensure CSRF token is fetched before making the request
      await import('./authService').then(auth => auth.getCsrfToken());
      return await api.post('projects', data);
    } catch (error) {
      console.error("Error creating project:", error);
      throw error;
    }
  },

  // Table operations
  getProjectTables: async (projectId: number) => {
    try {
      return await api.get(`knowledge-base/projects/${projectId}/tables`);
    } catch (error) {
      console.error(`Error fetching tables for project ${projectId}:`, error);
      throw error;
    }
  },

  getTableDetails: async (projectId: number, tableName: string) => {
    try {
      return await api.get(`knowledge-base/projects/${projectId}/tables/${tableName}`);
    } catch (error) {
      console.error(`Error fetching table details for ${tableName}:`, error);
      throw error;
    }
  },

  executeQuery: async (projectId: number, query: string, limit: number = 100) => {
    try {
      return await api.post(`knowledge-base/execute-query`, {
        project_id: projectId,
        query,
        limit
      });
    } catch (error) {
      console.error('Error executing query:', error);
      throw error;
    }
  },

  addTableToKnowledgeBase: async (projectId: number, tableName: string, options: any) => {
    try {
      return await api.post(`knowledge-base/projects/${projectId}/tables/${tableName}/add`, options);
    } catch (error) {
      console.error('Error adding table to knowledge base:', error);
      throw error;
    }
  },

  // Database connection methods
  getDatabaseConnections: async (projectId: number) => {
    try {
      return await api.get(`knowledge-base/projects/${projectId}/database-connections`);
    } catch (error) {
      console.error('Error fetching database connections:', error);
      throw error;
    }
  },

  saveDatabaseConnection: async (connectionData: any) => {
    try {
      return await api.post('knowledge-base/database-connections', connectionData);
    } catch (error) {
      console.error('Error saving database connection:', error);
      throw error;
    }
  },

  deleteDatabaseConnection: async (connectionId: number) => {
    try {
      return await api.delete(`knowledge-base/database-connections/${connectionId}`);
    } catch (error) {
      console.error('Error deleting database connection:', error);
      throw error;
    }
  },

  testDatabaseConnection: async (connectionData: any) => {
    try {
      return await api.post('knowledge-base/database-connections/test', connectionData);
    } catch (error) {
      console.error('Error testing database connection:', error);
      throw error;
    }
  },

  // Source operations
  getSources: async () => {
    try {
      return await api.get('knowledge-base/sources');
    } catch (error) {
      console.error("Error fetching sources:", error);
      throw error;
    }
  },

  createSource: async (data: { type: string; name: string; description?: string; priority?: number; status?: string; project_id?: number }) => {
    try {
      // Ensure CSRF token is fetched before making the request
      await import('./authService').then(auth => auth.getCsrfToken());
      return await api.post('knowledge-base/sources', data);
    } catch (error) {
      console.error("Error creating source:", error);
      throw error;
    }
  },

  getSource: async (sourceId: number) => {
    try {
      return await api.get(`knowledge-base/sources/${sourceId}`);
    } catch (error) {
      console.error(`Error fetching source ${sourceId}:`, error);
      throw error;
    }
  },

  updateSource: async (sourceId: number, data: { type?: string; name?: string; description?: string; priority?: number; status?: string }) => {
    try {
      return await api.put(`knowledge-base/sources/${sourceId}`, data);
    } catch (error) {
      console.error(`Error updating source ${sourceId}:`, error);
      throw error;
    }
  },

  deleteSource: async (sourceId: number) => {
    try {
      return await api.delete(`knowledge-base/sources/${sourceId}`);
    } catch (error) {
      console.error(`Error deleting source ${sourceId}:`, error);
      throw error;
    }
  },

  // Document operations
  getDocuments: async (projectId?: number) => {
    try {
      const url = projectId
        ? `knowledge-base/documents?project_id=${projectId}`
        : 'knowledge-base/documents';
      return await api.get(url);
    } catch (error) {
      console.error("Error fetching documents:", error);
      throw error;
    }
  },

  getDocumentsBySource: async (sourceId: number) => {
    try {
      return await api.get(`knowledge-base/sources/${sourceId}/documents`);
    } catch (error) {
      console.error(`Error fetching documents for source ${sourceId}:`, error);
      throw error;
    }
  },

  uploadDocument: async (file: File, data: {
    category?: string;
    project_id?: number;
    is_active_source?: boolean;
    source_id?: number;
  }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      if (data.category) {
        formData.append('category', data.category);
      }

      if (data.project_id) {
        formData.append('project_id', data.project_id.toString());
      }

      if (data.is_active_source !== undefined) {
        formData.append('is_active_source', data.is_active_source ? '1' : '0');
      }

      if (data.source_id) {
        formData.append('source_id', data.source_id.toString());
      }

      return await api.post('knowledge-base/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    } catch (error) {
      console.error("Error uploading document:", error);
      throw error;
    }
  },

  deleteDocument: async (documentId: number) => {
    try {
      return await api.delete(`knowledge-base/documents/${documentId}`);
    } catch (error) {
      console.error(`Error deleting document ${documentId}:`, error);
      throw error;
    }
  },

  toggleDocumentAsAISource: async (documentId: number, isActiveSource: boolean) => {
    try {
      return await api.post(`knowledge-base/documents/${documentId}/toggle-active`, {
        is_active_source: isActiveSource
      });
    } catch (error) {
      console.error(`Error toggling document ${documentId} as AI source:`, error);
      throw error;
    }
  },

  // URL scraping operations
  scrapeUrl: async (url: string, data: {
    project_id?: number;
    table_name?: string;
    source_id?: number;
    model_id?: number;
    render_js?: boolean;
    use_cache?: boolean;
    wait_for_selector?: string;
    scroll_page?: boolean;
  }) => {
    try {
      return await api.post('knowledge-base/scrape-url', {
        url,
        project_id: data.project_id,
        table_name: data.table_name,
        source_id: data.source_id,
        model_id: data.model_id,
        render_js: data.render_js,
        use_cache: data.use_cache,
        wait_for_selector: data.wait_for_selector,
        scroll_page: data.scroll_page
      });
    } catch (error) {
      console.error("Error scraping URL:", error);
      throw error;
    }
  },

  saveScrape: async (data: {
    content: string;
    format: 'raw' | 'text' | 'table' | 'json';
    url: string;
    title?: string;
    project_id: number;
    table_name: string;
    source_id?: number;
    model_id?: number;
    metadata?: Record<string, any>;
  }) => {
    try {
      return await api.post('knowledge-base/save-scrape', data);
    } catch (error) {
      console.error("Error saving scraped content:", error);
      throw error;
    }
  },

  getScrapedContentTables: async () => {
    try {
      return await api.get('knowledge-base/scraped-content-tables');
    } catch (error) {
      console.error("Error getting scraped content tables:", error);
      throw error;
    }
  },

  // Scheduled scraping operations
  getScheduledScrapes: async (filters: { project_id?: number; status?: string }) => {
    try {
      const queryParams = new URLSearchParams();

      if (filters.project_id) {
        queryParams.append('project_id', filters.project_id.toString());
      }

      if (filters.status) {
        queryParams.append('status', filters.status);
      }

      const url = `knowledge-base/scheduled-scrapes${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      return await api.get(url);
    } catch (error) {
      console.error("Error getting scheduled scrapes:", error);
      throw error;
    }
  },

  saveScheduledScrape: async (data: {
    id?: number;
    name: string;
    url: string;
    project_id: number;
    table_name: string;
    format: 'raw' | 'text' | 'table' | 'json';
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
    model_id?: number;
    status?: string;
    options?: Record<string, any>;
    auth?: {
      username?: string;
      password?: string;
      cookies?: Array<Record<string, any>>;
      form?: {
        url?: string;
        username_selector?: string;
        password_selector?: string;
        submit_selector?: string;
        username?: string;
        password?: string;
      };
    };
  }) => {
    try {
      return await api.post('knowledge-base/scheduled-scrapes', data);
    } catch (error) {
      console.error("Error saving scheduled scrape:", error);
      throw error;
    }
  },

  deleteScheduledScrape: async (id: number) => {
    try {
      return await api.delete(`knowledge-base/scheduled-scrapes/${id}`);
    } catch (error) {
      console.error("Error deleting scheduled scrape:", error);
      throw error;
    }
  },

  runScheduledScrape: async (id: number) => {
    try {
      return await api.post(`knowledge-base/scheduled-scrapes/${id}/run`);
    } catch (error) {
      console.error("Error running scheduled scrape:", error);
      throw error;
    }
  },

  getScrapedUrls: async (filters?: { source_id?: number; project_id?: number }) => {
    try {
      let url = 'knowledge-base/scraped-urls';
      const params = new URLSearchParams();

      if (filters?.source_id) {
        params.append('source_id', filters.source_id.toString());
      }

      if (filters?.project_id) {
        params.append('project_id', filters.project_id.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      return await api.get(url);
    } catch (error) {
      console.error("Error fetching scraped URLs:", error);
      throw error;
    }
  },

  deleteScrapedUrl: async (id: number) => {
    try {
      return await api.delete(`knowledge-base/scraped-urls/${id}`);
    } catch (error) {
      console.error(`Error deleting scraped URL ${id}:`, error);
      throw error;
    }
  },

  // Context module methods
  getContextSettings: async (projectId: number) => {
    try {
      return await api.get(`knowledge-base/projects/${projectId}/context`);
    } catch (error) {
      console.error(`Error getting context settings for project ${projectId}:`, error);
      throw error;
    }
  },

  updateContextSettings: async (projectId: number, settings: {
    priority?: { documents: number; database: number; web: number };
    contextRetention?: string;
    relevanceThreshold?: number;
    maxSourcesPerQuery?: number;
    enabledSources?: { documents: boolean; database: boolean; web: boolean };
  }) => {
    try {
      return await api.put(`knowledge-base/projects/${projectId}/context`, settings);
    } catch (error) {
      console.error(`Error updating context settings for project ${projectId}:`, error);
      throw error;
    }
  },

  getContextRules: async (projectId: number) => {
    try {
      return await api.get(`knowledge-base/projects/${projectId}/context/rules`);
    } catch (error) {
      console.error(`Error getting context rules for project ${projectId}:`, error);
      throw error;
    }
  },

  saveContextRule: async (projectId: number, data: {
    name: string;
    description?: string;
    sources?: string[];
    keywords?: string[];
    priority?: number;
    active?: boolean;
  }) => {
    try {
      return await api.post(`knowledge-base/projects/${projectId}/context/rules`, data);
    } catch (error) {
      console.error(`Error saving context rule for project ${projectId}:`, error);
      throw error;
    }
  },

  updateContextRule: async (projectId: number, ruleId: number, data: {
    name?: string;
    description?: string;
    sources?: string[];
    keywords?: string[];
    priority?: number;
    active?: boolean;
  }) => {
    try {
      return await api.put(`knowledge-base/projects/${projectId}/context/rules/${ruleId}`, data);
    } catch (error) {
      console.error(`Error updating context rule ${ruleId} for project ${projectId}:`, error);
      throw error;
    }
  },

  deleteContextRule: async (projectId: number, ruleId: number) => {
    try {
      return await api.delete(`knowledge-base/projects/${projectId}/context/rules/${ruleId}`);
    } catch (error) {
      console.error(`Error deleting context rule ${ruleId} for project ${projectId}:`, error);
      throw error;
    }
  },

  testContextRule: async (projectId: number, query: string) => {
    try {
      return await api.post(`knowledge-base/projects/${projectId}/context/test`, { query });
    } catch (error) {
      console.error(`Error testing context rule for project ${projectId}:`, error);
      throw error;
    }
  },

  searchSimilarDocuments: async (query: string, filters: {
    project_id: number;
    category?: string;
    source_id?: number;
    limit?: number;
    threshold?: number;
  }) => {
    try {
      return await api.post('knowledge-base/search', {
        query,
        ...filters
      });
    } catch (error) {
      console.error('Error searching similar documents:', error);
      throw error;
    }
  }
};
