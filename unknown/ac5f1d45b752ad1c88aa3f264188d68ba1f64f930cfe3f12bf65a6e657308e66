<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\KnowledgeDocument;
use App\Models\KnowledgeEmbedding;
use App\Models\AIModel;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Exception;
use Throwable;

class VectorEmbeddingService
{
    /**
     * Configuration for embedding models
     */
    protected $embeddingModels = [
        'openai' => [
            'name' => 'OpenAI Ada',
            'endpoint' => 'https://api.openai.com/v1/embeddings',
            'model' => 'text-embedding-ada-002',
            'dimensions' => 1536,
            'max_tokens' => 8191,
        ],
        'huggingface' => [
            'name' => 'Hugging Face Sentence Transformers',
            'endpoint' => 'https://api-inference.huggingface.co/pipeline/feature-extraction',
            'model' => 'sentence-transformers/all-mpnet-base-v2',
            'dimensions' => 768,
            'max_tokens' => 512,
        ],
    ];

    /**
     * Generate embeddings for a document
     *
     * @param KnowledgeDocument $document
     * @param string $provider
     * @param array $options
     * @return bool
     */
    public function generateEmbeddings(KnowledgeDocument $document, string $provider = 'openai', array $options = []): bool
    {
        $lockKey = "embedding_lock_doc_{$document->id}";
        $cacheKey = "embedding_status_doc_{$document->id}";

        // Check if there's already an embedding process running for this document
        if (Cache::has($lockKey)) {
            Log::info("Embedding generation already in progress for document {$document->id}");
            return false;
        }

        // Set a lock to prevent concurrent embedding generation
        Cache::put($lockKey, true, 3600); // 1 hour lock

        try {
            // Check if document has extracted text
            if (empty($document->extracted_text)) {
                Log::warning("Document {$document->id} has no extracted text for embedding generation");
                $this->updateDocumentStatus($document, 'failed_embeddings', 'No extracted text available');
                return false;
            }

            // Get model information if model_id is provided
            $modelName = $this->embeddingModels[$provider]['model'];
            if (!empty($options['model_id'])) {
                $model = AIModel::find($options['model_id']);
                if ($model) {
                    $provider = strtolower($model->provider);
                    $modelName = $model->name;
                    $options['api_key'] = $model->api_key;
                }
            }

            // Update document status to processing
            $this->updateDocumentStatus($document, 'processing_embeddings');

            // Check if we already have some embeddings for this document
            $existingCount = KnowledgeEmbedding::where('document_id', $document->id)->count();

            // Get text chunks
            $chunkSize = $options['chunk_size'] ?? 1000;
            $chunkOverlap = $options['chunk_overlap'] ?? 200;
            $chunks = $this->chunkText($document->extracted_text, $chunkSize, $chunkOverlap);

            // Store status in cache for monitoring
            Cache::put($cacheKey, [
                'status' => 'processing',
                'total_chunks' => count($chunks),
                'processed_chunks' => $existingCount,
                'success_count' => $existingCount,
                'error_count' => 0,
                'last_error' => null,
                'start_time' => now()->timestamp,
            ], 3600);

            // Generate embeddings for each chunk
            $embeddingsData = [];
            $successCount = $existingCount;
            $errorCount = 0;
            $totalChunks = count($chunks);
            $lastError = null;

            // Skip chunks that already have embeddings
            $startIndex = $existingCount;
            $chunksToProcess = array_slice($chunks, $startIndex);

            foreach ($chunksToProcess as $index => $chunk) {
                $actualIndex = $index + $startIndex;

                try {
                    $embedding = $this->callEmbeddingAPI($chunk, $provider, $options);

                    if ($embedding) {
                        $embeddingsData[] = [
                            'document_id' => $document->id,
                            'chunk_index' => $actualIndex,
                            'chunk_text' => $chunk,
                            'embedding' => json_encode($embedding),
                            'provider' => $provider,
                            'model' => $modelName,
                            'dimensions' => count($embedding),
                            'metadata' => json_encode([
                                'position' => $actualIndex,
                                'total_chunks' => $totalChunks,
                                'document_name' => $document->file_name,
                                'category' => $document->category,
                                'chunk_size' => $chunkSize,
                                'chunk_overlap' => $chunkOverlap,
                            ]),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                        $successCount++;

                        // Update progress periodically
                        if ($successCount % 5 === 0 || $successCount === $totalChunks) {
                            $progress = intval(($successCount / $totalChunks) * 100);
                            $document->update([
                                'embeddings_progress' => $progress
                            ]);

                            // Update cache status
                            Cache::put($cacheKey, [
                                'status' => 'processing',
                                'total_chunks' => $totalChunks,
                                'processed_chunks' => $successCount + $errorCount,
                                'success_count' => $successCount,
                                'error_count' => $errorCount,
                                'last_error' => $lastError,
                                'progress' => $progress,
                                'start_time' => Cache::get($cacheKey)['start_time'],
                            ], 3600);
                        }

                        // Store embeddings in batches to avoid memory issues
                        if (count($embeddingsData) >= 20) {
                            DB::beginTransaction();
                            try {
                                KnowledgeEmbedding::insert($embeddingsData);
                                DB::commit();
                                $embeddingsData = []; // Clear after successful insert
                            } catch (Throwable $e) {
                                DB::rollBack();
                                Log::error("Failed to insert embedding batch: " . $e->getMessage());
                                throw $e;
                            }
                        }
                    } else {
                        $errorCount++;
                        $lastError = "Failed to generate embedding for chunk {$actualIndex}";
                        Log::warning("Failed to generate embedding for document {$document->id}, chunk {$actualIndex}");
                    }
                } catch (Throwable $e) {
                    $errorCount++;
                    $lastError = $e->getMessage();
                    Log::error("Error processing chunk {$actualIndex} for document {$document->id}: " . $e->getMessage());

                    // Continue with next chunk instead of failing the entire process
                    continue;
                }
            }

            // Store any remaining embeddings
            if (!empty($embeddingsData)) {
                DB::beginTransaction();
                try {
                    KnowledgeEmbedding::insert($embeddingsData);
                    DB::commit();
                } catch (Throwable $e) {
                    DB::rollBack();
                    Log::error("Failed to insert final embedding batch: " . $e->getMessage());
                    throw $e;
                }
            }

            // Get total count of embeddings for this document
            $totalEmbeddings = KnowledgeEmbedding::where('document_id', $document->id)->count();

            // Update document status based on results
            if ($totalEmbeddings > 0) {
                $status = ($totalEmbeddings == $totalChunks) ? 'ready' : 'partial_embeddings';
                $document->update([
                    'has_embeddings' => true,
                    'embeddings_count' => $totalEmbeddings,
                    'embeddings_provider' => $provider,
                    'embeddings_model' => $modelName,
                    'embeddings_generated_at' => now(),
                    'embeddings_progress' => intval(($totalEmbeddings / $totalChunks) * 100),
                    'status' => $status
                ]);

                // Update cache status
                Cache::put($cacheKey, [
                    'status' => 'completed',
                    'total_chunks' => $totalChunks,
                    'processed_chunks' => $totalChunks,
                    'success_count' => $totalEmbeddings,
                    'error_count' => $errorCount,
                    'last_error' => $lastError,
                    'progress' => intval(($totalEmbeddings / $totalChunks) * 100),
                    'start_time' => Cache::get($cacheKey)['start_time'],
                    'end_time' => now()->timestamp,
                ], 3600);

                return true;
            }

            // If we got here, no embeddings were generated
            $this->updateDocumentStatus($document, 'failed_embeddings', $lastError ?? 'No embeddings were generated');
            return false;
        } catch (Throwable $e) {
            Log::error("Error generating embeddings for document {$document->id}: " . $e->getMessage());

            // Update cache status
            if (Cache::has($cacheKey)) {
                $status = Cache::get($cacheKey);
                $status['status'] = 'failed';
                $status['last_error'] = $e->getMessage();
                $status['end_time'] = now()->timestamp;
                Cache::put($cacheKey, $status, 3600);
            }

            // Update document status to failed
            $this->updateDocumentStatus($document, 'failed_embeddings', $e->getMessage());

            return false;
        } finally {
            // Always release the lock
            Cache::forget($lockKey);
        }
    }

    /**
     * Update document status with error tracking
     *
     * @param KnowledgeDocument $document
     * @param string $status
     * @param string|null $errorMessage
     * @return void
     */
    protected function updateDocumentStatus(KnowledgeDocument $document, string $status, ?string $errorMessage = null): void
    {
        $updateData = ['status' => $status];

        if ($errorMessage) {
            $metadata = $document->metadata ?? [];
            $metadata['last_error'] = $errorMessage;
            $metadata['error_timestamp'] = now()->toIso8601String();
            $updateData['metadata'] = $metadata;
        }

        $document->update($updateData);
    }

    /**
     * Generate embeddings for multiple documents
     *
     * @param array $documentIds
     * @param string $provider
     * @param array $options
     * @return array
     */
    public function batchGenerateEmbeddings(array $documentIds, string $provider = 'openai', array $options = []): array
    {
        $batchId = uniqid('batch_');
        $cacheKey = "embedding_batch_{$batchId}";

        // Initialize batch status
        $batchStatus = [
            'id' => $batchId,
            'total' => count($documentIds),
            'pending' => count($documentIds),
            'processing' => 0,
            'successful' => 0,
            'failed' => 0,
            'details' => [],
            'start_time' => now()->timestamp,
            'estimated_completion' => null,
        ];

        // Store initial status in cache
        Cache::put($cacheKey, $batchStatus, 24 * 3600); // 24 hours

        // Process documents in smaller batches to avoid memory issues
        $documentBatches = array_chunk($documentIds, 5);

        foreach ($documentBatches as $batchIndex => $batch) {
            foreach ($batch as $documentId) {
                try {
                    // Update document status to pending
                    $batchStatus = Cache::get($cacheKey);
                    $batchStatus['pending']--;
                    $batchStatus['processing']++;
                    $batchStatus['details'][$documentId] = [
                        'status' => 'processing',
                        'start_time' => now()->timestamp,
                    ];
                    Cache::put($cacheKey, $batchStatus, 24 * 3600);

                    // Get document
                    $document = KnowledgeDocument::find($documentId);

                    if (!$document) {
                        $this->updateBatchStatus($cacheKey, $documentId, 'error', 'Document not found');
                        continue;
                    }

                    // Generate embeddings
                    $success = $this->generateEmbeddings($document, $provider, $options);

                    if ($success) {
                        $this->updateBatchStatus($cacheKey, $documentId, 'success', null, [
                            'chunks' => $document->embeddings_count,
                            'provider' => $document->embeddings_provider,
                            'model' => $document->embeddings_model,
                        ]);
                    } else {
                        // Get error message from document metadata
                        $errorMessage = 'Failed to generate embeddings';
                        if (isset($document->metadata['last_error'])) {
                            $errorMessage = $document->metadata['last_error'];
                        }

                        $this->updateBatchStatus($cacheKey, $documentId, 'error', $errorMessage);
                    }
                } catch (Throwable $e) {
                    $this->updateBatchStatus($cacheKey, $documentId, 'error', $e->getMessage());
                    Log::error("Error in batch embedding for document {$documentId}: " . $e->getMessage());
                }
            }

            // Update estimated completion time after each batch
            $batchStatus = Cache::get($cacheKey);
            $elapsedTime = now()->timestamp - $batchStatus['start_time'];
            $processedCount = $batchStatus['successful'] + $batchStatus['failed'];

            if ($processedCount > 0 && $batchStatus['pending'] + $batchStatus['processing'] > 0) {
                $timePerDocument = $elapsedTime / $processedCount;
                $remainingDocuments = $batchStatus['pending'] + $batchStatus['processing'];
                $estimatedRemainingTime = $timePerDocument * $remainingDocuments;
                $batchStatus['estimated_completion'] = now()->addSeconds($estimatedRemainingTime)->timestamp;
                Cache::put($cacheKey, $batchStatus, 24 * 3600);
            }
        }

        // Get final status
        $finalStatus = Cache::get($cacheKey);
        $finalStatus['end_time'] = now()->timestamp;
        $finalStatus['duration'] = $finalStatus['end_time'] - $finalStatus['start_time'];
        $finalStatus['status'] = 'completed';
        Cache::put($cacheKey, $finalStatus, 24 * 3600);

        return [
            'batch_id' => $batchId,
            'total' => $finalStatus['total'],
            'successful' => $finalStatus['successful'],
            'failed' => $finalStatus['failed'],
            'duration' => $finalStatus['duration'],
            'details' => $finalStatus['details'],
        ];
    }

    /**
     * Update batch status in cache
     *
     * @param string $cacheKey
     * @param int $documentId
     * @param string $status
     * @param string|null $message
     * @param array $additionalData
     * @return void
     */
    protected function updateBatchStatus(string $cacheKey, int $documentId, string $status, ?string $message = null, array $additionalData = []): void
    {
        $batchStatus = Cache::get($cacheKey);

        if (!$batchStatus) {
            return;
        }

        // Update document status
        $batchStatus['processing']--;

        if ($status === 'success') {
            $batchStatus['successful']++;
        } else {
            $batchStatus['failed']++;
        }

        $batchStatus['details'][$documentId] = array_merge([
            'status' => $status,
            'end_time' => now()->timestamp,
            'duration' => now()->timestamp - ($batchStatus['details'][$documentId]['start_time'] ?? $batchStatus['start_time']),
        ], $additionalData);

        if ($message) {
            $batchStatus['details'][$documentId]['message'] = $message;
        }

        Cache::put($cacheKey, $batchStatus, 24 * 3600);
    }

    /**
     * Call the embedding API
     *
     * @param string $text
     * @param string $provider
     * @param array $options
     * @return array|null
     * @throws Exception
     */
    protected function callEmbeddingAPI(string $text, string $provider, array $options = []): ?array
    {
        $config = $this->embeddingModels[$provider] ?? null;

        if (!$config) {
            throw new Exception("Unknown embedding provider: {$provider}");
        }

        $apiKey = $options['api_key'] ?? env(strtoupper($provider) . '_API_KEY');

        if (!$apiKey) {
            throw new Exception("No API key found for provider: {$provider}");
        }

        // Check if text is empty
        if (empty(trim($text))) {
            Log::warning("Empty text provided for embedding generation");
            return [];
        }

        // Check if text exceeds maximum token limit
        $maxTokens = $config['max_tokens'];
        $estimatedTokens = intval(str_word_count($text) * 1.3); // Rough estimate

        if ($estimatedTokens > $maxTokens) {
            Log::warning("Text likely exceeds maximum token limit ({$estimatedTokens} > {$maxTokens})");
            // Truncate text to avoid API errors
            $text = substr($text, 0, intval($maxTokens / $estimatedTokens * strlen($text)));
        }

        // Retry logic with exponential backoff
        $maxRetries = $options['max_retries'] ?? 5;
        $retryDelay = $options['retry_delay'] ?? 1000; // milliseconds
        $timeout = $options['timeout'] ?? 60; // seconds

        // Rate limiting tracking
        $rateLimitKey = "rate_limit_{$provider}";
        $rateLimitResetKey = "rate_limit_reset_{$provider}";

        // Check if we're currently rate limited
        if (Cache::has($rateLimitKey) && Cache::get($rateLimitKey) === true) {
            $resetTime = Cache::get($rateLimitResetKey, 0);
            $currentTime = now()->timestamp;

            if ($resetTime > $currentTime) {
                $waitTime = $resetTime - $currentTime;
                Log::info("Rate limit in effect for {$provider}. Waiting {$waitTime} seconds.");

                if ($waitTime > 30) {
                    throw new Exception("Rate limit exceeded for {$provider}. Try again later.");
                }

                // Wait for rate limit to reset
                sleep(min($waitTime, 10)); // Wait at most 10 seconds
            } else {
                // Reset has passed
                Cache::forget($rateLimitKey);
                Cache::forget($rateLimitResetKey);
            }
        }

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                if ($provider === 'openai') {
                    $response = Http::timeout($timeout)
                        ->withHeaders([
                            'Authorization' => "Bearer {$apiKey}",
                            'Content-Type' => 'application/json',
                            'OpenAI-Organization' => $options['organization_id'] ?? env('OPENAI_ORGANIZATION_ID', ''),
                        ])
                        ->post($config['endpoint'], [
                            'input' => $text,
                            'model' => $options['model'] ?? $config['model'],
                            'dimensions' => $options['dimensions'] ?? null,
                        ]);

                    if ($response->successful()) {
                        $data = $response->json();
                        return $data['data'][0]['embedding'] ?? null;
                    }

                    // Handle rate limiting
                    if ($response->status() === 429) {
                        $retryAfter = $response->header('Retry-After', 60);
                        $resetTime = now()->addSeconds((int)$retryAfter)->timestamp;

                        // Store rate limit info in cache
                        Cache::put($rateLimitKey, true, $retryAfter);
                        Cache::put($rateLimitResetKey, $resetTime, $retryAfter + 10);

                        Log::warning("OpenAI rate limit exceeded. Retry after {$retryAfter} seconds.");

                        if ($attempt < $maxRetries) {
                            sleep(min((int)$retryAfter, 30)); // Wait at most 30 seconds
                            continue;
                        } else {
                            throw new Exception("Rate limit exceeded after {$maxRetries} attempts");
                        }
                    }

                    // Handle other errors
                    $errorData = $response->json();
                    $errorMessage = $errorData['error']['message'] ?? "API error: {$response->status()}";
                    throw new Exception($errorMessage);

                } elseif ($provider === 'huggingface') {
                    $response = Http::timeout($timeout)
                        ->withHeaders([
                            'Authorization' => "Bearer {$apiKey}",
                            'Content-Type' => 'application/json',
                        ])
                        ->post($config['endpoint'] . '/' . ($options['model'] ?? $config['model']), [
                            'inputs' => $text,
                        ]);

                    if ($response->successful()) {
                        return $response->json();
                    }

                    // Handle rate limiting
                    if ($response->status() === 429) {
                        $retryAfter = $response->header('Retry-After', 60);
                        $resetTime = now()->addSeconds((int)$retryAfter)->timestamp;

                        // Store rate limit info in cache
                        Cache::put($rateLimitKey, true, $retryAfter);
                        Cache::put($rateLimitResetKey, $resetTime, $retryAfter + 10);

                        Log::warning("Hugging Face rate limit exceeded. Retry after {$retryAfter} seconds.");

                        if ($attempt < $maxRetries) {
                            sleep(min((int)$retryAfter, 30)); // Wait at most 30 seconds
                            continue;
                        } else {
                            throw new Exception("Rate limit exceeded after {$maxRetries} attempts");
                        }
                    }

                    // Handle other errors
                    $errorData = $response->json();
                    $errorMessage = $errorData['error'] ?? "API error: {$response->status()}";
                    throw new Exception($errorMessage);
                } else {
                    throw new Exception("Unsupported provider: {$provider}");
                }
            } catch (Throwable $e) {
                Log::warning("Embedding API request exception (attempt {$attempt}/{$maxRetries}): " . $e->getMessage());

                if ($attempt < $maxRetries) {
                    // Exponential backoff with jitter
                    $jitter = rand(0, 1000) / 1000; // Random value between 0 and 1
                    $sleepTime = ($retryDelay * (2 ** ($attempt - 1))) * (1 + $jitter);
                    usleep(min($sleepTime, 30000) * 1000); // Cap at 30 seconds, convert to microseconds
                } else {
                    throw new Exception("Failed to generate embedding after {$maxRetries} attempts: " . $e->getMessage());
                }
            }
        }

        throw new Exception("Failed to generate embedding after {$maxRetries} attempts");
    }

    /**
     * Get embedding status for a document
     *
     * @param int $documentId
     * @return array
     */
    public function getEmbeddingStatus(int $documentId): array
    {
        $document = KnowledgeDocument::find($documentId);

        if (!$document) {
            return [
                'status' => 'error',
                'message' => 'Document not found',
            ];
        }

        $cacheKey = "embedding_status_doc_{$documentId}";
        $cachedStatus = Cache::get($cacheKey);

        if ($cachedStatus) {
            return $cachedStatus;
        }

        // If no cached status, return document status
        return [
            'status' => $document->status,
            'has_embeddings' => $document->has_embeddings,
            'embeddings_count' => $document->embeddings_count,
            'embeddings_provider' => $document->embeddings_provider,
            'embeddings_model' => $document->embeddings_model,
            'embeddings_generated_at' => $document->embeddings_generated_at ? $document->embeddings_generated_at->toIso8601String() : null,
            'progress' => $document->embeddings_progress ?? 0,
            'error' => $document->metadata['last_error'] ?? null,
        ];
    }

    /**
     * Chunk text into smaller pieces
     *
     * @param string $text
     * @param int $chunkSize
     * @param int $overlap
     * @return array
     */
    protected function chunkText(string $text, int $chunkSize = 1000, int $overlap = 200): array
    {
        // Simple chunking by character count
        $chunks = [];
        $textLength = strlen($text);

        if ($textLength <= $chunkSize) {
            return [$text];
        }

        $start = 0;
        while ($start < $textLength) {
            $end = min($start + $chunkSize, $textLength);

            // Try to end at a sentence or paragraph boundary
            if ($end < $textLength) {
                $possibleBoundaries = [
                    strrpos(substr($text, $start, $end - $start), "\n\n"),
                    strrpos(substr($text, $start, $end - $start), ".\n"),
                    strrpos(substr($text, $start, $end - $start), ". "),
                    strrpos(substr($text, $start, $end - $start), ".\n"),
                ];

                foreach ($possibleBoundaries as $boundary) {
                    if ($boundary !== false && $boundary > ($chunkSize * 0.5)) {
                        $end = $start + $boundary + 1;
                        break;
                    }
                }
            }

            $chunks[] = substr($text, $start, $end - $start);
            $start = $end - $overlap;
        }

        return $chunks;
    }

    /**
     * Get available embedding models
     *
     * @return array
     */
    public function getAvailableModels(): array
    {
        return $this->embeddingModels;
    }

    /**
     * Search for similar documents using vector similarity
     *
     * @param string $query
     * @param array $filters
     * @param int $limit
     * @param float $threshold
     * @param string $provider
     * @param array $options
     * @return array
     */
    public function searchSimilarDocuments(string $query, array $filters = [], int $limit = 5, float $threshold = 0.7, string $provider = 'openai', array $options = []): array
    {
        $cacheKey = 'search_' . md5($query . json_encode($filters) . $limit . $threshold . $provider);
        $cacheTtl = $options['cache_ttl'] ?? 300; // 5 minutes default

        // Check cache first if caching is enabled
        if (($options['use_cache'] ?? true) && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Validate inputs
            if (empty(trim($query))) {
                return [
                    'results' => [],
                    'metadata' => [
                        'status' => 'error',
                        'message' => 'Query cannot be empty',
                    ],
                ];
            }

            // Generate embedding for the query
            $startTime = microtime(true);
            $queryEmbedding = $this->callEmbeddingAPI($query, $provider, $options);
            $embeddingTime = microtime(true) - $startTime;

            if (!$queryEmbedding) {
                Log::error("Failed to generate embedding for query: {$query}");
                return [
                    'results' => [],
                    'metadata' => [
                        'status' => 'error',
                        'message' => 'Failed to generate embedding for query',
                    ],
                ];
            }

            // Pagination parameters
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? $limit;
            $offset = ($page - 1) * $perPage;

            // Convert to JSON for database comparison
            $queryEmbeddingJson = json_encode($queryEmbedding);
            $searchStartTime = microtime(true);

            // Build the query
            $dbQuery = \App\Models\KnowledgeEmbedding::select([
                'knowledge_embeddings.id as embedding_id',
                'knowledge_embeddings.chunk_text',
                'knowledge_embeddings.chunk_index',
                'knowledge_embeddings.document_id',
                'knowledge_documents.file_name',
                'knowledge_documents.file_type',
                'knowledge_documents.category',
                'knowledge_documents.source_id',
                'knowledge_documents.project_id',
                \DB::raw("(1 - (knowledge_embeddings.embedding <=> '{$queryEmbeddingJson}')) as similarity")
            ])
            ->join('knowledge_documents', 'knowledge_embeddings.document_id', '=', 'knowledge_documents.id')
            ->where('knowledge_documents.is_active_source', true);

            // Apply filters
            if (!empty($filters['project_id'])) {
                $dbQuery->where('knowledge_documents.project_id', $filters['project_id']);
            }

            if (!empty($filters['category'])) {
                if (is_array($filters['category'])) {
                    $dbQuery->whereIn('knowledge_documents.category', $filters['category']);
                } else {
                    $dbQuery->where('knowledge_documents.category', $filters['category']);
                }
            }

            if (!empty($filters['source_id'])) {
                if (is_array($filters['source_id'])) {
                    $dbQuery->whereIn('knowledge_documents.source_id', $filters['source_id']);
                } else {
                    $dbQuery->where('knowledge_documents.source_id', $filters['source_id']);
                }
            }

            if (!empty($filters['file_type'])) {
                if (is_array($filters['file_type'])) {
                    $dbQuery->whereIn('knowledge_documents.file_type', $filters['file_type']);
                } else {
                    $dbQuery->where('knowledge_documents.file_type', $filters['file_type']);
                }
            }

            // Text search in chunk content if provided
            if (!empty($filters['text_search'])) {
                $searchTerm = $filters['text_search'];
                $dbQuery->where('knowledge_embeddings.chunk_text', 'like', "%{$searchTerm}%");
            }

            // Apply similarity threshold filter
            $dbQuery->where(\DB::raw("(1 - (knowledge_embeddings.embedding <=> '{$queryEmbeddingJson}'))"), '>=', $threshold);

            // Count total results for pagination
            $countQuery = clone $dbQuery;
            $totalResults = $countQuery->count();

            // Apply pagination
            $results = $dbQuery->orderByDesc('similarity')
                ->skip($offset)
                ->take($perPage)
                ->get();

            $searchTime = microtime(true) - $searchStartTime;

            // Format results
            $formattedResults = $results->map(function ($result) {
                return [
                    'document_id' => $result->document_id,
                    'file_name' => $result->file_name,
                    'file_type' => $result->file_type,
                    'category' => $result->category,
                    'source_id' => $result->source_id,
                    'project_id' => $result->project_id,
                    'embedding_id' => $result->embedding_id,
                    'chunk_index' => $result->chunk_index,
                    'chunk_text' => $result->chunk_text,
                    'similarity' => round($result->similarity * 100, 2), // Convert to percentage
                ];
            })->toArray();

            // Prepare response with pagination metadata
            $response = [
                'results' => $formattedResults,
                'metadata' => [
                    'total' => $totalResults,
                    'page' => $page,
                    'per_page' => $perPage,
                    'pages' => ceil($totalResults / $perPage),
                    'query' => $query,
                    'threshold' => $threshold,
                    'provider' => $provider,
                    'timing' => [
                        'embedding_generation' => round($embeddingTime * 1000, 2), // ms
                        'search' => round($searchTime * 1000, 2), // ms
                        'total' => round((microtime(true) - $startTime) * 1000, 2), // ms
                    ],
                ],
            ];

            // Cache the results if caching is enabled
            if ($options['use_cache'] ?? true) {
                Cache::put($cacheKey, $response, $cacheTtl);
            }

            return $response;
        } catch (Throwable $e) {
            Log::error("Error searching for similar documents: " . $e->getMessage());
            return [
                'results' => [],
                'metadata' => [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ],
            ];
        }
    }

    /**
     * Delete embeddings for a document
     *
     * @param int $documentId
     * @return bool
     */
    public function deleteEmbeddings(int $documentId): bool
    {
        try {
            $count = \App\Models\KnowledgeEmbedding::where('document_id', $documentId)->delete();

            // Update document status
            $document = KnowledgeDocument::find($documentId);
            if ($document) {
                $document->update([
                    'has_embeddings' => false,
                    'embeddings_count' => 0,
                    'embeddings_provider' => null,
                    'embeddings_model' => null,
                    'embeddings_generated_at' => null,
                    'embeddings_progress' => 0
                ]);
            }

            return $count > 0;
        } catch (Exception $e) {
            Log::error("Error deleting embeddings for document {$documentId}: " . $e->getMessage());
            return false;
        }
    }
}
