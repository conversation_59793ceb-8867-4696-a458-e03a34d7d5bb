import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import {
    Upload, FileText, Download, Trash2, Eye, Search,
    RefreshCw, Grid, List, SlidersHorizontal, Database
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Progress } from '@/components/ui/progress'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { EmbeddingStatus } from '@/components/knowledge-base/embedding-status'
import { BatchEmbeddingButton } from '@/components/knowledge-base/batch-embedding-button'
import NoDocumentsPlaceholder from '@/components/knowledge-base/no-documents-placeholder'
import { AIModelSelector } from '@/components/ai-models/ai-model-selector'

export default function DocumentsTab({
    projects,
    selectedProjectId,
    setSelectedProjectId,
    refreshTrigger,
    onRefresh
}) {
    // State
    const [documents, setDocuments] = useState([])
    const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
    const [files, setFiles] = useState([])
    const [fileFilter, setFileFilter] = useState('')
    const [sortOrder, setSortOrder] = useState('desc')
    const [selectedModel, setSelectedModel] = useState(null)
    const [isActiveSource, setIsActiveSource] = useState(true)
    const [category, setCategory] = useState('general')
    const [uploadProgress, setUploadProgress] = useState({})
    const [selectedFiles, setSelectedFiles] = useState([])
    const [selectedDocument, setSelectedDocument] = useState(null)
    const [previewOpen, setPreviewOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false)

    // Fetch documents for selected project
    useEffect(() => {
        const fetchDocuments = async () => {
            if (!selectedProjectId) return

            setIsLoading(true)
            try {
                const response = await knowledgeBaseService.getDocuments(selectedProjectId)
                if (Array.isArray(response.data)) {
                    setDocuments(response.data)
                } else {
                    setDocuments([])
                }
            } catch (error) {
                console.error('Failed to load documents:', error)
                toast.error('Failed to load documents')
            } finally {
                setIsLoading(false)
            }
        }

        fetchDocuments()
    }, [selectedProjectId, refreshTrigger])

    // File upload handlers
    const handleFileSelect = useCallback((e) => {
        if (e.target.files) {
            setFiles(Array.from(e.target.files))
        }
    }, [])

    const handleDragOver = useCallback((e) => {
        e.preventDefault()
        e.stopPropagation()
    }, [])

    const handleDrop = useCallback((e) => {
        e.preventDefault()
        e.stopPropagation()

        if (e.dataTransfer.files) {
            setFiles(Array.from(e.dataTransfer.files))
        }
    }, [])

    const handleUpload = useCallback(async (): Promise<void> => {
        if (files.length === 0) {
            toast.error('Please select files to upload');
            return;
        }

        if (!selectedProjectId) {
            toast.error('Please select a project');
            return;
        }

        // Fetch CSRF token before upload
        try {
            await import('@/utils/authService').then(auth => auth.getCsrfToken());
        } catch (error) {
            console.error('Failed to fetch CSRF token:', error);
            toast.error('Authentication error. Please try again.');
            return;
        }

        // Create a source if needed
        let sourceId = 1 // Default source ID
        try {
            const sourceResponse = await knowledgeBaseService.createSource({
                type: 'file',
                name: `Files for project ${selectedProjectId}`,
                description: 'Automatically created for file uploads',
                status: 'active',
                project_id: selectedProjectId // Add project_id to the source creation
            })

            if (sourceResponse.data?.id) {
                sourceId = sourceResponse.data.id
            }
        } catch (error) {
            console.error('Failed to create source:', error)
        }

        // Upload each file
        const uploadPromises = files.map(async (file) => {
            try {
                return new Promise<void>((resolve, reject) => {
                    const xhr = new XMLHttpRequest()

                    // Set up progress tracking
                    xhr.upload.addEventListener('progress', (event) => {
                        if (event.lengthComputable) {
                            const progress = Math.round((event.loaded / event.total) * 100)
                            setUploadProgress(prev => ({ ...prev, [file.name]: progress }))
                        }
                    })

                    // Create form data
                    const formData = new FormData()
                    formData.append('file', file)
                    formData.append('category', category)
                    formData.append('project_id', String(selectedProjectId))
                    formData.append('is_active_source', isActiveSource ? '1' : '0')
                    formData.append('source_id', String(sourceId))
                    if (selectedModel) {
                        formData.append('model_id', String(selectedModel))
                    }

                    // Set up request
                    xhr.open('POST', '/api/knowledge-base/upload', true)
                    xhr.withCredentials = true

                    // Get CSRF token from cookie
                    const csrfToken = document.cookie
                        .split('; ')
                        .find(row => row.startsWith('XSRF-TOKEN='))
                        ?.split('=')[1];

                    if (csrfToken) {
                        xhr.setRequestHeader('X-XSRF-TOKEN', decodeURIComponent(csrfToken));
                    }

                    xhr.onload = () => {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            resolve()
                        } else {
                            reject(new Error(`Upload failed with status ${xhr.status}`))
                        }
                    }

                    xhr.onerror = () => {
                        reject(new Error('Network error during upload'))
                    }

                    xhr.send(formData)
                })
            } catch (error) {
                console.error(`Error uploading ${file.name}:`, error)
                toast.error(`Failed to upload ${file.name}`)
                setUploadProgress(prev => ({ ...prev, [file.name]: 0 }))
                throw error
            }
        })

        try {
            await Promise.all(uploadPromises)
            setFiles([])
            setUploadProgress({})
            onRefresh()
            toast.success(`${files.length} file(s) uploaded successfully`)
        } catch (error) {
            console.error('Some uploads failed:', error)
            toast.error('Some uploads failed')
        }
    }, [files, selectedProjectId, category, isActiveSource, selectedModel, onRefresh])

    // Toggle AI source
    const handleToggleActive = useCallback((id, active) => {
        knowledgeBaseService.toggleDocumentAsAISource(id, active)
            .then(() => {
                setDocuments(documents.map(doc =>
                    doc.id === id ? { ...doc, is_active_source: active } : doc
                ))
                setSelectedFiles(prev =>
                    prev.map(f => f.id === id ? { ...f, is_active_source: active } : f)
                )
                toast.success('AI source updated')
            })
            .catch((error) => {
                console.error('Error toggling AI source:', error)
                toast.error('Update failed')
            })
    }, [documents])

    // Delete document
    const handleDeleteDocument = useCallback((id) => {
        if (confirm('Are you sure you want to delete this document?')) {
            knowledgeBaseService.deleteDocument(id)
                .then(response => {
                    if (response.data?.success) {
                        setSelectedFiles(prev => prev.filter(f => f.id !== id))
                        onRefresh()
                        toast.success('Document deleted')
                    } else {
                        toast.error(response.data?.message || 'Delete failed')
                    }
                })
                .catch((error) => {
                    console.error('Error deleting document:', error)
                    toast.error('Delete failed')
                })
        }
    }, [onRefresh])

    // Filtered and sorted documents
    const filteredDocuments = useCallback(() => {
        return documents
            .filter(doc =>
                !fileFilter ? true : (
                    doc.file_name?.toLowerCase().includes(fileFilter.toLowerCase()) ||
                    (doc.category || '').toLowerCase().includes(fileFilter.toLowerCase()) ||
                    (doc.file_type || '').toLowerCase().includes(fileFilter.toLowerCase())
                )
            )
            .sort((a, b) => {
                const dateA = new Date(a.created_at || 0).getTime()
                const dateB = new Date(b.created_at || 0).getTime()
                return sortOrder === 'asc' ? dateA - dateB : dateB - dateA
            })
    }, [documents, fileFilter, sortOrder])

    return (
        <div className="space-y-6">
            {/* Project selector and controls */}
            <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                    <ProjectSelector
                        projects={projects}
                        selectedProjectId={selectedProjectId}
                        setSelectedProjectId={setSelectedProjectId}
                    />

                    <Button
                        variant="outline"
                        size="icon"
                        onClick={onRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                </div>

                <div className="flex items-center gap-3">
                    <div className="relative">
                        <Input
                            placeholder="Filter documents..."
                            value={fileFilter}
                            onChange={e => setFileFilter(e.target.value)}
                            className="pl-8 w-[200px]"
                        />
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    </div>

                    <Select
                        value={sortOrder}
                        onValueChange={setSortOrder}
                    >
                        <SelectTrigger className="w-[130px]">
                            <SelectValue placeholder="Sort order" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="desc">Newest first</SelectItem>
                            <SelectItem value="asc">Oldest first</SelectItem>
                        </SelectContent>
                    </Select>

                    <div className="flex items-center gap-2 border rounded-md p-1">
                        <Button
                            variant={viewMode === 'grid' ? 'default' : 'ghost'}
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => setViewMode('grid')}
                        >
                            <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'list' ? 'default' : 'ghost'}
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => setViewMode('list')}
                        >
                            <List className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>

            {/* Main content area */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Upload panel */}
                <Card className="md:col-span-1">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-lg">
                            <Upload className="h-5 w-5" />
                            Upload Documents
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div
                            className="border-2 border-dashed rounded-md p-6 mb-4 text-center hover:bg-muted/50 transition cursor-pointer"
                            onDragOver={handleDragOver}
                            onDrop={handleDrop}
                            onClick={() => document.getElementById('file-input')?.click()}
                        >
                            <input
                                type="file"
                                id="file-input"
                                className="hidden"
                                onChange={handleFileSelect}
                                multiple
                            />
                            <Upload className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                            <p className="text-sm font-medium">Drag and drop files here or click to browse</p>
                            <p className="text-xs text-muted-foreground mt-1">
                                Supported formats: PDF, DOCX, XLSX, TXT, CSV, JSON
                            </p>
                        </div>

                        <div className="space-y-4 mt-4">
                            <div className="space-y-2">
                                <Label htmlFor="category-input">Category</Label>
                                <Input
                                    id="category-input"
                                    value={category}
                                    onChange={e => setCategory(e.target.value)}
                                    placeholder="General"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="ai-model-selector">Processing Model (Optional)</Label>
                                <AIModelSelector
                                    id="ai-model-selector"
                                    selectedModelId={selectedModel}
                                    onModelChange={(value) => {
                                        console.log("Model changed:", value);
                                        setSelectedModel(value);
                                    }}
                                    placeholder="Select AI model"
                                    isOptional={true}
                                    className="w-full"
                                />
                                <p className="text-xs text-muted-foreground">
                                    AI model will be used for processing and embedding
                                </p>
                            </div>

                            <div className="flex items-center gap-2">
                                <Checkbox
                                    id="ai-source-checkbox"
                                    checked={isActiveSource}
                                    onCheckedChange={v => setIsActiveSource(!!v)}
                                />
                                <Label htmlFor="ai-source-checkbox" className="cursor-pointer">
                                    Set as AI Source
                                </Label>
                            </div>

                            <Button
                                onClick={handleUpload}
                                className="w-full"
                                disabled={files.length === 0 || !selectedProjectId}
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                Upload {files.length > 0 ? `(${files.length} file${files.length > 1 ? 's' : ''})` : ''}
                            </Button>
                        </div>

                        {/* Selected Files */}
                        {files.length > 0 && (
                            <div className="mt-4 border rounded-md divide-y">
                                {files.map((file, index) => (
                                    <div key={index} className="flex items-center justify-between p-2 text-sm">
                                        <div className="flex items-center gap-2 truncate">
                                            <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                            <span className="truncate">{file.name}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="text-xs text-muted-foreground">
                                                {(file.size / 1024).toFixed(1)} KB
                                            </span>
                                            {uploadProgress[file.name] > 0 && uploadProgress[file.name] < 100 && (
                                                <Progress value={uploadProgress[file.name]} className="w-16 h-2" />
                                            )}
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="h-6 w-6 text-muted-foreground hover:text-destructive"
                                                onClick={() => setFiles(files.filter((_, i) => i !== index))}
                                            >
                                                <Trash2 className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Document gallery/list */}
                <Card className="md:col-span-2">
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-lg">
                            Document Library
                        </CardTitle>

                        {selectedFiles.length > 0 && (
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-muted-foreground">
                                    {selectedFiles.length} selected
                                </span>
                                <BatchEmbeddingButton
                                    selectedFiles={selectedFiles}
                                    onComplete={() => {
                                        onRefresh()
                                        setSelectedFiles([])
                                    }}
                                />
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setSelectedFiles([])}
                                >
                                    Clear
                                </Button>
                            </div>
                        )}
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="h-[400px] flex items-center justify-center">
                                <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                            </div>
                        ) : documents.length === 0 ? (
                            <NoDocumentsPlaceholder />
                        ) : filteredDocuments().length === 0 ? (
                            <div className="h-[400px] flex items-center justify-center flex-col">
                                <Search className="h-12 w-12 text-muted-foreground mb-4" />
                                <p className="text-muted-foreground">No documents match your filter</p>
                            </div>
                        ) : viewMode === 'grid' ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {filteredDocuments().map(doc => (
                                    <div
                                        key={doc.id}
                                        className={`
                      group relative border rounded-lg overflow-hidden hover:shadow-md transition-all
                      ${selectedFiles.some(f => f.id === doc.id) ? 'ring-2 ring-primary' : ''}
                    `}
                                        onClick={() => {
                                            if (selectedFiles.some(f => f.id === doc.id)) {
                                                setSelectedFiles(selectedFiles.filter(f => f.id !== doc.id))
                                            } else {
                                                setSelectedFiles([...selectedFiles, doc])
                                            }
                                        }}
                                    >
                                        <div className="aspect-square bg-muted/30 flex items-center justify-center">
                                            <FileText className="h-16 w-16 text-muted-foreground" />
                                        </div>
                                        <div className="p-3">
                                            <div className="flex items-center justify-between mb-1">
                                                <h3 className="font-medium text-sm truncate">{doc.file_name}</h3>
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-2">
                                                {new Date(doc.created_at).toLocaleDateString()}
                                            </div>
                                            <div className="flex flex-wrap gap-2">
                                                <Badge variant="outline" className="text-xs">
                                                    {doc.file_type}
                                                </Badge>
                                                {doc.category && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        {doc.category}
                                                    </Badge>
                                                )}
                                            </div>
                                            <div className="mt-2">
                                                <EmbeddingStatus
                                                    hasEmbeddings={!!doc.has_embeddings}
                                                    embeddingsCount={doc.embeddings_count}
                                                    embeddingsProvider={doc.embeddings_provider}
                                                    embeddingsModel={doc.embeddings_model}
                                                    size="sm"
                                                />
                                            </div>
                                        </div>
                                        <div className="absolute top-2 right-2 flex gap-1">
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            size="icon"
                                                            variant="ghost"
                                                            className="h-7 w-7 opacity-0 group-hover:opacity-100 bg-background/80"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleToggleActive(doc.id, !doc.is_active_source);
                                                            }}
                                                        >
                                                            <Database className={`h-4 w-4 ${doc.is_active_source ? 'text-primary' : 'text-muted-foreground'}`} />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        {doc.is_active_source ? 'Active AI Source' : 'Set as AI Source'}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7 opacity-0 group-hover:opacity-100 bg-background/80"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setSelectedDocument(doc);
                                                    setPreviewOpen(true);
                                                }}
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7 opacity-0 group-hover:opacity-100 bg-background/80"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteDocument(doc.id);
                                                }}
                                            >
                                                <Trash2 className="h-4 w-4 text-destructive" />
                                            </Button>
                                        </div>

                                        <div className="absolute top-3 left-3">
                                            <Checkbox
                                                checked={selectedFiles.some(f => f.id === doc.id)}
                                                className="h-5 w-5 bg-background/80"
                                                onClick={(e) => e.stopPropagation()}
                                                onCheckedChange={(checked) => {
                                                    if (checked) {
                                                        setSelectedFiles([...selectedFiles, doc])
                                                    } else {
                                                        setSelectedFiles(selectedFiles.filter(f => f.id !== doc.id))
                                                    }
                                                }}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="divide-y border rounded-md">
                                {filteredDocuments().map(doc => (
                                    <div
                                        key={doc.id}
                                        className={`
                      flex items-center justify-between p-3 hover:bg-muted/50 transition cursor-pointer
                      ${selectedFiles.some(f => f.id === doc.id) ? 'bg-primary/10 border-l-4 border-primary' : ''}
                    `}
                                    >
                                        <div className="flex items-center gap-3">
                                            <Checkbox
                                                checked={selectedFiles.some(f => f.id === doc.id)}
                                                className="mr-2"
                                                onCheckedChange={(checked) => {
                                                    if (checked) {
                                                        setSelectedFiles([...selectedFiles, doc])
                                                    } else {
                                                        setSelectedFiles(selectedFiles.filter(f => f.id !== doc.id))
                                                    }
                                                }}
                                            />
                                            <FileText className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium text-sm truncate max-w-[200px]">{doc.file_name}</span>
                                            <Badge variant="outline" className="text-xs">{doc.file_type}</Badge>
                                            {doc.category && (
                                                <Badge variant="secondary" className="text-xs">{doc.category}</Badge>
                                            )}
                                        </div>

                                        <div className="flex items-center gap-3">
                                            <span className="text-xs text-muted-foreground">
                                                {new Date(doc.created_at).toLocaleDateString()}
                                            </span>

                                            <EmbeddingStatus
                                                hasEmbeddings={!!doc.has_embeddings}
                                                embeddingsCount={doc.embeddings_count}
                                                embeddingsProvider={doc.embeddings_provider}
                                                embeddingsModel={doc.embeddings_model}
                                                size="sm"
                                            />

                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            size="icon"
                                                            variant="ghost"
                                                            className="h-7 w-7"
                                                            onClick={() => handleToggleActive(doc.id, !doc.is_active_source)}
                                                        >
                                                            <Database className={`h-4 w-4 ${doc.is_active_source ? 'text-primary' : 'text-muted-foreground'}`} />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        {doc.is_active_source ? 'Active AI Source' : 'Set as AI Source'}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7"
                                                onClick={() => {
                                                    setSelectedDocument(doc)
                                                    setPreviewOpen(true)
                                                }}
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>

                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-7 w-7"
                                                onClick={() => handleDeleteDocument(doc.id)}
                                            >
                                                <Trash2 className="h-4 w-4 text-destructive" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Document preview would go here - could be implemented as a modal */}
            {selectedDocument && previewOpen && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    {/* Preview modal content */}
                </div>
            )}
        </div>
    )
}