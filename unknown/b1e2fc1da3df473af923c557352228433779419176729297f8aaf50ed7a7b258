import { useState } from 'react'
import { toast } from 'sonner'
import { Loader2, AlertCircle, Info } from 'lucide-react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { cn } from '@/lib/utils'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ProjectCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProjectCreated: (project: any) => void
  trigger?: React.ReactNode
}

export function ProjectCreateDialog({
  open,
  onOpenChange,
  onProjectCreated,
  trigger
}: ProjectCreateDialogProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<{
    name?: string;
    description?: string;
    general?: string;
  }>({})

  const validateForm = (): boolean => {
    const newErrors: {
      name?: string;
      description?: string;
    } = {};

    // Validate name
    if (!name.trim()) {
      newErrors.name = 'Name is required';
    } else if (name.length > 255) {
      newErrors.name = 'Name must be less than 255 characters';
    }

    // Validate description (optional)
    if (description && description.length > 1000) {
      newErrors.description = 'Description must be less than 1000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateForm()) {
      return;
    }

    setIsLoading(true)

    try {
      const response = await knowledgeBaseService.createProject({
        name: name.trim(),
        description: description.trim()
      })

      if (response?.success) {
        toast.success('Knowledge base created successfully')
        onProjectCreated(response.data)
        onOpenChange(false)
        resetForm()
      } else {
        setErrors({ general: response?.message || 'Failed to create knowledge base' });
        toast.error(response?.message || 'Failed to create knowledge base')
      }
    } catch (error: any) {
      console.error('Error creating knowledge base:', error)

      // Check if it's an authentication error
      if (error.response?.status === 401) {
        toast.error('Authentication required. Please log in.')
        onOpenChange(false)
        // Redirect to login page after a short delay
        setTimeout(() => {
          window.location.href = '/login'
        }, 2000)
      } else {
        const errorMessage = error.response?.data?.message || error.message || 'Failed to create knowledge base';
        setErrors({ general: errorMessage });
        toast.error(errorMessage);
      }
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setName('')
    setDescription('')
    setErrors({})
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Knowledge Base</DialogTitle>
          <DialogDescription>
            Create a place to store information that your AI can access when answering questions.
          </DialogDescription>
        </DialogHeader>

        <Alert variant="outline" className="my-3">
          <Info className="h-4 w-4" />
          <AlertDescription>
            Your knowledge base helps your AI provide accurate, factual answers based on your own content.
          </AlertDescription>
        </Alert>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {errors.general && (
              <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                <span>{errors.general}</span>
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="name" className={errors.name ? "text-destructive" : ""}>
                Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                placeholder="My Company Knowledge"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  if (errors.name) {
                    setErrors({ ...errors, name: undefined });
                  }
                }}
                className={cn(
                  errors.name && "border-destructive focus-visible:ring-destructive"
                )}
                aria-invalid={!!errors.name}
              />
              {errors.name ? (
                <p className="text-destructive text-sm">{errors.name}</p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  A clear name that identifies what information this knowledge base contains
                </p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description" className={errors.description ? "text-destructive" : ""}>
                Description (Optional)
              </Label>
              <Textarea
                id="description"
                placeholder="This knowledge base contains our product information, FAQs, and support documentation."
                value={description}
                onChange={(e) => {
                  setDescription(e.target.value);
                  if (errors.description) {
                    setErrors({ ...errors, description: undefined });
                  }
                }}
                rows={3}
                className={cn(
                  errors.description && "border-destructive focus-visible:ring-destructive"
                )}
                aria-invalid={!!errors.description}
              />
              {errors.description ? (
                <p className="text-destructive text-sm">{errors.description}</p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Additional details about what information this knowledge base will contain
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                resetForm()
                onOpenChange(false)
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Knowledge Base
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
