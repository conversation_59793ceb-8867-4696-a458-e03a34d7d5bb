<?php

namespace App\Providers;

use App\Services\VectorEmbeddingService;
use Illuminate\Support\ServiceProvider;

class VectorEmbeddingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(VectorEmbeddingService::class, function ($app) {
            return new VectorEmbeddingService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
