/**
 * AI Configuration Service
 *
 * Service for managing AI model configurations and settings
 */

import api from '@/utils/api'
import { AiConfiguration, ModelProvider, ModelSettings, AIModelData, AvailableModelData } from '../types'

export const aiConfigurationService = {
  /**
   * Get AI configuration for a widget
   */
  getConfiguration: async (widgetId: number): Promise<AiConfiguration> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/ai-configuration`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching AI configuration:', error)
      throw error
    }
  },

  /**
   * Update AI configuration
   */
  updateConfiguration: async (widgetId: number, config: Partial<AiConfiguration>): Promise<AiConfiguration> => {
    try {
      const response = await api.put(`/widgets/${widgetId}/ai-configuration`, config)
      return response.data.data
    } catch (error) {
      console.error('Error updating AI configuration:', error)
      throw error
    }
  },

  /**
   * Get available model providers
   */
  getProviders: async (): Promise<ModelProvider[]> => {
    try {
      const response = await api.get('/ai/providers')
      return response.data.data
    } catch (error) {
      console.error('Error fetching model providers:', error)
      throw error
    }
  },

  /**
   * Get models for a specific provider
   */
  getProviderModels: async (providerId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/ai/providers/${providerId}/models`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching provider models:', error)
      throw error
    }
  },

  /**
   * Test AI model configuration
   */
  testConfiguration: async (config: ModelSettings): Promise<any> => {
    try {
      const response = await api.post('/ai/test-configuration', config)
      return response.data
    } catch (error) {
      console.error('Error testing AI configuration:', error)
      throw error
    }
  },

  /**
   * Get AI usage statistics
   */
  getUsageStats: async (widgetId: number): Promise<any> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/ai-usage`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching AI usage stats:', error)
      throw error
    }
  },

  /**
   * AI Model Management Functions
   */

  // Get all models
  getModels: async (): Promise<AIModelData[]> => {
    try {
      const response = await api.get('ai-models')
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data
      }
      if (Array.isArray(response.data)) {
        return response.data
      }
      return []
    } catch (error) {
      console.error('Error fetching AI models:', error)
      throw error
    }
  },

  // Create a new model
  createModel: async (modelData: AIModelData): Promise<AIModelData> => {
    try {
      const response = await api.post('ai-models', modelData)
      return response.data.data || response.data
    } catch (error) {
      console.error('Error creating AI model:', error)
      throw error
    }
  },

  // Update an existing model
  updateModel: async (id: number, modelData: Partial<AIModelData>): Promise<AIModelData> => {
    try {
      const response = await api.put(`ai-models/${id}`, modelData)
      return response.data.data || response.data
    } catch (error) {
      console.error('Error updating AI model:', error)
      throw error
    }
  },

  // Delete a model
  deleteModel: async (id: number): Promise<void> => {
    try {
      await api.delete(`ai-models/${id}`)
    } catch (error) {
      console.error('Error deleting AI model:', error)
      throw error
    }
  },

  // Test connection to a model
  testConnection: async (id: number): Promise<any> => {
    try {
      const response = await api.post(`ai-models/${id}/test-connection`)
      return response.data
    } catch (error) {
      console.error('Error testing AI model connection:', error)
      throw error
    }
  },

  // Test chat with a model
  testChat: async (id: number, message: string): Promise<any> => {
    try {
      const response = await api.post(`ai-models/${id}/test-chat`, { message })
      return response.data
    } catch (error) {
      console.error('Error testing chat with AI model:', error)
      throw error
    }
  },

  // Discover available models from provider
  discoverModels: async (id: number): Promise<any> => {
    try {
      const response = await api.post(`ai-models/${id}/discover-models`)
      return response.data
    } catch (error) {
      console.error('Error discovering models:', error)
      throw error
    }
  },

  // Get available models for a provider without creating
  discoverModelsWithoutCreating: async (provider: string, apiKey: string): Promise<any> => {
    try {
      const response = await api.post('ai-models/discover-models-direct', {
        provider,
        api_key: apiKey
      })
      return response.data
    } catch (error) {
      console.error('Error discovering models directly:', error)
      throw error
    }
  }
}
