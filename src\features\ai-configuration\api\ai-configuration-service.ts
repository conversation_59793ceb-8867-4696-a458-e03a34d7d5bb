/**
 * AI Configuration Service
 * 
 * Service for managing AI model configurations and settings
 */

import api from '@/utils/api'
import { AiConfiguration, ModelProvider, ModelSettings } from '../types'

export const aiConfigurationService = {
  /**
   * Get AI configuration for a widget
   */
  getConfiguration: async (widgetId: number): Promise<AiConfiguration> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/ai-configuration`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching AI configuration:', error)
      throw error
    }
  },

  /**
   * Update AI configuration
   */
  updateConfiguration: async (widgetId: number, config: Partial<AiConfiguration>): Promise<AiConfiguration> => {
    try {
      const response = await api.put(`/widgets/${widgetId}/ai-configuration`, config)
      return response.data.data
    } catch (error) {
      console.error('Error updating AI configuration:', error)
      throw error
    }
  },

  /**
   * Get available model providers
   */
  getProviders: async (): Promise<ModelProvider[]> => {
    try {
      const response = await api.get('/ai/providers')
      return response.data.data
    } catch (error) {
      console.error('Error fetching model providers:', error)
      throw error
    }
  },

  /**
   * Get models for a specific provider
   */
  getProviderModels: async (providerId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/ai/providers/${providerId}/models`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching provider models:', error)
      throw error
    }
  },

  /**
   * Test AI model configuration
   */
  testConfiguration: async (config: ModelSettings): Promise<any> => {
    try {
      const response = await api.post('/ai/test-configuration', config)
      return response.data
    } catch (error) {
      console.error('Error testing AI configuration:', error)
      throw error
    }
  },

  /**
   * Get AI usage statistics
   */
  getUsageStats: async (widgetId: number): Promise<any> => {
    try {
      const response = await api.get(`/widgets/${widgetId}/ai-usage`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching AI usage stats:', error)
      throw error
    }
  }
}
