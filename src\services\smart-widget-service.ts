/**
 * Smart Widget Service
 * 
 * Centralized service for smart widget builder functionality.
 * Handles API interactions, configuration management, and business logic
 * for widget features like webhooks, integrations, AI models, etc.
 */

import api from '@/utils/api';
import { widgetService } from '@/utils/widgetService';
import { toast } from 'sonner';

// Types for smart widget features
export interface WebhookConfig {
  url: string;
  method: 'POST' | 'GET' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  secret?: string;
  eventType: 'message_sent' | 'chat_start' | 'chat_end' | 'user_rating' | 'test';
}

export interface IntegrationConfig {
  id: string;
  type: string;
  name: string;
  url: string;
  active: boolean;
  events: string[];
  secret?: string;
  created_at: string;
}

export interface AIModelConfig {
  id: number;
  name: string;
  provider: string;
  model: string;
  active: boolean;
  fallback_models?: number[];
}

export interface PreChatFormConfig {
  enabled: boolean;
  title?: string;
  description?: string;
  buttonText?: string;
  required?: boolean;
  fields?: Array<{
    id: string;
    type: string;
    label: string;
    required: boolean;
    options?: string[];
  }>;
}

export interface PostChatSurveyConfig {
  enabled: boolean;
  title?: string;
  description?: string;
  questions?: Array<{
    id: string;
    type: string;
    question: string;
    required: boolean;
    options?: string[];
  }>;
}

export interface DomainRestrictionConfig {
  enabled: boolean;
  allowedDomains: string[];
  blockMode: 'whitelist' | 'blacklist';
}

export interface MobileOptimizationConfig {
  enabled: boolean;
  responsiveBreakpoint: number;
  mobilePosition: string;
  mobileSize: string;
  touchOptimized: boolean;
}

export interface ConversationPersistenceConfig {
  enabled: boolean;
  storageType: 'session' | 'local' | 'server';
  retentionDays: number;
  maxMessages: number;
}

export interface CustomCSSConfig {
  enabled: boolean;
  css: string;
  variables?: Record<string, string>;
}

export interface LogoUploadConfig {
  enabled: boolean;
  logoUrl?: string;
  logoFile?: File;
  position: 'header' | 'chat' | 'both';
  size: 'small' | 'medium' | 'large';
}

/**
 * Smart Widget Service Class
 */
class SmartWidgetService {
  /**
   * Webhook Management
   */
  async testWebhook(widgetId: number, config: WebhookConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/webhooks/test`, config);
      return response;
    } catch (error) {
      console.error('Webhook test failed:', error);
      throw error;
    }
  }

  async saveWebhookConfig(widgetId: number, config: WebhookConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/webhooks`, config);
      toast.success('Webhook configuration saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save webhook config:', error);
      toast.error('Failed to save webhook configuration');
      throw error;
    }
  }

  async getWebhookConfigs(widgetId: number) {
    try {
      const response = await api.get(`widgets/${widgetId}/webhooks`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch webhook configs:', error);
      throw error;
    }
  }

  /**
   * Integration Management
   */
  async saveIntegration(widgetId: number, integration: IntegrationConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/integrations`, integration);
      toast.success(`${integration.name} integration saved successfully`);
      return response;
    } catch (error) {
      console.error('Failed to save integration:', error);
      toast.error('Failed to save integration');
      throw error;
    }
  }

  async getIntegrations(widgetId: number) {
    try {
      const response = await api.get(`widgets/${widgetId}/integrations`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch integrations:', error);
      throw error;
    }
  }

  async deleteIntegration(widgetId: number, integrationId: string) {
    try {
      const response = await api.delete(`widgets/${widgetId}/integrations/${integrationId}`);
      toast.success('Integration deleted successfully');
      return response;
    } catch (error) {
      console.error('Failed to delete integration:', error);
      toast.error('Failed to delete integration');
      throw error;
    }
  }

  /**
   * AI Model Management
   */
  async getAvailableAIModels() {
    try {
      const response = await api.get('ai-models');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch AI models:', error);
      throw error;
    }
  }

  async saveAIModelConfig(widgetId: number, config: AIModelConfig) {
    try {
      const response = await api.put(`widgets/${widgetId}/ai-model`, config);
      toast.success('AI model configuration saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save AI model config:', error);
      toast.error('Failed to save AI model configuration');
      throw error;
    }
  }

  /**
   * Pre-Chat Form Management
   */
  async savePreChatForm(widgetId: number, config: PreChatFormConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/pre-chat-form`, config);
      toast.success('Pre-chat form saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save pre-chat form:', error);
      toast.error('Failed to save pre-chat form');
      throw error;
    }
  }

  async getPreChatForm(widgetId: number) {
    try {
      const response = await api.get(`widgets/${widgetId}/pre-chat-form`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch pre-chat form:', error);
      throw error;
    }
  }

  /**
   * Post-Chat Survey Management
   */
  async savePostChatSurvey(widgetId: number, config: PostChatSurveyConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/post-chat-survey`, config);
      toast.success('Post-chat survey saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save post-chat survey:', error);
      toast.error('Failed to save post-chat survey');
      throw error;
    }
  }

  async getPostChatSurvey(widgetId: number) {
    try {
      const response = await api.get(`widgets/${widgetId}/post-chat-survey`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch post-chat survey:', error);
      throw error;
    }
  }

  /**
   * Domain Restriction Management
   */
  async saveDomainRestrictions(widgetId: number, config: DomainRestrictionConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/domain-restrictions`, config);
      toast.success('Domain restrictions saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save domain restrictions:', error);
      toast.error('Failed to save domain restrictions');
      throw error;
    }
  }

  /**
   * Mobile Optimization Management
   */
  async saveMobileOptimization(widgetId: number, config: MobileOptimizationConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/mobile-optimization`, config);
      toast.success('Mobile optimization saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save mobile optimization:', error);
      toast.error('Failed to save mobile optimization');
      throw error;
    }
  }

  /**
   * Conversation Persistence Management
   */
  async saveConversationPersistence(widgetId: number, config: ConversationPersistenceConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/conversation-persistence`, config);
      toast.success('Conversation persistence saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save conversation persistence:', error);
      toast.error('Failed to save conversation persistence');
      throw error;
    }
  }

  /**
   * Custom CSS Management
   */
  async saveCustomCSS(widgetId: number, config: CustomCSSConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/custom-css`, config);
      toast.success('Custom CSS saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save custom CSS:', error);
      toast.error('Failed to save custom CSS');
      throw error;
    }
  }

  /**
   * Logo Upload Management
   */
  async uploadLogo(widgetId: number, file: File, config: LogoUploadConfig) {
    try {
      const formData = new FormData();
      formData.append('logo', file);
      formData.append('config', JSON.stringify(config));

      const response = await api.post(`widgets/${widgetId}/logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      toast.success('Logo uploaded successfully');
      return response;
    } catch (error) {
      console.error('Failed to upload logo:', error);
      toast.error('Failed to upload logo');
      throw error;
    }
  }

  async saveLogoFromUrl(widgetId: number, config: LogoUploadConfig) {
    try {
      const response = await api.post(`widgets/${widgetId}/logo-url`, config);
      toast.success('Logo saved successfully');
      return response;
    } catch (error) {
      console.error('Failed to save logo from URL:', error);
      toast.error('Failed to save logo');
      throw error;
    }
  }

  /**
   * Widget Template Management
   */
  async getWidgetTemplates() {
    try {
      const response = await api.get('widget-templates');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch widget templates:', error);
      throw error;
    }
  }

  async applyTemplate(widgetId: number, templateId: string) {
    try {
      const response = await api.post(`widgets/${widgetId}/apply-template`, { templateId });
      toast.success('Template applied successfully');
      return response;
    } catch (error) {
      console.error('Failed to apply template:', error);
      toast.error('Failed to apply template');
      throw error;
    }
  }
}

// Export singleton instance
export const smartWidgetService = new SmartWidgetService();
export default smartWidgetService;
