'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import { Integration } from '@/features/widget/types/integration'
import { getIntegrationTypeDetails } from '@/features/widget/utils/integration-utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { toast } from 'sonner'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface IntegrationCardProps {
    integration: Integration
    onEdit: (integration: Integration) => void
    onToggleActive: (id: string) => void
    onDelete: (id: string) => void
}

export function IntegrationCard({
    integration,
    onEdit,
    onToggleActive,
    onDelete
}: IntegrationCardProps) {
    const typeDetails = getIntegrationTypeDetails(integration.type);

    return (
        <Card key={integration.id} className={!integration.active ? "opacity-70" : ""}>
            <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                        <img
                            src={typeDetails.icon}
                            alt={typeDetails.name}
                            className="w-6 h-6"
                            onError={(e) => {
                                e.currentTarget.src = '/assets/integrations/webhook-icon.svg'
                            }}
                        />
                        <div>
                            <CardTitle className="text-base">{integration.name}</CardTitle>
                            <CardDescription className="text-xs">
                                {typeDetails.name}
                            </CardDescription>
                            <div className="flex items-center gap-1 mt-1">
                                {integration.active ? (
                                    <Badge variant="outline" className="text-[10px] h-4 px-1 bg-green-50 text-green-700 hover:bg-green-50">
                                        Active
                                    </Badge>
                                ) : (
                                    <Badge variant="outline" className="text-[10px] h-4 px-1 bg-muted hover:bg-muted">
                                        Inactive
                                    </Badge>
                                )}
                            </div>
                        </div>
                    </div>
                    <div>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0" title="Options">
                                    <span className="sr-only">Open menu</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                        <circle cx="12" cy="12" r="1" />
                                        <circle cx="12" cy="5" r="1" />
                                        <circle cx="12" cy="19" r="1" />
                                    </svg>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onEdit(integration)}>
                                    Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onToggleActive(integration.id)}>
                                    {integration.active ? "Disable" : "Enable"}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    className="text-destructive focus:text-destructive"
                                    onClick={() => onDelete(integration.id)}
                                >
                                    Delete
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="text-xs text-muted-foreground truncate" title={integration.url}>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span className="flex items-center gap-1 cursor-default">
                                    {integration.url.length > 40
                                        ? integration.url.substring(0, 40) + "..."
                                        : integration.url}
                                    <Copy className="h-3 w-3 cursor-pointer hover:text-primary" onClick={(e) => {
                                        e.stopPropagation();
                                        navigator.clipboard.writeText(integration.url);
                                        toast.success("URL Copied", {
                                            description: "Webhook URL copied to clipboard"
                                        });
                                    }} />
                                </span>
                            </TooltipTrigger>
                            <TooltipContent>
                                {integration.url}
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                    {integration.events.includes('message.new') && (
                        <Badge variant="secondary" className="text-xs">New messages</Badge>
                    )}
                    {integration.events.includes('rating.submit') && (
                        <Badge variant="secondary" className="text-xs">Ratings</Badge>
                    )}
                    {integration.events.includes('session.start') && (
                        <Badge variant="secondary" className="text-xs">Sessions start</Badge>
                    )}
                    {integration.events.includes('session.end') && (
                        <Badge variant="secondary" className="text-xs">Sessions end</Badge>
                    )}
                </div>
            </CardContent>
        </Card>
    )
} 