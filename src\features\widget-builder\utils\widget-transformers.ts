/**
 * Widget Transformer Utility
 * 
 * This utility provides functions to transform data between the widget builder form schema
 * and the API schema required by the backend. It handles mapping between different property
 * naming conventions and structures.
 */

import { z } from 'zod'
import { Widget, WidgetBuilderFormData } from '../types'

/**
 * Transforms widget builder form values to API format
 * 
 * @param formData Form values from widget builder
 * @returns Widget data in API format
 */
export function transformFormToApi(formData: WidgetBuilderFormData): Partial<Widget> {
  return {
    name: formData.name,
    description: formData.description,
    primaryColor: formData.primaryColor,
    secondaryColor: formData.secondaryColor,
    autoOpen: formData.autoOpen,
    userRatings: formData.userRatings,
    contextRetention: formData.contextRetention,
    enableAnalytics: formData.enableAnalytics,
    isActive: true,
    
    // Transform features to API format
    settings: {
      features: formData.features,
      advanced: formData.advanced,
      webhooks: formData.webhooks,
      integrations: formData.integrations,
      aiModel: formData.aiModel,
      preChatForm: formData.preChatForm,
      postChatSurvey: formData.postChatSurvey,
      domainRestrictions: formData.domainRestrictions,
      mobileOptimization: formData.mobileOptimization,
      conversationPersistence: formData.conversationPersistence,
      customCSS: formData.customCSS,
      logoUpload: formData.logoUpload
    }
  }
}

/**
 * Transforms API widget data to widget builder form values
 * 
 * @param apiData Widget data from API
 * @returns Form values for widget builder
 */
export function transformApiToForm(apiData: Widget): WidgetBuilderFormData {
  const settings = apiData.settings || {}
  
  return {
    name: apiData.name || '',
    description: apiData.description || '',
    primaryColor: apiData.primaryColor || '#3b82f6',
    secondaryColor: apiData.secondaryColor || '#ffffff',
    autoOpen: apiData.autoOpen || false,
    userRatings: apiData.userRatings || false,
    contextRetention: apiData.contextRetention || false,
    enableAnalytics: apiData.enableAnalytics || false,
    
    // Transform settings back to form format
    features: settings.features || {},
    advanced: settings.advanced || {},
    webhooks: settings.webhooks || [],
    integrations: settings.integrations || [],
    aiModel: settings.aiModel || {},
    preChatForm: settings.preChatForm || {},
    postChatSurvey: settings.postChatSurvey || {},
    domainRestrictions: settings.domainRestrictions || {},
    mobileOptimization: settings.mobileOptimization || {},
    conversationPersistence: settings.conversationPersistence || {},
    customCSS: settings.customCSS || {},
    logoUpload: settings.logoUpload || {}
  }
}

/**
 * Get default form data for widget builder
 */
export function getDefaultFormData(): WidgetBuilderFormData {
  return {
    name: '',
    description: '',
    primaryColor: '#3b82f6',
    secondaryColor: '#ffffff',
    autoOpen: false,
    userRatings: false,
    contextRetention: false,
    enableAnalytics: true,
    features: {},
    advanced: {},
    webhooks: [],
    integrations: [],
    aiModel: {},
    preChatForm: {},
    postChatSurvey: {},
    domainRestrictions: {},
    mobileOptimization: {},
    conversationPersistence: {},
    customCSS: {},
    logoUpload: {}
  }
}

/**
 * Validate widget form data
 */
export function validateWidgetFormData(data: WidgetBuilderFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!data.name?.trim()) {
    errors.push('Widget name is required')
  }

  if (data.name && data.name.length > 100) {
    errors.push('Widget name must be less than 100 characters')
  }

  if (data.description && data.description.length > 500) {
    errors.push('Widget description must be less than 500 characters')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
