/**
 * Widget Types
 * 
 * This file exports all type definitions for the widget feature
 */

import { z } from 'zod';
import { widgetSchema, integrationSchema } from '../schemas/widget-schema';

/**
 * Widget type definition based on Zod schema
 */
export type Widget = z.infer<typeof widgetSchema>;

/**
 * Integration type definition based on Zod schema
 */
export type Integration = z.infer<typeof integrationSchema>;

/**
 * Integration type options
 */
export type IntegrationType = 'slack' | 'discord' | 'ms-teams' | 'zapier' | 'generic';

/**
 * Integration event types
 */
export type IntegrationEvent =
    | 'message_sent'
    | 'conversation_started'
    | 'conversation_ended'
    | 'feedback_received'
    | 'error_occurred';

/**
 * Integration type details
 */
export interface IntegrationTypeInfo {
    type: IntegrationType;
    name: string;
    description: string;
    icon: string;
    color: string;
    documentationUrl?: string;
}

/**
 * Widget creation input
 */
export type CreateWidgetInput = Omit<Widget, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Widget update input
 */
export type UpdateWidgetInput = Partial<Omit<Widget, 'id' | 'createdAt' | 'updatedAt'>>;

/**
 * Widget with statistics
 */
export interface WidgetWithStats extends Widget {
    stats: {
        totalConversations: number;
        totalMessages: number;
        averageRating: number;
        activeUsers: number;
    };
}

/**
 * Widget filter options
 */
export interface WidgetFilters {
    search?: string;
    isActive?: boolean;
    projectId?: number;
    sortBy?: keyof Widget;
    sortDirection?: 'asc' | 'desc';
}

/**
 * Widget embed code options
 */
export interface EmbedCodeOptions {
    widgetId: string;
    format: 'script' | 'iframe';
    includeStyles: boolean;
    position?: 'left' | 'right' | 'bottom';
}

/**
 * Export integration related types from previous implementation
 */
export * from './integration'; 