/**
 * API Test Helper Utilities
 */

import { ApiTestResponse } from '../types'

/**
 * Format API response for display
 */
export function formatApiResponse(response: ApiTestResponse): string {
  return JSON.stringify(response.data, null, 2)
}

/**
 * Get status color based on HTTP status code
 */
export function getStatusColor(status: number): string {
  if (status >= 200 && status < 300) return 'text-green-600'
  if (status >= 300 && status < 400) return 'text-yellow-600'
  if (status >= 400 && status < 500) return 'text-orange-600'
  if (status >= 500) return 'text-red-600'
  return 'text-gray-600'
}

/**
 * Format duration for display
 */
export function formatDuration(duration: number): string {
  if (duration < 1000) return `${duration}ms`
  return `${(duration / 1000).toFixed(2)}s`
}

/**
 * Validate JSON string
 */
export function isValidJson(str: string): boolean {
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}

/**
 * Parse headers string to object
 */
export function parseHeaders(headersString: string): Record<string, string> {
  const headers: Record<string, string> = {}
  
  if (!headersString.trim()) return headers
  
  headersString.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split(':')
    if (key && valueParts.length > 0) {
      headers[key.trim()] = valueParts.join(':').trim()
    }
  })
  
  return headers
}
