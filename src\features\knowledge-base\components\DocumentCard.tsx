'use client'

import { <PERSON><PERSON><PERSON>, Trash, <PERSON>f<PERSON><PERSON><PERSON>, FileText } from 'lucide-react'
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from "@/components/ui/card"
import { <PERSON>ton } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Document } from '../types'
import { formatFileSize, formatDate, getDocumentStatus } from '../utils/document-utils'

interface DocumentCardProps {
    document: Document
    onDelete: (id: string) => void
    onProcess: (id: string) => void
    onSelect: (id: string) => void
}

export function DocumentCard({
    document,
    onDelete,
    onProcess,
    onSelect
}: DocumentCardProps) {
    const status = getDocumentStatus(document)

    return (
        <Card className="w-full hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                        <FileIcon className="h-4 w-4 text-primary" />
                        <CardTitle className="text-md truncate max-w-[200px]">{document.title}</CardTitle>
                    </div>
                    <Badge variant={status.color as any}>{status.label}</Badge>
                </div>
                <CardDescription className="text-xs">
                    {formatFileSize(document.fileSize)} • {document.fileType.toUpperCase()} •
                    Uploaded {formatDate(document.createdAt)}
                </CardDescription>
            </CardHeader>

            <CardContent className="pb-2">
                <p className="text-sm text-muted-foreground line-clamp-2">
                    {document.content || "No preview available"}
                </p>
            </CardContent>

            <CardFooter className="flex justify-between pt-2">
                <Button variant="outline" size="sm" onClick={() => onSelect(document.id)}>
                    <FileText className="h-4 w-4 mr-1" />
                    View
                </Button>

                <div className="flex gap-2">
                    {!document.isProcessed && (
                        <Button variant="outline" size="sm" onClick={() => onProcess(document.id)}>
                            <RefreshCw className="h-4 w-4 mr-1" />
                            Process
                        </Button>
                    )}

                    <Button
                        variant="outline"
                        size="sm"
                        className="text-destructive hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => onDelete(document.id)}
                    >
                        <Trash className="h-4 w-4" />
                    </Button>
                </div>
            </CardFooter>
        </Card>
    )
} 