/**
 * Follow-up API Service
 * 
 * This file contains all API calls related to follow-up functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    FollowUpChain,
    FollowUpQuestion,
    FollowUpR<PERSON>ponse,
    FollowUpFilters,
    FollowUpResult
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for follow-ups
 */
const FOLLOW_UP_API = `${API_BASE_URL}/follow-ups`;

/**
 * Follow-up API Service
 */
export const followUpService = {
    /**
     * Get all follow-up chains with optional filters
     */
    getFollowUpChains: async (filters?: FollowUpFilters): Promise<FollowUpChain[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(FOLLOW_UP_API, { params: filters });
        }, 'getFollowUpChains');

        if (error || !response) {
            throw error || new Error('Failed to fetch follow-up chains');
        }

        return response.data;
    },

    /**
     * Get a follow-up chain by ID
     */
    getFollowUpChain: async (id: string): Promise<FollowUpChain> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${FOLLOW_UP_API}/${id}`);
        }, 'getFollowUpChain');

        if (error || !response) {
            throw error || new Error(`Failed to fetch follow-up chain with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Create a new follow-up chain
     */
    createFollowUpChain: async (chain: Omit<FollowUpChain, 'id'>): Promise<FollowUpChain> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(FOLLOW_UP_API, chain);
        }, 'createFollowUpChain');

        if (error || !response) {
            throw error || new Error('Failed to create follow-up chain');
        }

        return response.data;
    },

    /**
     * Update a follow-up chain
     */
    updateFollowUpChain: async (id: string, chain: Partial<FollowUpChain>): Promise<FollowUpChain> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${FOLLOW_UP_API}/${id}`, chain);
        }, 'updateFollowUpChain');

        if (error || !response) {
            throw error || new Error(`Failed to update follow-up chain with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a follow-up chain
     */
    deleteFollowUpChain: async (id: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${FOLLOW_UP_API}/${id}`);
        }, 'deleteFollowUpChain');

        if (error || !response) {
            throw error || new Error(`Failed to delete follow-up chain with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Toggle follow-up chain active status
     */
    toggleFollowUpChainStatus: async (id: string, isActive: boolean): Promise<FollowUpChain> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${FOLLOW_UP_API}/${id}/toggle-status`, { isActive });
        }, 'toggleFollowUpChainStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle status for follow-up chain with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Submit a follow-up response
     */
    submitFollowUpResponse: async (response: Omit<FollowUpResponse, 'id'>): Promise<FollowUpResponse> => {
        const [apiResponse, error] = await tryCatch(async () => {
            return await axios.post(`${FOLLOW_UP_API}/responses`, response);
        }, 'submitFollowUpResponse');

        if (error || !apiResponse) {
            throw error || new Error('Failed to submit follow-up response');
        }

        return apiResponse.data;
    },

    /**
     * Get follow-up responses for a chain
     */
    getFollowUpResponses: async (chainId: string): Promise<FollowUpResponse[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${FOLLOW_UP_API}/${chainId}/responses`);
        }, 'getFollowUpResponses');

        if (error || !response) {
            throw error || new Error(`Failed to fetch responses for follow-up chain with ID ${chainId}`);
        }

        return response.data;
    },

    /**
     * Get follow-up results with metrics
     */
    getFollowUpResults: async (chainId: string): Promise<FollowUpResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${FOLLOW_UP_API}/${chainId}/results`);
        }, 'getFollowUpResults');

        if (error || !response) {
            throw error || new Error(`Failed to fetch results for follow-up chain with ID ${chainId}`);
        }

        return response.data;
    },

    /**
     * Generate follow-up questions based on a prompt or context
     */
    generateFollowUpQuestions: async (prompt: string, count: number = 3): Promise<FollowUpQuestion[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${FOLLOW_UP_API}/generate`, { prompt, count });
        }, 'generateFollowUpQuestions');

        if (error || !response) {
            throw error || new Error('Failed to generate follow-up questions');
        }

        return response.data;
    },

    /**
     * Get relevant follow-up chains for a conversation
     */
    getRelevantFollowUps: async (conversationContext: string): Promise<FollowUpChain[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${FOLLOW_UP_API}/relevant`, { context: conversationContext });
        }, 'getRelevantFollowUps');

        if (error || !response) {
            throw error || new Error('Failed to get relevant follow-up chains');
        }

        return response.data;
    }
};

export default followUpService; 