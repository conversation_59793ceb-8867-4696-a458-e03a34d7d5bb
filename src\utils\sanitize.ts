/**
 * Sanitization Utilities
 * 
 * This file contains utilities for sanitizing user input
 * to prevent XSS attacks and other security issues.
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html HTML content to sanitize
 * @returns Sanitized HTML
 */
export function sanitizeHtml(html: string): string {
  if (!html) return '';
  
  // Create a temporary element
  const tempElement = document.createElement('div');
  tempElement.textContent = html;
  return tempElement.innerHTML;
}

/**
 * Sanitize a string for use in a URL
 * @param str String to sanitize
 * @returns Sanitized string
 */
export function sanitizeUrlParam(str: string): string {
  if (!str) return '';
  
  // Remove any characters that could be used for XSS or URL manipulation
  return encodeURIComponent(str.replace(/[^\w\s-]/gi, ''));
}

/**
 * Sanitize an object's string properties
 * @param obj Object to sanitize
 * @returns Sanitized object
 */
export function sanitizeObject<T extends Record<string, any>>(obj: T): T {
  if (!obj) return {} as T;
  
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeHtml(value);
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized as T;
}

/**
 * Sanitize user input for chat messages
 * @param message Message to sanitize
 * @returns Sanitized message
 */
export function sanitizeChatMessage(message: string): string {
  if (!message) return '';
  
  // Remove any HTML tags
  const sanitized = message.replace(/<[^>]*>?/gm, '');
  
  // Trim whitespace
  return sanitized.trim();
}

/**
 * Validate and sanitize an email address
 * @param email Email to validate and sanitize
 * @returns Sanitized email or empty string if invalid
 */
export function validateEmail(email: string): string {
  if (!email) return '';
  
  // Basic email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    return '';
  }
  
  // Sanitize the email
  return email.trim().toLowerCase();
}

/**
 * Validate and sanitize a phone number
 * @param phone Phone number to validate and sanitize
 * @returns Sanitized phone number or empty string if invalid
 */
export function validatePhone(phone: string): string {
  if (!phone) return '';
  
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Basic validation - at least 7 digits
  if (digits.length < 7) {
    return '';
  }
  
  return digits;
}

/**
 * Sanitize CSS to prevent CSS injection attacks
 * @param css CSS to sanitize
 * @returns Sanitized CSS
 */
export function sanitizeCss(css: string): string {
  if (!css) return '';
  
  // Remove potentially dangerous CSS
  return css
    // Remove JavaScript URLs
    .replace(/url\s*\(\s*javascript:/gi, 'url(#')
    // Remove expression and behavior
    .replace(/expression\s*\(.*?\)/gi, '')
    .replace(/behavior\s*:.*?;/gi, '')
    // Remove import and charset
    .replace(/@import\s+url/gi, '/* @import url')
    .replace(/@charset\s+[^;]+;/gi, '/* @charset */');
}
