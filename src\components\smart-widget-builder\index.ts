// Main component
export { default as SmartWidgetBuilder } from '../SmartWidgetBuilder';

// Core components
export { default as TemplateGallery } from './TemplateGallery';
export { default as QuickSettings } from './QuickSettings';
export { default as FeatureCards } from './FeatureCards';
export { default as FeatureModals } from './FeatureModals';

// Modal components
export { default as PreChatModal } from './modals/PreChatModal';
export { default as PostChatModal } from './modals/PostChatModal';
export { default as WebhookModal } from './modals/WebhookModal';
export { default as DomainModal } from './modals/DomainModal';
export { default as PersistenceModal } from './modals/PersistenceModal';
export { default as MobileModal } from './modals/MobileModal';
export { default as CustomCSSModal } from './modals/CustomCSSModal';
export { default as AIModelModal } from './modals/AIModelModal';
export { default as LogoUploadModal } from './modals/LogoUploadModal';

// Types
export interface SmartWidgetConfig {
  name: string;
  welcomeMessage: string;
  primaryColor: string;
  secondaryColor: string;
  theme: 'modern' | 'glass' | 'dark' | 'rounded' | 'minimal';
  features: {
    preChat: boolean;
    webhooks: boolean;
    domainRestriction: boolean;
    conversationPersistence: boolean;
    mobileOptimization: boolean;
  };
  behavior: {
    autoOpen: boolean;
    autoOpenDelay: number;
    showLogo: boolean;
    startMinimized: boolean;
  };
}

export interface FeatureModalProps {
  form: any;
  onClose: () => void;
  widgetId?: string;
}
