/**
 * Context Rule Manager Component
 * 
 * This component provides UI for managing context rules
 */

import { useState } from 'react'
import { useContextRule } from '../hooks/use-context-rule'
import { ContextRule, ContextCondition, ContextAction } from '../types'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { AlertCircle, Plus, Settings, Brain, ListFilter } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { <PERSON>lider } from '@/components/ui/slider'

export interface ContextRuleManagerProps {
    sessionId?: string;
    onRuleSelect?: (rule: ContextRule) => void;
}

export function ContextRuleManager({ sessionId, onRuleSelect }: ContextRuleManagerProps) {
    const [activeTab, setActiveTab] = useState<string>('rules')
    const [testInput, setTestInput] = useState<string>('')
    const [editMode, setEditMode] = useState<boolean>(false)
    const [formState, setFormState] = useState<Partial<ContextRule>>({})

    const { toast } = useToast()

    const {
        rules,
        currentRule,
        contextData,
        matchResults,
        isLoading,
        isSaving,
        isEvaluating,
        error,
        fetchRules,
        fetchRule,
        createRule,
        updateRule,
        deleteRule,
        toggleRuleStatus,
        evaluateRules,
        getContextData,
        updateContextData,
        clearContextData,
        testRule,
        clearCurrentRule
    } = useContextRule()

    // Handle creating a new rule
    const handleCreateRule = () => {
        const newRule: Partial<ContextRule> = {
            name: 'New Rule',
            description: 'Enter a description for this rule',
            priority: 10,
            isActive: true,
            conditions: [{
                type: 'message',
                field: 'content',
                operator: 'contains',
                value: '',
                isNegated: false
            }],
            actions: [{
                type: 'set',
                field: 'context_value',
                value: '',
                isTransient: false
            }],
            scope: 'conversation',
            tags: []
        }

        setFormState(newRule)
        clearCurrentRule()
        setEditMode(true)
    }

    // Render content based on development status
    return (
        <div className="space-y-6">
            <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Module in development</AlertTitle>
                <AlertDescription>
                    The Context Rules module is currently under development. Basic functionality has been implemented but may be incomplete.
                </AlertDescription>
            </Alert>

            <Card>
                <CardHeader>
                    <CardTitle>Context Rules Configuration</CardTitle>
                    <CardDescription>
                        Define rules that determine how context is managed during conversations
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList className="mb-4">
                            <TabsTrigger value="rules">Rules</TabsTrigger>
                            <TabsTrigger value="conditions">Conditions</TabsTrigger>
                            <TabsTrigger value="testing">Testing</TabsTrigger>
                        </TabsList>

                        <TabsContent value="rules" className="space-y-4">
                            <div className="flex justify-between items-center">
                                <p className="text-muted-foreground">
                                    Create context-based rules that control how context is managed in conversations.
                                </p>
                                <Button onClick={handleCreateRule}>
                                    <Plus className="h-4 w-4 mr-2" /> Add Rule
                                </Button>
                            </div>

                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
                                {isLoading ? (
                                    <div className="col-span-full text-center py-8">Loading rules...</div>
                                ) : rules.length > 0 ? (
                                    rules.map(rule => (
                                        <Card key={rule.id} className="overflow-hidden">
                                            <CardHeader className="pb-2">
                                                <div className="flex justify-between items-center">
                                                    <div className="flex items-center gap-2">
                                                        <div className="bg-primary/10 text-primary w-6 h-6 rounded-full flex items-center justify-center text-xs">
                                                            {rule.priority}
                                                        </div>
                                                        <CardTitle className="text-base">{rule.name}</CardTitle>
                                                    </div>
                                                    <Switch
                                                        checked={rule.isActive}
                                                        onCheckedChange={(checked) => toggleRuleStatus(rule.id, checked)}
                                                    />
                                                </div>
                                                <CardDescription className="line-clamp-2">{rule.description}</CardDescription>
                                            </CardHeader>
                                            <CardContent className="pt-0">
                                                <div className="text-xs text-muted-foreground mb-2">
                                                    <span className="font-medium">Scope:</span> {rule.scope}
                                                    {rule.expirySeconds && (
                                                        <span className="ml-2">
                                                            (Expires after {rule.expirySeconds}s)
                                                        </span>
                                                    )}
                                                </div>

                                                <div className="flex flex-wrap gap-1 mt-2">
                                                    {rule.tags?.map(tag => (
                                                        <span key={tag} className="bg-muted px-2 py-1 rounded-md text-xs">
                                                            {tag}
                                                        </span>
                                                    ))}
                                                </div>

                                                <div className="flex justify-end gap-2 mt-4">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => fetchRule(rule.id)}
                                                    >
                                                        Edit
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => deleteRule(rule.id)}
                                                    >
                                                        Delete
                                                    </Button>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))
                                ) : (
                                    <div className="col-span-full text-center py-8 text-muted-foreground">
                                        No rules found. Create one to get started.
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="conditions">
                            <div className="grid gap-6">
                                <div>
                                    <h3 className="text-lg font-medium mb-2">Condition Types</h3>
                                    <p className="text-muted-foreground mb-4">
                                        Define conditions that will trigger specific actions based on user input and context.
                                    </p>

                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-base">Message</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-sm text-muted-foreground">
                                                    Trigger based on the content of user messages
                                                </p>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-base">Metadata</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-sm text-muted-foreground">
                                                    Trigger based on conversation metadata
                                                </p>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-base">Intent</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-sm text-muted-foreground">
                                                    Trigger based on detected user intent
                                                </p>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-base">Entity</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-sm text-muted-foreground">
                                                    Trigger based on detected entities in messages
                                                </p>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-base">Session</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-sm text-muted-foreground">
                                                    Trigger based on session data
                                                </p>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-base">Custom</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-sm text-muted-foreground">
                                                    Trigger based on custom logic
                                                </p>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </div>

                                <div>
                                    <h3 className="text-lg font-medium mb-2">Operators</h3>
                                    <p className="text-muted-foreground mb-4">
                                        Available operators for condition evaluation
                                    </p>

                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">equals</h4>
                                            <p className="text-xs text-muted-foreground">Exact match</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">contains</h4>
                                            <p className="text-xs text-muted-foreground">Contains string</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">startsWith</h4>
                                            <p className="text-xs text-muted-foreground">Starts with string</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">endsWith</h4>
                                            <p className="text-xs text-muted-foreground">Ends with string</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">exists</h4>
                                            <p className="text-xs text-muted-foreground">Field exists</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">greaterThan</h4>
                                            <p className="text-xs text-muted-foreground">Numeric comparison</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">lessThan</h4>
                                            <p className="text-xs text-muted-foreground">Numeric comparison</p>
                                        </div>
                                        <div className="p-3 border rounded-md">
                                            <h4 className="font-medium mb-1">regex</h4>
                                            <p className="text-xs text-muted-foreground">Regular expression</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="testing">
                            <div className="space-y-4">
                                <p className="text-muted-foreground">
                                    Test your context rules with sample inputs to ensure they work as expected.
                                </p>

                                <div className="grid gap-4">
                                    <div>
                                        <Label htmlFor="test-input">Test Input</Label>
                                        <Textarea
                                            id="test-input"
                                            value={testInput}
                                            onChange={(e) => setTestInput(e.target.value)}
                                            placeholder="Enter a sample message to test your rules against"
                                            className="h-24"
                                        />
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="session-id">Session ID</Label>
                                            <Input
                                                id="session-id"
                                                value={sessionId || ''}
                                                readOnly={!!sessionId}
                                                placeholder="Enter a session ID for testing"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="test-rule">Test Rule</Label>
                                            <Select>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select a rule to test" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Rules</SelectItem>
                                                    {rules.map(rule => (
                                                        <SelectItem key={rule.id} value={rule.id}>
                                                            {rule.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>

                                    <Button disabled={!testInput || isEvaluating}>
                                        Test Rules
                                    </Button>

                                    <div className="border rounded-md p-4 bg-muted/20">
                                        <h3 className="font-medium mb-2">Results</h3>
                                        <p className="text-sm text-muted-foreground">
                                            {matchResults.length
                                                ? `${matchResults.filter(r => r.matched).length} of ${matchResults.length} rules matched`
                                                : 'No rules have been tested yet'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    )
}