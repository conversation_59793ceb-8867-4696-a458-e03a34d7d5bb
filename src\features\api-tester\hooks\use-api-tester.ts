/**
 * API Tester Hook
 * 
 * Custom hook for managing API testing functionality
 */

import { useState, useCallback } from 'react'
import { apiTesterService } from '../api'
import { ApiRoute, ApiTestRequest, ApiTestResponse, TestConfiguration } from '../types'
import { useToast } from '@/components/ui/use-toast'

export function useApiTester() {
  const [routes, setRoutes] = useState<ApiRoute[]>([])
  const [configurations, setConfigurations] = useState<TestConfiguration[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<ApiTestResponse[]>([])
  
  const { toast } = useToast()

  /**
   * Load available API routes
   */
  const loadRoutes = useCallback(async () => {
    setIsLoading(true)
    try {
      const data = await apiTesterService.getApiRoutes()
      setRoutes(data)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load API routes',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  /**
   * Test an API endpoint
   */
  const testEndpoint = useCallback(async (request: ApiTestRequest) => {
    setIsLoading(true)
    try {
      const result = await apiTesterService.testEndpoint(request)
      setTestResults(prev => [...prev, result])
      
      toast({
        title: result.success ? 'Test Successful' : 'Test Failed',
        description: `${request.method} ${request.url} - ${result.status} ${result.statusText}`,
        variant: result.success ? 'default' : 'destructive'
      })
      
      return result
    } catch (error) {
      toast({
        title: 'Test Error',
        description: 'Failed to execute API test',
        variant: 'destructive'
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  /**
   * Load test configurations
   */
  const loadConfigurations = useCallback(async () => {
    setIsLoading(true)
    try {
      const data = await apiTesterService.getTestConfigurations()
      setConfigurations(data)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load test configurations',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  /**
   * Save test configuration
   */
  const saveConfiguration = useCallback(async (config: Omit<TestConfiguration, 'id' | 'createdAt' | 'updatedAt'>) => {
    setIsLoading(true)
    try {
      const saved = await apiTesterService.saveTestConfiguration(config)
      setConfigurations(prev => [...prev, saved])
      
      toast({
        title: 'Configuration Saved',
        description: 'Test configuration has been saved successfully',
        variant: 'default'
      })
      
      return saved
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save test configuration',
        variant: 'destructive'
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  /**
   * Clear test results
   */
  const clearResults = useCallback(() => {
    setTestResults([])
  }, [])

  return {
    routes,
    configurations,
    testResults,
    isLoading,
    loadRoutes,
    testEndpoint,
    loadConfigurations,
    saveConfiguration,
    clearResults
  }
}
