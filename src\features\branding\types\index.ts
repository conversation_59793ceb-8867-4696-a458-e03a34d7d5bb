/**
 * Branding Types
 * 
 * This file exports all type definitions for the branding feature
 */

import { z } from 'zod';
import {
    colorSchema,
    typographySchema,
    logoSchema,
    brandingSchema,
    messageFormatSchema
} from '../schemas/branding-schema';

/**
 * Color theme type
 */
export type ColorTheme = z.infer<typeof colorSchema>;

/**
 * Typography settings type
 */
export type Typography = z.infer<typeof typographySchema>;

/**
 * Logo settings type
 */
export type Logo = z.infer<typeof logoSchema>;

/**
 * Voice tone settings type
 */
export interface VoiceTone {
    formal: number;
    friendly: number;
    technical: number;
    persuasive: number;
}

/**
 * Branding type
 */
export type Branding = z.infer<typeof brandingSchema>;

/**
 * Message format type
 */
export type MessageFormat = z.infer<typeof messageFormatSchema>;

/**
 * Message format type enum
 */
export type MessageFormatType = 'greeting' | 'response' | 'followup' | 'error' | 'custom';

/**
 * Branding filter options
 */
export interface BrandingFilters {
    search?: string;
    isDefault?: boolean;
    sortBy?: keyof Branding;
    sortDirection?: 'asc' | 'desc';
}

/**
 * Message format filter options
 */
export interface MessageFormatFilters {
    search?: string;
    type?: MessageFormatType;
    brandId?: string;
    isDefault?: boolean;
    sortBy?: keyof MessageFormat;
    sortDirection?: 'asc' | 'desc';
}

/**
 * Create branding input
 */
export type CreateBrandingInput = Omit<Branding, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Update branding input
 */
export type UpdateBrandingInput = Partial<Omit<Branding, 'id' | 'createdAt' | 'updatedAt'>>;

/**
 * Create message format input
 */
export type CreateMessageFormatInput = Omit<MessageFormat, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Update message format input
 */
export type UpdateMessageFormatInput = Partial<Omit<MessageFormat, 'id' | 'createdAt' | 'updatedAt'>>;

/**
 * Format message input
 */
export interface FormatMessageInput {
    formatId: string;
    variables: Record<string, string>;
} 