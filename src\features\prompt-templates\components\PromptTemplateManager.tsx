/**
 * Prompt Template Manager Component
 * 
 * This component provides UI for managing prompt templates
 */

import { useState, useEffect } from 'react'
import { usePromptTemplate } from '../hooks/use-prompt-template'
import { PromptTemplate, PromptVariable, TemplateCategory } from '../types'

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/hooks/use-toast'
import {
    Edit,
    FileText,
    Play,
    Save,
    Trash2,
    PlusCircle,
    RotateCcw,
    Copy
} from 'lucide-react'

export interface PromptTemplateManagerProps {
    initialCategory?: TemplateCategory;
    onTemplateSelect?: (template: PromptTemplate) => void;
}

export function PromptTemplateManager({ initialCategory, onTemplateSelect }: PromptTemplateManagerProps) {
    const [activeTab, setActiveTab] = useState<string>('templates')
    const [editMode, setEditMode] = useState<boolean>(false)
    const [testInput, setTestInput] = useState<string>('')
    const [testResult, setTestResult] = useState<string>('')
    const [formState, setFormState] = useState<Partial<PromptTemplate>>({})
    const [variablesInput, setVariablesInput] = useState<string>('')

    const { toast } = useToast()

    const {
        templates,
        currentTemplate,
        processedTemplate,
        isLoading,
        isSaving,
        isProcessing,
        error,
        fetchTemplates,
        fetchTemplate,
        createTemplate,
        updateTemplate,
        deleteTemplate,
        setAsDefaultTemplate,
        toggleTemplateStatus,
        processTemplate,
        clearCurrentTemplate,
        clearProcessedTemplate,
    } = usePromptTemplate()

    // Load templates on mount
    useEffect(() => {
        const loadTemplates = async () => {
            await fetchTemplates(initialCategory ? { category: initialCategory } : undefined)
        }

        loadTemplates()
    }, [fetchTemplates, initialCategory])

    // Handle template selection
    const handleSelectTemplate = async (id: string) => {
        await fetchTemplate(id)
        setEditMode(false)
        setTestInput('')
        setTestResult('')
        setVariablesInput('')

        if (onTemplateSelect && currentTemplate) {
            onTemplateSelect(currentTemplate)
        }
    }

    // Handle creating a new template
    const handleCreateTemplate = () => {
        const newTemplate: Partial<PromptTemplate> = {
            name: 'New Template',
            description: 'Enter a description for this template',
            category: initialCategory || 'system',
            content: 'Enter your prompt template here. Use {{variable_name}} for variables that should be replaced with actual values.',
            version: 1,
            isActive: true,
            isDefault: false,
            variables: [],
            tags: []
        }

        setFormState(newTemplate)
        clearCurrentTemplate()
        setEditMode(true)
    }

    // Handle saving a template
    const handleSaveTemplate = async () => {
        if (!formState.name || !formState.content) {
            toast({
                title: 'Validation Error',
                description: 'Name and content are required fields',
                variant: 'destructive'
            })
            return
        }

        try {
            if (currentTemplate?.id) {
                // Update existing template
                await updateTemplate(currentTemplate.id, formState)
                toast({
                    title: 'Template Updated',
                    description: 'The prompt template has been saved successfully.'
                })
            } else {
                // Create new template
                await createTemplate(formState as Omit<PromptTemplate, 'id'>)
                toast({
                    title: 'Template Created',
                    description: 'The new prompt template has been created successfully.'
                })
            }

            setEditMode(false)
            await fetchTemplates(initialCategory ? { category: initialCategory } : undefined)
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to save the template. Please try again.',
                variant: 'destructive'
            })
        }
    }

    // Handle deleting a template
    const handleDeleteTemplate = async (id: string) => {
        if (confirm('Are you sure you want to delete this template?')) {
            try {
                await deleteTemplate(id)
                toast({
                    title: 'Template Deleted',
                    description: 'The template has been removed successfully.'
                })
            } catch (error) {
                toast({
                    title: 'Error',
                    description: 'Failed to delete the template.',
                    variant: 'destructive'
                })
            }
        }
    }

    // Handle toggling template active status
    const handleToggleStatus = async (id: string, isActive: boolean) => {
        try {
            await toggleTemplateStatus(id, isActive)
            toast({
                title: isActive ? 'Template Activated' : 'Template Deactivated',
                description: `The template has been ${isActive ? 'activated' : 'deactivated'} successfully.`
            })
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to update template status.',
                variant: 'destructive'
            })
        }
    }

    // Handle setting a template as default
    const handleSetAsDefault = async (id: string) => {
        try {
            await setAsDefaultTemplate(id)
            toast({
                title: 'Default Template Set',
                description: 'The template has been set as default successfully.'
            })
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to set the template as default.',
                variant: 'destructive'
            })
        }
    }

    // Handle testing a template
    const handleTestTemplate = async () => {
        if (!currentTemplate) return

        try {
            // Parse variables from input
            let variables = {}
            try {
                if (variablesInput) {
                    variables = JSON.parse(variablesInput)
                }
            } catch (error) {
                toast({
                    title: 'Invalid Variables Format',
                    description: 'Variables must be in valid JSON format.',
                    variant: 'destructive'
                })
                return
            }

            const result = await processTemplate({
                templateId: currentTemplate.id,
                variables,
                context: { user_input: testInput }
            })

            if (result) {
                setTestResult(result.content)
            }
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to process the template.',
                variant: 'destructive'
            })
        }
    }

    // Handle form input changes
    const handleFormChange = (field: string, value: any) => {
        setFormState(prev => ({
            ...prev,
            [field]: value
        }))
    }

    return (
        <div className="grid gap-6 md:grid-cols-12">
            {/* Templates List */}
            <div className="md:col-span-4 space-y-6">
                <Card>
                    <CardHeader className="pb-3">
                        <div className="flex justify-between items-center">
                            <CardTitle className="text-xl flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Templates
                            </CardTitle>
                            <Button variant="outline" size="sm" onClick={handleCreateTemplate}>
                                <PlusCircle className="h-4 w-4 mr-2" /> New
                            </Button>
                        </div>
                        <CardDescription>
                            Create and manage prompt templates
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="py-4 text-center">Loading templates...</div>
                        ) : templates.length > 0 ? (
                            <div className="space-y-2">
                                {templates.map((template) => (
                                    <div
                                        key={template.id}
                                        className={`p-3 border rounded-md cursor-pointer transition-colors hover:bg-muted ${currentTemplate?.id === template.id ? 'border-primary bg-muted/50' : ''}`}
                                        onClick={() => handleSelectTemplate(template.id)}
                                    >
                                        <div className="flex justify-between items-center">
                                            <div>
                                                <h3 className="font-medium">{template.name}</h3>
                                                <p className="text-xs text-muted-foreground">
                                                    {template.category} • v{template.version}
                                                    {template.isDefault && <span className="ml-2 text-primary">Default</span>}
                                                    {!template.isActive && <span className="ml-2 text-muted-foreground">(Inactive)</span>}
                                                </p>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteTemplate(template.id);
                                                }}
                                            >
                                                <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="py-4 text-center text-muted-foreground">
                                No templates found. Create one to get started.
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Template Editor/Viewer */}
            <div className="md:col-span-8">
                {currentTemplate || editMode ? (
                    <Card>
                        <CardHeader>
                            <div className="flex justify-between items-center">
                                <CardTitle>
                                    {editMode ? (
                                        <Input
                                            value={formState.name || currentTemplate?.name || ''}
                                            onChange={(e) => handleFormChange('name', e.target.value)}
                                            placeholder="Template Name"
                                            className="text-xl font-semibold"
                                        />
                                    ) : (
                                        currentTemplate?.name
                                    )}
                                </CardTitle>
                                <div className="flex gap-2">
                                    {!editMode && (
                                        <>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                    setFormState(currentTemplate || {})
                                                    setEditMode(true)
                                                }}
                                            >
                                                <Edit className="h-4 w-4 mr-2" /> Edit
                                            </Button>
                                            {currentTemplate && (
                                                <>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleToggleStatus(currentTemplate.id, !currentTemplate.isActive)}
                                                    >
                                                        {currentTemplate.isActive ? 'Deactivate' : 'Activate'}
                                                    </Button>
                                                    {!currentTemplate.isDefault && (
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleSetAsDefault(currentTemplate.id)}
                                                        >
                                                            Set as Default
                                                        </Button>
                                                    )}
                                                </>
                                            )}
                                        </>
                                    )}
                                    {editMode && (
                                        <>
                                            <Button variant="outline" size="sm" onClick={() => setEditMode(false)}>
                                                <RotateCcw className="h-4 w-4 mr-2" /> Cancel
                                            </Button>
                                            <Button size="sm" onClick={handleSaveTemplate} disabled={isSaving}>
                                                <Save className="h-4 w-4 mr-2" /> Save
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                            {editMode ? (
                                <Textarea
                                    value={formState.description || currentTemplate?.description || ''}
                                    onChange={(e) => handleFormChange('description', e.target.value)}
                                    placeholder="Add a description for this template"
                                    className="h-20"
                                />
                            ) : (
                                <CardDescription>{currentTemplate?.description}</CardDescription>
                            )}
                        </CardHeader>
                        <CardContent>
                            <Tabs defaultValue="editor">
                                <TabsList className="mb-4">
                                    <TabsTrigger value="editor">Editor</TabsTrigger>
                                    <TabsTrigger value="test">Test</TabsTrigger>
                                    <TabsTrigger value="settings">Settings</TabsTrigger>
                                </TabsList>

                                <TabsContent value="editor" className="space-y-4">
                                    {editMode ? (
                                        <Textarea
                                            value={formState.content || currentTemplate?.content || ''}
                                            onChange={(e) => handleFormChange('content', e.target.value)}
                                            placeholder="Enter your prompt template here. Use {{variable_name}} for variables."
                                            className="min-h-[300px] font-mono"
                                        />
                                    ) : (
                                        <div className="p-4 border rounded-md bg-muted/20 min-h-[300px] whitespace-pre-wrap font-mono">
                                            {currentTemplate?.content}
                                        </div>
                                    )}
                                </TabsContent>

                                <TabsContent value="test" className="space-y-4">
                                    <div className="grid gap-4">
                                        <div>
                                            <Label>Variables (JSON format)</Label>
                                            <Textarea
                                                value={variablesInput}
                                                onChange={(e) => setVariablesInput(e.target.value)}
                                                placeholder='{"variable_name": "value", "another_variable": "value"}'
                                                className="h-24 font-mono"
                                            />
                                        </div>
                                        <div>
                                            <Label>Test Input</Label>
                                            <Textarea
                                                value={testInput}
                                                onChange={(e) => setTestInput(e.target.value)}
                                                placeholder="Enter test input to use with this template"
                                                className="h-24"
                                            />
                                        </div>
                                        <Button onClick={handleTestTemplate} disabled={isProcessing}>
                                            <Play className="h-4 w-4 mr-2" /> Test Template
                                        </Button>
                                        {testResult && (
                                            <div className="mt-4">
                                                <Label>Result</Label>
                                                <div className="p-4 border rounded-md bg-muted/20 whitespace-pre-wrap">
                                                    {testResult}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="settings" className="space-y-4">
                                    <div className="grid gap-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <Label>Category</Label>
                                                <Select
                                                    value={editMode ? formState.category : currentTemplate?.category}
                                                    onValueChange={(value) => handleFormChange('category', value)}
                                                    disabled={!editMode}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select category" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="system">System</SelectItem>
                                                        <SelectItem value="user">User</SelectItem>
                                                        <SelectItem value="assistant">Assistant</SelectItem>
                                                        <SelectItem value="function">Function</SelectItem>
                                                        <SelectItem value="custom">Custom</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div>
                                                <Label>Version</Label>
                                                <Input
                                                    value={currentTemplate?.version || 1}
                                                    disabled
                                                    className="bg-muted/20"
                                                />
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                id="isActive"
                                                checked={editMode ? !!formState.isActive : !!currentTemplate?.isActive}
                                                onCheckedChange={(checked) => handleFormChange('isActive', checked)}
                                                disabled={!editMode}
                                            />
                                            <Label htmlFor="isActive">Active</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                id="isDefault"
                                                checked={editMode ? !!formState.isDefault : !!currentTemplate?.isDefault}
                                                onCheckedChange={(checked) => handleFormChange('isDefault', checked)}
                                                disabled={!editMode}
                                            />
                                            <Label htmlFor="isDefault">Default for category</Label>
                                        </div>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                ) : (
                    <Card>
                        <CardContent className="py-12">
                            <div className="text-center">
                                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
                                <h3 className="text-lg font-medium">Select a template</h3>
                                <p className="text-muted-foreground mt-1">
                                    Select a template from the list or create a new one to get started.
                                </p>
                                <Button className="mt-4" onClick={handleCreateTemplate}>
                                    <PlusCircle className="h-4 w-4 mr-2" /> Create New Template
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </div>
    )
} 