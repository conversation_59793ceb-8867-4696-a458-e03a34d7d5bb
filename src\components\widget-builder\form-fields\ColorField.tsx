/**
 * Color Field Component
 * 
 * A form field for selecting colors with a color picker.
 */

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormDescription, FormMessage } from '@/components/ui/form';
import ColorPicker from '@/components/widget-builder/ColorPicker';

interface ColorFieldProps {
  label: string;
  fieldName: string;
  control: Control<any>;
  description?: string;
  presets?: string[];
}

/**
 * Color Field Component
 * A form field for selecting colors with a color picker
 */
export function ColorField({
  label,
  fieldName,
  control,
  description,
  presets = []
}: ColorFieldProps) {
  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <ColorPicker
            color={field.value || "#1e40af"}
            onChange={(value) => {
              // Ensure we're setting a valid value
              field.onChange(value || "#1e40af");
            }}
            presets={presets}
          />
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
