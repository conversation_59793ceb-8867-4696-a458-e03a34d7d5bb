/**
 * Response Formatter API Service
 * 
 * This file contains all API calls related to response formatter functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    ResponseFormatter,
    FormatterFilters,
    FormatResponseInput,
    FormatResponseResult
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for response formatters
 */
const FORMATTER_API = `${API_BASE_URL}/response-formatters`;

/**
 * Response Formatter API Service
 */
export const responseFormatterService = {
    /**
     * Get all formatters with optional filters
     */
    getFormatters: async (filters?: FormatterFilters): Promise<ResponseFormatter[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(FORMATTER_API, { params: filters });
        }, 'getFormatters');

        if (error || !response) {
            throw error || new Error('Failed to fetch response formatters');
        }

        return response.data;
    },

    /**
     * Get a formatter by ID
     */
    getFormatter: async (id: string): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${FORMATTER_API}/${id}`);
        }, 'getFormatter');

        if (error || !response) {
            throw error || new Error(`Failed to fetch response formatter with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Get the default formatter
     */
    getDefaultFormatter: async (type?: string): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${FORMATTER_API}/default`, { params: { type } });
        }, 'getDefaultFormatter');

        if (error || !response) {
            throw error || new Error('Failed to fetch default response formatter');
        }

        return response.data;
    },

    /**
     * Create a new formatter
     */
    createFormatter: async (formatter: Omit<ResponseFormatter, 'id'>): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(FORMATTER_API, formatter);
        }, 'createFormatter');

        if (error || !response) {
            throw error || new Error('Failed to create response formatter');
        }

        return response.data;
    },

    /**
     * Update a formatter
     */
    updateFormatter: async (id: string, formatter: Partial<ResponseFormatter>): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${FORMATTER_API}/${id}`, formatter);
        }, 'updateFormatter');

        if (error || !response) {
            throw error || new Error(`Failed to update response formatter with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a formatter
     */
    deleteFormatter: async (id: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${FORMATTER_API}/${id}`);
        }, 'deleteFormatter');

        if (error || !response) {
            throw error || new Error(`Failed to delete response formatter with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Set a formatter as default
     */
    setDefaultFormatter: async (id: string): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${FORMATTER_API}/${id}/default`, { isDefault: true });
        }, 'setDefaultFormatter');

        if (error || !response) {
            throw error || new Error(`Failed to set response formatter with ID ${id} as default`);
        }

        return response.data;
    },

    /**
     * Toggle formatter active status
     */
    toggleFormatterStatus: async (id: string, isActive: boolean): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${FORMATTER_API}/${id}/toggle-status`, { isActive });
        }, 'toggleFormatterStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle status for response formatter with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Format a response
     */
    formatResponse: async (input: FormatResponseInput): Promise<FormatResponseResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${FORMATTER_API}/format`, input);
        }, 'formatResponse');

        if (error || !response) {
            throw error || new Error('Failed to format response');
        }

        return response.data;
    },

    /**
     * Preview a formatter with sample content
     */
    previewFormatter: async (id: string, content: string): Promise<FormatResponseResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${FORMATTER_API}/${id}/preview`, { content });
        }, 'previewFormatter');

        if (error || !response) {
            throw error || new Error(`Failed to preview formatter with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Clone a formatter
     */
    cloneFormatter: async (id: string, updates?: Partial<ResponseFormatter>): Promise<ResponseFormatter> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${FORMATTER_API}/${id}/clone`, updates || {});
        }, 'cloneFormatter');

        if (error || !response) {
            throw error || new Error(`Failed to clone formatter with ID ${id}`);
        }

        return response.data;
    }
};

export default responseFormatterService; 