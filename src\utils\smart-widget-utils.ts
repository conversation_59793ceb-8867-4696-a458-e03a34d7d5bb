/**
 * Smart Widget Builder Utilities
 * 
 * Utility functions for smart widget builder functionality.
 * Includes validation, transformation, and helper functions.
 */

import { SmartWidgetFormData } from '@/hooks/use-smart-widget-builder';
import { IntegrationConfig, WebhookConfig } from '@/services/smart-widget-service';

// Feature configuration metadata
export const FEATURE_CONFIG = {
  preChat: {
    title: 'Collect Visitor Information',
    description: 'Set up a form to collect visitor details before chat starts',
    icon: 'UserPlus',
    category: 'engagement',
    complexity: 'basic',
    benefits: ['Higher quality leads', 'Better personalization', 'Contact information capture'],
  },
  postChat: {
    title: 'Post-Chat Survey',
    description: 'Collect feedback and ratings after chat sessions',
    icon: 'MessageSquare',
    category: 'feedback',
    complexity: 'basic',
    benefits: ['Customer satisfaction insights', 'Service improvement data', 'Quality metrics'],
  },
  webhooks: {
    title: 'Connect Your Tools',
    description: 'Send chat data to Slack, email, CRM, or any webhook URL',
    icon: 'Zap',
    category: 'integration',
    complexity: 'intermediate',
    benefits: ['Real-time notifications', 'CRM integration', 'Workflow automation'],
  },
  domainRestriction: {
    title: 'Control Widget Access',
    description: 'Choose which websites can display your widget',
    icon: 'Shield',
    category: 'security',
    complexity: 'basic',
    benefits: ['Prevent unauthorized usage', 'Brand protection', 'Usage control'],
  },
  conversationPersistence: {
    title: 'Remember Conversations',
    description: 'Configure how chat history is stored and retrieved',
    icon: 'Database',
    category: 'experience',
    complexity: 'intermediate',
    benefits: ['Seamless user experience', 'Context retention', 'Conversation continuity'],
  },
  mobileOptimization: {
    title: 'Mobile Experience',
    description: 'Optimize widget display and behavior for mobile devices',
    icon: 'Smartphone',
    category: 'experience',
    complexity: 'basic',
    benefits: ['Better mobile UX', 'Touch optimization', 'Responsive design'],
  },
  customCSS: {
    title: 'Custom CSS Styling',
    description: 'Add custom CSS to completely customize your widget\'s appearance',
    icon: 'Palette',
    category: 'customization',
    complexity: 'advanced',
    benefits: ['Complete design control', 'Brand consistency', 'Unique styling'],
  },
  aiModelSelection: {
    title: 'AI Model Selection',
    description: 'Choose specific AI models for optimal performance',
    icon: 'Brain',
    category: 'ai',
    complexity: 'intermediate',
    benefits: ['Performance optimization', 'Cost control', 'Feature access'],
  },
  logoUpload: {
    title: 'Widget Logo & Avatar',
    description: 'Upload custom logo to strengthen brand identity',
    icon: 'Image',
    category: 'branding',
    complexity: 'basic',
    benefits: ['Brand recognition', 'Professional appearance', 'Trust building'],
  },
  userRatings: {
    title: 'User Ratings',
    description: 'Allow users to rate their chat experience',
    icon: 'Star',
    category: 'feedback',
    complexity: 'basic',
    benefits: ['Quality feedback', 'Performance metrics', 'User satisfaction'],
  },
  analytics: {
    title: 'Enable Analytics',
    description: 'Track widget performance and user interactions',
    icon: 'BarChart3',
    category: 'analytics',
    complexity: 'basic',
    benefits: ['Usage insights', 'Performance tracking', 'Data-driven decisions'],
  },
  autoOpen: {
    title: 'Auto Open Widget',
    description: 'Automatically open the widget after a delay',
    icon: 'Timer',
    category: 'engagement',
    complexity: 'basic',
    benefits: ['Proactive engagement', 'Higher interaction rates', 'User guidance'],
  },
} as const;

// Feature categories for organization
export const FEATURE_CATEGORIES = {
  engagement: {
    title: 'Engagement',
    description: 'Features to increase user interaction',
    color: 'blue',
  },
  feedback: {
    title: 'Feedback & Analytics',
    description: 'Collect insights and measure performance',
    color: 'green',
  },
  integration: {
    title: 'Integrations',
    description: 'Connect with external tools and services',
    color: 'purple',
  },
  security: {
    title: 'Security & Control',
    description: 'Manage access and protect your widget',
    color: 'red',
  },
  experience: {
    title: 'User Experience',
    description: 'Enhance the user journey and interaction',
    color: 'orange',
  },
  customization: {
    title: 'Customization',
    description: 'Personalize appearance and behavior',
    color: 'pink',
  },
  ai: {
    title: 'AI Configuration',
    description: 'Configure AI models and behavior',
    color: 'indigo',
  },
  branding: {
    title: 'Branding',
    description: 'Customize visual identity and branding',
    color: 'yellow',
  },
  analytics: {
    title: 'Analytics',
    description: 'Track and analyze widget performance',
    color: 'teal',
  },
} as const;

// Validation utilities
export const validateWebhookUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
};

export const validateDomain = (domain: string): boolean => {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain);
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateCSS = (css: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Basic CSS validation
  if (css.includes('<script>') || css.includes('javascript:')) {
    errors.push('JavaScript is not allowed in CSS');
  }
  
  // Check for balanced braces
  const openBraces = (css.match(/{/g) || []).length;
  const closeBraces = (css.match(/}/g) || []).length;
  
  if (openBraces !== closeBraces) {
    errors.push('Unbalanced CSS braces');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Transformation utilities
export const transformIntegrationToWebhook = (integration: IntegrationConfig): WebhookConfig => {
  return {
    url: integration.url,
    method: 'POST',
    headers: integration.type === 'slack' ? { 'Content-Type': 'application/json' } : {},
    secret: integration.secret,
    eventType: 'message_sent',
  };
};

export const generateWidgetPreviewData = (formData: SmartWidgetFormData) => {
  return {
    name: formData.name || 'Preview Widget',
    primaryColor: formData.quickSettings.primaryColor,
    theme: formData.quickSettings.theme,
    position: formData.quickSettings.position,
    size: formData.quickSettings.size,
    borderRadius: formData.quickSettings.borderRadius,
    welcomeMessage: formData.quickSettings.welcomeMessage,
    features: formData.features,
    hasLogo: formData.features.logoUpload && formData.advanced.logoUpload?.logoUrl,
    logoUrl: formData.advanced.logoUpload?.logoUrl,
  };
};

// Feature dependency checking
export const getFeatureDependencies = (featureName: keyof SmartWidgetFormData['features']): string[] => {
  const dependencies: Record<string, string[]> = {
    postChat: ['userRatings'], // Post-chat surveys often include ratings
    webhooks: [], // No dependencies
    customCSS: [], // No dependencies
    // Add more dependencies as needed
  };
  
  return dependencies[featureName] || [];
};

export const checkFeatureConflicts = (
  features: SmartWidgetFormData['features']
): { hasConflicts: boolean; conflicts: string[] } => {
  const conflicts: string[] = [];
  
  // Example conflict: Custom CSS might conflict with theme settings
  if (features.customCSS && features.logoUpload) {
    // This is just an example - adjust based on actual conflicts
    // conflicts.push('Custom CSS may override logo positioning');
  }
  
  return {
    hasConflicts: conflicts.length > 0,
    conflicts,
  };
};

// Template utilities
export const getRecommendedFeatures = (category: string): (keyof SmartWidgetFormData['features'])[] => {
  const recommendations: Record<string, (keyof SmartWidgetFormData['features'])[]> = {
    'lead-generation': ['preChat', 'webhooks', 'analytics'],
    'customer-support': ['postChat', 'userRatings', 'conversationPersistence'],
    'sales': ['preChat', 'webhooks', 'analytics', 'autoOpen'],
    'feedback': ['postChat', 'userRatings', 'analytics'],
    'basic': ['analytics', 'mobileOptimization'],
  };
  
  return recommendations[category] || [];
};

// Color utilities
export const generateColorPalette = (primaryColor: string) => {
  // This would generate a complementary color palette
  // For now, return a simple palette
  return {
    primary: primaryColor,
    secondary: adjustColorBrightness(primaryColor, -20),
    accent: adjustColorBrightness(primaryColor, 20),
    text: '#333333',
    background: '#ffffff',
  };
};

export const adjustColorBrightness = (color: string, amount: number): string => {
  // Simple color brightness adjustment
  // In a real implementation, you'd use a proper color manipulation library
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = Math.max(0, Math.min(255, (num >> 16) + amount));
  const g = Math.max(0, Math.min(255, (num >> 8 & 0x00FF) + amount));
  const b = Math.max(0, Math.min(255, (num & 0x0000FF) + amount));
  return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;
};

// Export utilities
export const exportWidgetConfig = (formData: SmartWidgetFormData) => {
  return {
    version: '1.0',
    exportedAt: new Date().toISOString(),
    config: formData,
  };
};

export const importWidgetConfig = (configData: any): SmartWidgetFormData | null => {
  try {
    if (configData.version === '1.0' && configData.config) {
      return configData.config as SmartWidgetFormData;
    }
    return null;
  } catch {
    return null;
  }
};

// Performance utilities
export const calculateWidgetComplexity = (formData: SmartWidgetFormData): 'low' | 'medium' | 'high' => {
  const enabledFeatures = Object.values(formData.features).filter(Boolean).length;
  const hasAdvancedFeatures = formData.features.customCSS || formData.features.webhooks;
  
  if (enabledFeatures <= 3 && !hasAdvancedFeatures) return 'low';
  if (enabledFeatures <= 6 || hasAdvancedFeatures) return 'medium';
  return 'high';
};

export const getPerformanceRecommendations = (formData: SmartWidgetFormData): string[] => {
  const recommendations: string[] = [];
  const complexity = calculateWidgetComplexity(formData);
  
  if (complexity === 'high') {
    recommendations.push('Consider reducing the number of enabled features for better performance');
  }
  
  if (formData.features.customCSS) {
    recommendations.push('Optimize custom CSS for faster loading');
  }
  
  if (formData.advanced.integrations?.length > 5) {
    recommendations.push('Too many integrations may slow down the widget');
  }
  
  return recommendations;
};
