/**
 * Role Schema Definitions
 * 
 * This file contains Zod schemas for roles
 */

import { z } from 'zod'
import { permissionSchema } from './permission-schema'

/**
 * Base role schema
 */
export const roleBaseSchema = z.object({
    name: z.string()
        .min(2, 'Role name must be at least 2 characters')
        .max(50, 'Role name must be less than 50 characters'),
    description: z.string().nullable().optional(),
})

/**
 * Role schema with ID and timestamps
 */
export const roleSchema = roleBaseSchema.extend({
    id: z.number(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
    permissions: z.array(permissionSchema).optional(),
})

/**
 * Role schema for creation
 */
export const roleCreateSchema = roleBaseSchema

/**
 * Role schema for update
 */
export const roleUpdateSchema = roleBaseSchema.partial()

/**
 * Schema for role with user count
 */
export const roleWithUserCountSchema = roleSchema.extend({
    users_count: z.number().optional(),
})

/**
 * Schema for role permission assignment
 */
export const rolePermissionAssignmentSchema = z.object({
    permissions: z.array(z.number()),
}) 