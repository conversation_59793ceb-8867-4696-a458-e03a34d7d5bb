import React, { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Loader2,
  Search,
  MoreVertical,
  Plus,
  Edit,
  Trash,
  FileText,
  Star,
  Copy,
  Eye
} from "lucide-react"
import { templateService } from "@/utils/template-service"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { TemplateTester } from "@/components/templates/template-tester"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AdminLayout } from "@/components/admin-layout"
import { PageHeader } from "@/components/page-header"

interface Template {
  id: number
  name: string
  description: string
  category: string
  content: string
  version: number
  is_default: boolean
  status: string
  variables: string[]
  created_at: string
  updated_at: string
}

export default function TemplatesPage() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState<boolean>(true)
  const [templates, setTemplates] = useState<Template[]>([])
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
  const [showDeleteAlert, setShowDeleteAlert] = useState<boolean>(false)
  const [showViewDialog, setShowViewDialog] = useState<boolean>(false)

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    setLoading(true)
    try {
      const response = await templateService.getTemplates()
      if (response) {
        setTemplates(response as Template[])
      }
    } catch (error) {
      console.error("Error loading templates:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load templates. Please try again."
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleEdit = (template: Template) => {
    navigate(`/templates/${template.id}`)
  }

  const handleView = (template: Template) => {
    setSelectedTemplate(template)
    setShowViewDialog(true)
  }

  const handleDelete = (template: Template) => {
    setSelectedTemplate(template)
    setShowDeleteAlert(true)
  }

  const confirmDelete = async () => {
    if (!selectedTemplate) return

    setLoading(true)
    try {
      await templateService.deleteTemplate(selectedTemplate.id)
      toast({
        title: "Success",
        description: "Template deleted successfully."
      })
      loadTemplates()
    } catch (error) {
      console.error("Error deleting template:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete template. Please try again."
      })
    } finally {
      setLoading(false)
      setShowDeleteAlert(false)
    }
  }

  const handleSetDefault = async (template: Template) => {
    setLoading(true)
    try {
      await templateService.setDefaultTemplate(template.id)
      toast({
        title: "Success",
        description: `${template.name} set as default template.`
      })
      loadTemplates()
    } catch (error) {
      console.error("Error setting default template:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to set default template. Please try again."
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDuplicate = async (template: Template) => {
    setLoading(true)
    try {
      const data = {
        ...template,
        name: `${template.name} (Copy)`,
        is_default: false
      }
      delete data.id
      delete data.created_at
      delete data.updated_at

      await templateService.createTemplate(data)
      toast({
        title: "Success",
        description: "Template duplicated successfully."
      })
      loadTemplates()
    } catch (error) {
      console.error("Error duplicating template:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to duplicate template. Please try again."
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    navigate("/templates/new")
  }

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <AdminLayout>
      <div className="w-full">
        <PageHeader
          title="Prompt Templates"
          description="Manage your prompt templates for AI models"
          actions={
            <Button className="flex items-center gap-1" onClick={handleCreateTemplate}>
              <Plus className="h-4 w-4" />
              Create Template
            </Button>
          }
        />

        <div className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div className="relative max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search templates..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading && templates.length === 0 ? (
                <div className="flex justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Variables</TableHead>
                      <TableHead>Version</TableHead>
                      <TableHead>Updated</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTemplates.length > 0 ? (
                      filteredTemplates.map((template) => (
                        <TableRow key={template.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              {template.name}
                              {template.is_default && (
                                <Badge variant="secondary" className="ml-1">Default</Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-500 mt-1 truncate max-w-xs">
                              {template.description}
                            </p>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{template.category}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                template.status === 'active'
                                  ? 'success'
                                  : template.status === 'draft'
                                    ? 'outline'
                                    : 'secondary'
                              }
                            >
                              {template.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1 max-w-xs">
                              {template.variables && template.variables.length > 0 ? (
                                <Badge variant="outline">
                                  {template.variables.length} vars
                                </Badge>
                              ) : (
                                <span className="text-gray-500 text-xs">None</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>v{template.version}</TableCell>
                          <TableCell>
                            {new Date(template.updated_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => handleView(template)}
                                  className="cursor-pointer"
                                >
                                  <Eye className="mr-2 h-4 w-4" />
                                  View & Test
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleEdit(template)}
                                  className="cursor-pointer"
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDuplicate(template)}
                                  className="cursor-pointer"
                                >
                                  <Copy className="mr-2 h-4 w-4" />
                                  Duplicate
                                </DropdownMenuItem>
                                {!template.is_default && (
                                  <DropdownMenuItem
                                    onClick={() => handleSetDefault(template)}
                                    className="cursor-pointer"
                                  >
                                    <Star className="mr-2 h-4 w-4" />
                                    Set as Default
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDelete(template)}
                                  className="cursor-pointer text-destructive"
                                >
                                  <Trash className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                          {searchQuery ? (
                            <>No templates found matching &ldquo;{searchQuery}&rdquo;</>
                          ) : (
                            <>No templates found. Create your first template.</>
                          )}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            <CardFooter className="flex justify-between text-sm text-gray-500 pt-2">
              <div>
                Total: {filteredTemplates.length} templates
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* View Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-5xl">
          {selectedTemplate && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {selectedTemplate.name}
                  </div>
                  {selectedTemplate.is_default && (
                    <Badge variant="secondary">Default</Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  {selectedTemplate.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="preview">
                  <TabsList className="mb-4">
                    <TabsTrigger value="preview">Template Preview</TabsTrigger>
                    <TabsTrigger value="test">Test Template</TabsTrigger>
                    <TabsTrigger value="details">Template Details</TabsTrigger>
                  </TabsList>

                  <TabsContent value="preview">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium mb-2">Template Content</h3>
                        <div className="p-4 border rounded-md bg-gray-50 whitespace-pre-wrap font-mono text-sm">
                          {selectedTemplate.content}
                        </div>
                      </div>

                      {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                        <div>
                          <h3 className="text-lg font-medium mb-2">Variables</h3>
                          <div className="flex flex-wrap gap-2">
                            {selectedTemplate.variables.map((variable) => (
                              <Badge key={variable} variant="secondary">
                                {variable}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="test">
                    <TemplateTester
                      templateId={selectedTemplate.id}
                      templateContent={selectedTemplate.content}
                      variables={selectedTemplate.variables}
                    />
                  </TabsContent>

                  <TabsContent value="details">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-lg font-medium mb-2">Template Info</h3>
                        <div className="p-4 border rounded-md space-y-2">
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Category:</div>
                            <div className="text-sm col-span-2">
                              <Badge variant="outline">{selectedTemplate.category}</Badge>
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Status:</div>
                            <div className="text-sm col-span-2">
                              <Badge
                                variant={
                                  selectedTemplate.status === 'active'
                                    ? 'success'
                                    : selectedTemplate.status === 'draft'
                                      ? 'outline'
                                      : 'secondary'
                                }
                              >
                                {selectedTemplate.status}
                              </Badge>
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Version:</div>
                            <div className="text-sm col-span-2">v{selectedTemplate.version}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Created:</div>
                            <div className="text-sm col-span-2">
                              {new Date(selectedTemplate.created_at).toLocaleString()}
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Updated:</div>
                            <div className="text-sm col-span-2">
                              {new Date(selectedTemplate.updated_at).toLocaleString()}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-medium mb-2">Statistics</h3>
                        <div className="p-4 border rounded-md space-y-2">
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Variables:</div>
                            <div className="text-sm col-span-2">
                              {selectedTemplate.variables?.length || 0} variables
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Content Length:</div>
                            <div className="text-sm col-span-2">
                              {selectedTemplate.content.length} characters
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Word Count:</div>
                            <div className="text-sm col-span-2">
                              {selectedTemplate.content.split(/\s+/).length} words
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowViewDialog(false)}
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setShowViewDialog(false)
                    handleEdit(selectedTemplate)
                  }}
                >
                  Edit Template
                </Button>
              </CardFooter>
            </Card>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Alert */}
      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the template &ldquo;{selectedTemplate?.name}&rdquo;.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  )
}
