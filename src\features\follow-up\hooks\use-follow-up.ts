/**
 * useFollowUp Hook
 * 
 * Custom hook for follow-up management
 */

import { useState, useCallback } from 'react'
import { followUpService } from '../api/follow-up-service'
import {
    FollowUpChain,
    FollowUpQuestion,
    FollowUpResponse,
    FollowUpFilters,
    FollowUpResult
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useFollowUp() {
    const [chains, setChains] = useState<FollowUpChain[]>([])
    const [currentChain, setCurrentChain] = useState<FollowUpChain | null>(null)
    const [responses, setResponses] = useState<FollowUpResponse[]>([])
    const [results, setResults] = useState<FollowUpResult | null>(null)
    const [generatedQuestions, setGeneratedQuestions] = useState<FollowUpQuestion[]>([])
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isSaving, setIsSaving] = useState<boolean>(false)
    const [isGenerating, setIsGenerating] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch all follow-up chains
     */
    const fetchChains = useCallback(async (filters?: FollowUpFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedChains = await followUpService.getFollowUpChains(filters)
            setChains(fetchedChains)
            return fetchedChains
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single follow-up chain
     */
    const fetchChain = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const chain = await followUpService.getFollowUpChain(id)
            setCurrentChain(chain)
            return chain
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new follow-up chain
     */
    const createChain = useCallback(async (chain: Omit<FollowUpChain, 'id'>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newChain = await followUpService.createFollowUpChain(chain)
            setChains(prev => [...prev, newChain])
            setCurrentChain(newChain)
            return newChain
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a follow-up chain
     */
    const updateChain = useCallback(async (id: string, chain: Partial<FollowUpChain>) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedChain = await followUpService.updateFollowUpChain(id, chain)
            setChains(prev => prev.map(c => c.id === id ? updatedChain : c))

            if (currentChain?.id === id) {
                setCurrentChain(updatedChain)
            }

            return updatedChain
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentChain])

    /**
     * Delete a follow-up chain
     */
    const deleteChain = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await followUpService.deleteFollowUpChain(id)
            setChains(prev => prev.filter(c => c.id !== id))

            if (currentChain?.id === id) {
                setCurrentChain(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentChain])

    /**
     * Toggle follow-up chain status
     */
    const toggleChainStatus = useCallback(async (id: string, isActive: boolean) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedChain = await followUpService.toggleFollowUpChainStatus(id, isActive)
            setChains(prev => prev.map(c => c.id === id ? updatedChain : c))

            if (currentChain?.id === id) {
                setCurrentChain(updatedChain)
            }

            return updatedChain
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentChain])

    /**
     * Submit a follow-up response
     */
    const submitResponse = useCallback(async (response: Omit<FollowUpResponse, 'id'>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newResponse = await followUpService.submitFollowUpResponse(response)
            setResponses(prev => [...prev, newResponse])
            return newResponse
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Fetch responses for a chain
     */
    const fetchResponses = useCallback(async (chainId: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedResponses = await followUpService.getFollowUpResponses(chainId)
            setResponses(fetchedResponses)
            return fetchedResponses
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch results for a chain
     */
    const fetchResults = useCallback(async (chainId: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedResults = await followUpService.getFollowUpResults(chainId)
            setResults(fetchedResults)
            return fetchedResults
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Generate follow-up questions
     */
    const generateQuestions = useCallback(async (prompt: string, count: number = 3) => {
        setIsGenerating(true)
        setError(null)

        try {
            const questions = await followUpService.generateFollowUpQuestions(prompt, count)
            setGeneratedQuestions(questions)
            return questions
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsGenerating(false)
        }
    }, [])

    /**
     * Get relevant follow-ups for a conversation
     */
    const getRelevantFollowUps = useCallback(async (conversationContext: string) => {
        setIsLoading(true)
        setError(null)

        try {
            return await followUpService.getRelevantFollowUps(conversationContext)
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Clear current chain
     */
    const clearCurrentChain = useCallback(() => {
        setCurrentChain(null)
    }, [])

    /**
     * Clear error
     */
    const clearError = useCallback(() => {
        setError(null)
    }, [])

    return {
        chains,
        currentChain,
        responses,
        results,
        generatedQuestions,
        isLoading,
        isSaving,
        isGenerating,
        error,
        fetchChains,
        fetchChain,
        createChain,
        updateChain,
        deleteChain,
        toggleChainStatus,
        submitResponse,
        fetchResponses,
        fetchResults,
        generateQuestions,
        getRelevantFollowUps,
        clearCurrentChain,
        clearError
    }
} 