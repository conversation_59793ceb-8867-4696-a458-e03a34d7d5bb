/**
 * useRoles Hook
 * 
 * Custom hook for role management
 */

import { useState, useCallback } from 'react'
import { roleService } from '../api/role-service'
import {
    Role,
    RoleCreateData,
    RoleUpdateData,
    RoleWithUserCount
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useRoles() {
    const [roles, setRoles] = useState<RoleWithUserCount[]>([])
    const [currentRole, setCurrentRole] = useState<Role | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isProcessing, setIsProcessing] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch all roles
     */
    const fetchRoles = useCallback(async () => {
        setIsLoading(true)
        setError(null)

        try {
            const response = await roleService.getAllRoles()
            setRoles(response.data || [])
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a role by ID
     */
    const fetchRole = useCallback(async (id: number) => {
        setIsLoading(true)
        setError(null)

        try {
            const response = await roleService.getRole(id)
            setCurrentRole(response.data)
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new role
     */
    const createRole = useCallback(async (roleData: RoleCreateData) => {
        setIsProcessing(true)
        setError(null)

        try {
            const response = await roleService.createRole(roleData)
            setRoles(prev => [...prev, response.data])
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsProcessing(false)
        }
    }, [])

    /**
     * Update a role
     */
    const updateRole = useCallback(async (id: number, roleData: RoleUpdateData) => {
        setIsProcessing(true)
        setError(null)

        try {
            const response = await roleService.updateRole(id, roleData)

            // Update roles list
            setRoles(prev => prev.map(role =>
                role.id === id ? { ...role, ...response.data } : role
            ))

            // Update current role if it's the one being edited
            if (currentRole?.id === id) {
                setCurrentRole(response.data)
            }

            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsProcessing(false)
        }
    }, [currentRole])

    /**
     * Delete a role
     */
    const deleteRole = useCallback(async (id: number) => {
        setIsProcessing(true)
        setError(null)

        try {
            const result = await roleService.deleteRole(id)

            // If deletion was successful, remove from local state
            if (result.success) {
                setRoles(prev => prev.filter(role => role.id !== id))

                // If the deleted role was the current role, clear current role
                if (currentRole?.id === id) {
                    setCurrentRole(null)
                }
            }

            return result.success
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsProcessing(false)
        }
    }, [currentRole])

    /**
     * Assign permissions to a role
     */
    const assignPermissions = useCallback(async (roleId: number, permissions: number[]) => {
        setIsProcessing(true)
        setError(null)

        try {
            const result = await roleService.assignPermissions(roleId, permissions)
            return result.success
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsProcessing(false)
        }
    }, [])

    /**
     * Set a role as the current role
     */
    const selectRole = useCallback((role: Role) => {
        setCurrentRole(role)
    }, [])

    /**
     * Clear the current role
     */
    const clearCurrentRole = useCallback(() => {
        setCurrentRole(null)
    }, [])

    return {
        roles,
        currentRole,
        isLoading,
        isProcessing,
        error,
        fetchRoles,
        fetchRole,
        createRole,
        updateRole,
        deleteRole,
        assignPermissions,
        selectRole,
        clearCurrentRole
    }
} 