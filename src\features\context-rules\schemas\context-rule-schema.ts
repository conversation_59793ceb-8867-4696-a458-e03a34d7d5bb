/**
 * Context Rule Schemas
 * 
 * This file contains Zod validation schemas for context rules functionality
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Context condition schema
 */
export const contextConditionSchema = z.object({
    type: z.enum(['message', 'metadata', 'intent', 'entity', 'session', 'custom']),
    field: z.string().min(1, 'Field name is required'),
    operator: z.enum(['equals', 'contains', 'startsWith', 'endsWith', 'exists', 'greaterThan', 'lessThan', 'regex']),
    value: z.union([z.string(), z.number(), z.boolean()]),
    isNegated: z.boolean().default(false),
});

/**
 * Context action schema
 */
export const contextActionSchema = z.object({
    type: z.enum(['set', 'append', 'remove', 'increment', 'decrement', 'clear']),
    field: z.string().min(1, 'Field name is required'),
    value: z.union([z.string(), z.number(), z.boolean(), z.array(z.any())]).optional(),
    isTransient: z.boolean().default(false),
    description: z.string().optional(),
});

/**
 * Context rule schema
 */
export const contextRuleSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    priority: z.number().int().min(0).max(100).default(10),
    isActive: z.boolean().default(true),
    conditions: z.array(contextConditionSchema).min(1, 'At least one condition is required'),
    actions: z.array(contextActionSchema).min(1, 'At least one action is required'),
    scope: z.enum(['global', 'user', 'session', 'conversation']).default('conversation'),
    expirySeconds: z.number().int().nonnegative().optional(),
    tags: z.array(z.string()).optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Context match result schema
 */
export const contextMatchResultSchema = z.object({
    ruleId: z.string(),
    matched: z.boolean(),
    appliedActions: z.array(z.object({
        actionId: z.string(),
        field: z.string(),
        success: z.boolean(),
        error: z.string().optional(),
    })).optional(),
    timestamp: z.date().default(() => new Date()),
});

/**
 * Context data schema
 */
export const contextDataSchema = z.record(z.string(), z.any());

export default {
    contextConditionSchema,
    contextActionSchema,
    contextRuleSchema,
    contextMatchResultSchema,
    contextDataSchema,
}; 