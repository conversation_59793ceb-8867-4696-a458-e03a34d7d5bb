import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Palette, MessageSquare, Type, Wand2, Sun, Moon, Monitor, Image, Settings, AlertTriangle } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import ColorPicker from '@/components/widget-builder/ColorPicker';
import { useLocalTheme } from './LocalThemeProvider';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface QuickSettingsProps {
  form: UseFormReturn<any>;
  widgetId?: string;
  onOpenFeatureModal?: (modalType: string) => void;
  showErrorHighlight?: boolean;
}

const colorPresets = [
  "#7E69AB", "#3B82F6", "#10B981", "#F59E0B",
  "#EF4444", "#8B5CF6", "#06B6D4", "#84CC16"
];

const themePresets = [
  { id: "modern", name: "Modern", colors: { primary: "#7E69AB", secondary: "#ffffff" } },
  { id: "glass", name: "Glass", colors: { primary: "#6366f1", secondary: "#f8fafc" } },
  { id: "dark", name: "Dark", colors: { primary: "#1f2937", secondary: "#111827" } },
  { id: "rounded", name: "Rounded", colors: { primary: "#10b981", secondary: "#ecfdf5" } },
  { id: "minimal", name: "Minimal", colors: { primary: "#6b7280", secondary: "#f9fafb" } },
];

/**
 * Quick Settings Component
 *
 * Provides immediate access to the most commonly changed settings:
 * - Widget name and welcome message
 * - Color customization with presets
 * - Quick theme switching
 * - Logo management (opens advanced modal)
 */
const QuickSettings = ({ form, onOpenFeatureModal, showErrorHighlight }: QuickSettingsProps) => {
  const { theme, setTheme } = useLocalTheme();

  // Watch for form errors to highlight cards
  const formErrors = form.formState.errors;
  const hasBasicInfoErrors = !!(formErrors.name || formErrors.welcomeMessage || formErrors.botName || formErrors.placeholderText);
  const hasColorErrors = !!(formErrors.primaryColor || formErrors.secondaryColor);

  const applyThemePreset = (themePreset: typeof themePresets[0]) => {
    form.setValue('theme', themePreset.id);
    form.setValue('primaryColor', themePreset.colors.primary);
    form.setValue('secondaryColor', themePreset.colors.secondary);

    // Clear color validation errors when applying preset
    form.clearErrors('primaryColor');
    form.clearErrors('secondaryColor');
  };

  const generateColorScheme = () => {
    const randomPreset = themePresets[Math.floor(Math.random() * themePresets.length)];
    applyThemePreset(randomPreset);
  };

  const getThemeIcon = (currentTheme: string) => {
    switch (currentTheme) {
      case 'light':
        return <Sun className="w-4 h-4" />;
      case 'dark':
        return <Moon className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  // Helper function to get card error styling with blinking effect
  const getCardErrorClass = (hasError: boolean) => {
    if (!hasError) return "";

    return cn(
      "border-red-200 bg-red-50/50 ring-1 ring-red-200",
      showErrorHighlight && "animate-pulse ring-2 ring-red-500 bg-red-100/70"
    );
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card className={cn("transition-all duration-200", getCardErrorClass(hasBasicInfoErrors))}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg">
            <MessageSquare className={cn("w-5 h-5 mr-2", hasBasicInfoErrors ? "text-red-600" : "text-blue-600")} />
            Basic Information
            {hasBasicInfoErrors && (
              <Badge variant="destructive" className="ml-2 text-xs">
                <AlertTriangle className="w-3 h-3 mr-1" />
                Required
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {hasBasicInfoErrors && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please fill in all required fields to continue. Widget name and welcome message are mandatory.
              </AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center">
                    Widget Name
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="My Chat Widget"
                      {...field}
                      className={cn(
                        "focus:ring-2 focus:ring-blue-500",
                        formErrors.name && "border-red-500 focus:ring-red-500"
                      )}
                      data-invalid={!!formErrors.name}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600 text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="welcomeMessage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center">
                    Welcome Message
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Hello! How can I help you today?"
                      rows={3}
                      {...field}
                      className={cn(
                        "focus:ring-2 focus:ring-blue-500 resize-none",
                        formErrors.welcomeMessage && "border-red-500 focus:ring-red-500"
                      )}
                      data-invalid={!!formErrors.welcomeMessage}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600 text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="botName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center">
                    Bot Name
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="AI Assistant"
                      {...field}
                      className={cn(
                        "focus:ring-2 focus:ring-blue-500",
                        formErrors.botName && "border-red-500 focus:ring-red-500"
                      )}
                      data-invalid={!!formErrors.botName}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600 text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="placeholderText"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center">
                    Placeholder Text
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Type your message..."
                      {...field}
                      className={cn(
                        "focus:ring-2 focus:ring-blue-500",
                        formErrors.placeholderText && "border-red-500 focus:ring-red-500"
                      )}
                      data-invalid={!!formErrors.placeholderText}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600 text-xs" />
                </FormItem>
              )}
            />
          </Form>
        </CardContent>
      </Card>

      {/* Builder Theme Toggle */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg">
            {getThemeIcon(theme)}
            <span className="ml-2">Builder Theme</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Choose your preferred interface theme. This affects the builder interface, not your widget design.
            </p>
            <div className="grid grid-cols-3 gap-2">
              {[
                { id: 'light', name: 'Light', icon: Sun },
                { id: 'dark', name: 'Dark', icon: Moon },
                { id: 'system', name: 'System', icon: Monitor },
              ].map((themeOption) => {
                const Icon = themeOption.icon;
                return (
                  <Button
                    key={themeOption.id}
                    variant={theme === themeOption.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setTheme(themeOption.id as any)}
                    className={`
                      flex flex-col items-center space-y-1 h-auto py-3
                      ${theme === themeOption.id ? 'bg-blue-600 text-white' : ''}
                    `}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-xs">{themeOption.name}</span>
                  </Button>
                );
              })}
            </div>
            {theme === 'system' && (
              <p className="text-xs text-muted-foreground">
                Automatically matches your system preference
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Widget Theme Presets */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center text-lg">
              <Type className="w-5 h-5 mr-2 text-purple-600" />
              Widget Themes
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={generateColorScheme}
              className="text-xs"
            >
              <Wand2 className="w-3 h-3 mr-1" />
              Random
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Choose a visual style for your chat widget. This affects how your widget appears to visitors.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {themePresets.map((theme) => (
                <Button
                  key={theme.id}
                  variant="outline"
                  className={`
                  h-auto p-3 flex flex-col items-center space-y-2 hover:shadow-md transition-all
                  ${form.watch('theme') === theme.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''}
                `}
                  onClick={() => applyThemePreset(theme)}
                >
                  <div className="flex space-x-1">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: theme.colors.primary }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: theme.colors.secondary }}
                    />
                  </div>
                  <span className="text-xs font-medium">{theme.name}</span>
                  {form.watch('theme') === theme.id && (
                    <Badge variant="secondary" className="text-xs">
                      Active
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Color Customization */}
      <Card className={cn("transition-all duration-200", getCardErrorClass(hasColorErrors))}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg">
            <Palette className={cn("w-5 h-5 mr-2", hasColorErrors ? "text-red-600" : "text-green-600")} />
            Colors
            {hasColorErrors && (
              <Badge variant="destructive" className="ml-2 text-xs">
                <AlertTriangle className="w-3 h-3 mr-1" />
                Invalid
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {hasColorErrors && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please provide valid hex colors (e.g., #7E69AB). Use the color picker or enter valid hex values.
              </AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="primaryColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium flex items-center">
                      Primary Color
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <div className={cn(
                      "rounded-md transition-all",
                      formErrors.primaryColor && "ring-2 ring-red-500 ring-opacity-50"
                    )}>
                      <ColorPicker
                        color={field.value}
                        onChange={(color) => {
                          field.onChange(color);
                          form.clearErrors('primaryColor');
                        }}
                        presets={colorPresets}
                        className="w-full"
                      />
                    </div>
                    <FormMessage className="text-red-600 text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="secondaryColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium flex items-center">
                      Background Color
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <div className={cn(
                      "rounded-md transition-all",
                      formErrors.secondaryColor && "ring-2 ring-red-500 ring-opacity-50"
                    )}>
                      <ColorPicker
                        color={field.value}
                        onChange={(color) => {
                          field.onChange(color);
                          form.clearErrors('secondaryColor');
                        }}
                        presets={["#ffffff", "#f8fafc", "#f1f5f9", "#e2e8f0"]}
                        className="w-full"
                      />
                    </div>
                    <FormMessage className="text-red-600 text-xs" />
                  </FormItem>
                )}
              />
            </div>

            {/* Color Preset Quick Actions */}
            <div className="pt-2">
              <p className="text-xs text-gray-600 mb-2">Quick color combinations:</p>
              <div className="flex flex-wrap gap-2">
                {[
                  { name: "Blue", primary: "#3B82F6", secondary: "#EFF6FF" },
                  { name: "Green", primary: "#10B981", secondary: "#ECFDF5" },
                  { name: "Purple", primary: "#8B5CF6", secondary: "#F3E8FF" },
                  { name: "Orange", primary: "#F59E0B", secondary: "#FFFBEB" },
                ].map((combo) => (
                  <Button
                    key={combo.name}
                    variant="outline"
                    size="sm"
                    className="h-8 px-3 text-xs"
                    onClick={() => {
                      form.setValue('primaryColor', combo.primary);
                      form.setValue('secondaryColor', combo.secondary);
                    }}
                  >
                    <div className="flex items-center space-x-1">
                      <div
                        className="w-3 h-3 rounded-full border"
                        style={{ backgroundColor: combo.primary }}
                      />
                      <span>{combo.name}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          </Form>
        </CardContent>
      </Card>

      {/* Logo Management - Simple Button to Open Advanced Modal */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg">
            <Image className="w-5 h-5 mr-2 text-orange-600" />
            Widget Logo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Add a custom logo to your widget to strengthen your brand identity.
            </p>
            <Button
              variant="outline"
              onClick={() => onOpenFeatureModal?.('logoUpload')}
              className="w-full flex items-center justify-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>Manage Logo</span>
            </Button>
            {form.watch('advanced.logoUrl') ? (
              <div className="flex items-center space-x-2 text-sm text-green-600 bg-green-50 p-2 rounded-md border border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span>✅ Logo configured</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-sm text-amber-600 bg-amber-50 p-2 rounded-md border border-amber-200">
                <div className="w-2 h-2 bg-amber-500 rounded-full" />
                <span>⚙️ Click to upload logo</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuickSettings;
