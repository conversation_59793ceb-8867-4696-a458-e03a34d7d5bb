/**
 * Permission API Service
 * 
 * This file contains all API calls related to permission management
 */

import axios from 'axios'
import { API_BASE_URL } from '@/lib/constants'
import {
    Permission,
    PermissionCreateData,
    PermissionUpdateData
} from '../types'
import { tryCatch } from '@/lib/error-handler'

/**
 * Base API endpoint for permissions
 */
const PERMISSIONS_API = `${API_BASE_URL}/permissions`

/**
 * Permission API Service
 */
export const permissionService = {
    /**
     * Get all permissions
     */
    getAllPermissions: async (): Promise<{ data: Permission[] }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(PERMISSIONS_API)
        }, 'getAllPermissions')

        if (error || !response) {
            throw error || new Error('Failed to fetch permissions')
        }

        return response.data
    },

    /**
     * Get a specific permission by ID
     */
    getPermission: async (id: number): Promise<{ data: Permission }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${PERMISSIONS_API}/${id}`)
        }, 'getPermission')

        if (error || !response) {
            throw error || new Error(`Failed to fetch permission with ID ${id}`)
        }

        return response.data
    },

    /**
     * Create a new permission
     */
    createPermission: async (permissionData: PermissionCreateData): Promise<{ data: Permission }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(PERMISSIONS_API, permissionData)
        }, 'createPermission')

        if (error || !response) {
            throw error || new Error('Failed to create permission')
        }

        return response.data
    },

    /**
     * Update an existing permission
     */
    updatePermission: async (id: number, permissionData: PermissionUpdateData): Promise<{ data: Permission }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${PERMISSIONS_API}/${id}`, permissionData)
        }, 'updatePermission')

        if (error || !response) {
            throw error || new Error(`Failed to update permission with ID ${id}`)
        }

        return response.data
    },

    /**
     * Delete a permission
     */
    deletePermission: async (id: number): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${PERMISSIONS_API}/${id}`)
        }, 'deletePermission')

        if (error || !response) {
            throw error || new Error(`Failed to delete permission with ID ${id}`)
        }

        return { success: true }
    },

    /**
     * Get permissions by category
     */
    getPermissionsByCategory: async (category: string): Promise<{ data: Permission[] }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${PERMISSIONS_API}/category/${category}`)
        }, 'getPermissionsByCategory')

        if (error || !response) {
            throw error || new Error(`Failed to fetch permissions for category ${category}`)
        }

        return response.data
    }
} 