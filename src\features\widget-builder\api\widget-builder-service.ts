/**
 * Widget Builder Service
 *
 * Centralized service for widget builder functionality.
 * Handles API interactions, configuration management, and business logic
 * for widget features like webhooks, integrations, AI models, etc.
 */

import api from '@/utils/api'
import { WebhookConfig, IntegrationConfig, AIModelConfig, PreChatFormConfig, PostChatSurveyConfig, DomainRestrictionConfig, MobileOptimizationConfig, ConversationPersistenceConfig, CustomCSSConfig, LogoUploadConfig, Widget, AnalyticsSummary } from '../types'

export const widgetBuilderService = {
  /**
   * Basic Widget CRUD Operations
   */
  getAllWidgets: async (): Promise<Widget[]> => {
    try {
      const response = await api.get('widgets')
      return response.data
    } catch (error) {
      console.error('Failed to fetch widgets:', error)
      throw error
    }
  },

  getWidget: async (id: number): Promise<Widget> => {
    try {
      const response = await api.get(`widgets/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch widget:', error)
      throw error
    }
  },

  createWidget: async (widgetData: Partial<Widget>): Promise<Widget> => {
    try {
      const response = await api.post('widgets', widgetData)
      return response.data
    } catch (error) {
      console.error('Failed to create widget:', error)
      throw error
    }
  },

  updateWidget: async (id: number, widgetData: Partial<Widget>): Promise<Widget> => {
    try {
      const response = await api.put(`widgets/${id}`, widgetData)
      return response.data
    } catch (error) {
      console.error('Failed to update widget:', error)
      throw error
    }
  },

  deleteWidget: async (id: number): Promise<void> => {
    try {
      await api.delete(`widgets/${id}`)
    } catch (error) {
      console.error('Failed to delete widget:', error)
      throw error
    }
  },

  getWidgetByPublicId: async (widgetId: string): Promise<Widget> => {
    try {
      const response = await api.get(`widgets/public/${widgetId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch widget by public ID:', error)
      throw error
    }
  },

  /**
   * Analytics Operations
   */
  getAnalyticsSummary: async (widgetId: number, period: 'day' | 'week' | 'month' | 'all' = 'month'): Promise<AnalyticsSummary> => {
    try {
      const response = await api.get(`widgets/${widgetId}/analytics/summary?period=${period}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch analytics summary:', error)
      throw error
    }
  },

  getAnalytics: async (widgetId: number, options: {
    fromDate?: string
    toDate?: string
    groupBy?: 'day' | 'week' | 'month' | 'event_type' | 'url'
  } = {}): Promise<any> => {
    try {
      const params = new URLSearchParams()
      if (options.fromDate) params.append('from_date', options.fromDate)
      if (options.toDate) params.append('to_date', options.toDate)
      if (options.groupBy) params.append('group_by', options.groupBy)

      const response = await api.get(`widgets/${widgetId}/analytics?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
      throw error
    }
  },

  /**
   * Embed Code Operations
   */
  generateEmbedCode: async (widgetId: number, options: {
    type: 'standard' | 'iframe' | 'web-component'
    customizations?: {
      allowed_domains?: string[]
      position_type?: 'fixed' | 'relative' | 'inline'
      csp_enabled?: boolean
      enable_sri?: boolean
      version?: string
    }
  }): Promise<any> => {
    try {
      const response = await api.post('embed-code/generate', {
        widget_id: widgetId,
        type: options.type,
        allowed_domains: options.customizations?.allowed_domains,
        position_type: options.customizations?.position_type,
        enable_sri: options.customizations?.enable_sri,
        csp_enabled: options.customizations?.csp_enabled
      })
      return response.data
    } catch (error) {
      console.error('Failed to generate embed code:', error)
      throw error
    }
  },

  validateDomain: async (widgetId: string, domain: string): Promise<any> => {
    try {
      const response = await api.post('embed-code/validate-domain', {
        widget_id: widgetId,
        domain
      })
      return response.data
    } catch (error) {
      console.error('Failed to validate domain:', error)
      throw error
    }
  },
  /**
   * Webhook Management
   */
  testWebhook: async (widgetId: number, config: WebhookConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/webhooks/test`, config)
      return response.data
    } catch (error) {
      console.error('Webhook test failed:', error)
      throw error
    }
  },

  saveWebhookConfig: async (widgetId: number, config: WebhookConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/webhooks`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save webhook config:', error)
      throw error
    }
  },

  getWebhookConfigs: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/webhooks`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch webhook configs:', error)
      throw error
    }
  },

  /**
   * Integration Management
   */
  saveIntegration: async (widgetId: number, integration: IntegrationConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/integrations`, integration)
      return response.data
    } catch (error) {
      console.error('Failed to save integration:', error)
      throw error
    }
  },

  getIntegrations: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/integrations`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch integrations:', error)
      throw error
    }
  },

  deleteIntegration: async (widgetId: number, integrationId: string) => {
    try {
      const response = await api.delete(`widgets/${widgetId}/integrations/${integrationId}`)
      return response.data
    } catch (error) {
      console.error('Failed to delete integration:', error)
      throw error
    }
  },

  /**
   * AI Model Management
   */
  getAvailableAIModels: async () => {
    try {
      const response = await api.get('ai-models')
      return response.data
    } catch (error) {
      console.error('Failed to fetch AI models:', error)
      throw error
    }
  },

  saveAIModelConfig: async (widgetId: number, config: AIModelConfig) => {
    try {
      const response = await api.put(`widgets/${widgetId}/ai-model`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save AI model config:', error)
      throw error
    }
  },

  /**
   * Pre-Chat Form Management
   */
  savePreChatForm: async (widgetId: number, config: PreChatFormConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/pre-chat-form`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save pre-chat form:', error)
      throw error
    }
  },

  getPreChatForm: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/pre-chat-form`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch pre-chat form:', error)
      throw error
    }
  },

  /**
   * Post-Chat Survey Management
   */
  savePostChatSurvey: async (widgetId: number, config: PostChatSurveyConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/post-chat-survey`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save post-chat survey:', error)
      throw error
    }
  },

  getPostChatSurvey: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/post-chat-survey`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch post-chat survey:', error)
      throw error
    }
  },

  /**
   * Domain Restriction Management
   */
  saveDomainRestrictions: async (widgetId: number, config: DomainRestrictionConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/domain-restrictions`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save domain restrictions:', error)
      throw error
    }
  },

  /**
   * Mobile Optimization Management
   */
  saveMobileOptimization: async (widgetId: number, config: MobileOptimizationConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/mobile-optimization`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save mobile optimization:', error)
      throw error
    }
  },

  /**
   * Conversation Persistence Management
   */
  saveConversationPersistence: async (widgetId: number, config: ConversationPersistenceConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/conversation-persistence`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save conversation persistence:', error)
      throw error
    }
  },

  /**
   * Custom CSS Management
   */
  saveCustomCSS: async (widgetId: number, config: CustomCSSConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/custom-css`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save custom CSS:', error)
      throw error
    }
  },

  /**
   * Logo Upload Management
   */
  uploadLogo: async (widgetId: number, file: File, config: LogoUploadConfig) => {
    try {
      const formData = new FormData()
      formData.append('logo', file)
      formData.append('config', JSON.stringify(config))

      const response = await api.post(`widgets/${widgetId}/logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      return response.data
    } catch (error) {
      console.error('Failed to upload logo:', error)
      throw error
    }
  },

  saveLogoFromUrl: async (widgetId: number, config: LogoUploadConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/logo-url`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save logo from URL:', error)
      throw error
    }
  },

  /**
   * Widget Template Management
   */
  getWidgetTemplates: async () => {
    try {
      const response = await api.get('widget-templates')
      return response.data
    } catch (error) {
      console.error('Failed to fetch widget templates:', error)
      throw error
    }
  },

  applyTemplate: async (widgetId: number, templateId: string) => {
    try {
      const response = await api.post(`widgets/${widgetId}/apply-template`, { templateId })
      return response.data
    } catch (error) {
      console.error('Failed to apply template:', error)
      throw error
    }
  }
}
