/**
 * Widget Builder Service
 * 
 * Centralized service for widget builder functionality.
 * Handles API interactions, configuration management, and business logic
 * for widget features like webhooks, integrations, AI models, etc.
 */

import api from '@/utils/api'
import { WebhookConfig, IntegrationConfig, AIModelConfig, PreChatFormConfig, PostChatSurveyConfig, DomainRestrictionConfig, MobileOptimizationConfig, ConversationPersistenceConfig, CustomCSSConfig, LogoUploadConfig } from '../types'

export const widgetBuilderService = {
  /**
   * Webhook Management
   */
  testWebhook: async (widgetId: number, config: WebhookConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/webhooks/test`, config)
      return response.data
    } catch (error) {
      console.error('Webhook test failed:', error)
      throw error
    }
  },

  saveWebhookConfig: async (widgetId: number, config: WebhookConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/webhooks`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save webhook config:', error)
      throw error
    }
  },

  getWebhookConfigs: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/webhooks`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch webhook configs:', error)
      throw error
    }
  },

  /**
   * Integration Management
   */
  saveIntegration: async (widgetId: number, integration: IntegrationConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/integrations`, integration)
      return response.data
    } catch (error) {
      console.error('Failed to save integration:', error)
      throw error
    }
  },

  getIntegrations: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/integrations`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch integrations:', error)
      throw error
    }
  },

  deleteIntegration: async (widgetId: number, integrationId: string) => {
    try {
      const response = await api.delete(`widgets/${widgetId}/integrations/${integrationId}`)
      return response.data
    } catch (error) {
      console.error('Failed to delete integration:', error)
      throw error
    }
  },

  /**
   * AI Model Management
   */
  getAvailableAIModels: async () => {
    try {
      const response = await api.get('ai-models')
      return response.data
    } catch (error) {
      console.error('Failed to fetch AI models:', error)
      throw error
    }
  },

  saveAIModelConfig: async (widgetId: number, config: AIModelConfig) => {
    try {
      const response = await api.put(`widgets/${widgetId}/ai-model`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save AI model config:', error)
      throw error
    }
  },

  /**
   * Pre-Chat Form Management
   */
  savePreChatForm: async (widgetId: number, config: PreChatFormConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/pre-chat-form`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save pre-chat form:', error)
      throw error
    }
  },

  getPreChatForm: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/pre-chat-form`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch pre-chat form:', error)
      throw error
    }
  },

  /**
   * Post-Chat Survey Management
   */
  savePostChatSurvey: async (widgetId: number, config: PostChatSurveyConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/post-chat-survey`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save post-chat survey:', error)
      throw error
    }
  },

  getPostChatSurvey: async (widgetId: number) => {
    try {
      const response = await api.get(`widgets/${widgetId}/post-chat-survey`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch post-chat survey:', error)
      throw error
    }
  },

  /**
   * Domain Restriction Management
   */
  saveDomainRestrictions: async (widgetId: number, config: DomainRestrictionConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/domain-restrictions`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save domain restrictions:', error)
      throw error
    }
  },

  /**
   * Mobile Optimization Management
   */
  saveMobileOptimization: async (widgetId: number, config: MobileOptimizationConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/mobile-optimization`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save mobile optimization:', error)
      throw error
    }
  },

  /**
   * Conversation Persistence Management
   */
  saveConversationPersistence: async (widgetId: number, config: ConversationPersistenceConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/conversation-persistence`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save conversation persistence:', error)
      throw error
    }
  },

  /**
   * Custom CSS Management
   */
  saveCustomCSS: async (widgetId: number, config: CustomCSSConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/custom-css`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save custom CSS:', error)
      throw error
    }
  },

  /**
   * Logo Upload Management
   */
  uploadLogo: async (widgetId: number, file: File, config: LogoUploadConfig) => {
    try {
      const formData = new FormData()
      formData.append('logo', file)
      formData.append('config', JSON.stringify(config))

      const response = await api.post(`widgets/${widgetId}/logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      
      return response.data
    } catch (error) {
      console.error('Failed to upload logo:', error)
      throw error
    }
  },

  saveLogoFromUrl: async (widgetId: number, config: LogoUploadConfig) => {
    try {
      const response = await api.post(`widgets/${widgetId}/logo-url`, config)
      return response.data
    } catch (error) {
      console.error('Failed to save logo from URL:', error)
      throw error
    }
  },

  /**
   * Widget Template Management
   */
  getWidgetTemplates: async () => {
    try {
      const response = await api.get('widget-templates')
      return response.data
    } catch (error) {
      console.error('Failed to fetch widget templates:', error)
      throw error
    }
  },

  applyTemplate: async (widgetId: number, templateId: string) => {
    try {
      const response = await api.post(`widgets/${widgetId}/apply-template`, { templateId })
      return response.data
    } catch (error) {
      console.error('Failed to apply template:', error)
      throw error
    }
  }
}
