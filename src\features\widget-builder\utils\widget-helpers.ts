/**
 * Widget Helper Utilities
 */

import { WidgetBuilderFormData } from '../types'

// Feature configuration metadata
export const FEATURE_CONFIG = {
  preChat: {
    title: 'Pre-Chat Form',
    description: 'Collect visitor information before starting a chat',
    color: 'blue',
  },
  postChat: {
    title: 'Post-Chat Survey',
    description: 'Gather feedback after chat sessions',
    color: 'green',
  },
  webhooks: {
    title: 'Webhooks & Integrations',
    description: 'Connect with external tools and services',
    color: 'purple',
  },
  userRatings: {
    title: 'User Ratings',
    description: 'Allow visitors to rate their chat experience',
    color: 'orange',
  },
  autoOpen: {
    title: 'Auto Open',
    description: 'Automatically open the widget for visitors',
    color: 'red',
  },
  customCSS: {
    title: 'Custom CSS',
    description: 'Add custom styling to your widget',
    color: 'pink',
  },
  logoUpload: {
    title: 'Logo Upload',
    description: 'Add your brand logo to the widget',
    color: 'cyan',
  },
  domainRestriction: {
    title: 'Domain Restrictions',
    description: 'Control which domains can embed your widget',
    color: 'gray',
  },
  mobileOptimization: {
    title: 'Mobile Optimization',
    description: 'Optimize widget behavior for mobile devices',
    color: 'emerald',
  },
  conversationPersistence: {
    title: 'Conversation Persistence',
    description: 'Remember conversations across sessions',
    color: 'violet',
  },
  analytics: {
    title: 'Analytics',
    description: 'Track and analyze widget performance',
    color: 'teal',
  },
} as const

// Feature dependency checking
export const getFeatureDependencies = (featureName: keyof WidgetBuilderFormData['features']): string[] => {
  const dependencies: Record<string, string[]> = {
    postChat: ['userRatings'], // Post-chat surveys often include ratings
    webhooks: [], // No dependencies
    customCSS: [], // No dependencies
    // Add more dependencies as needed
  }

  return dependencies[featureName] || []
}

export const checkFeatureConflicts = (
  features: WidgetBuilderFormData['features']
): { hasConflicts: boolean; conflicts: string[] } => {
  const conflicts: string[] = []

  // Example conflict: Custom CSS might conflict with theme settings
  if (features.customCSS && features.logoUpload) {
    // This is just an example - adjust based on actual conflicts
    // conflicts.push('Custom CSS may override logo positioning');
  }

  return {
    hasConflicts: conflicts.length > 0,
    conflicts,
  }
}

// Template utilities
export const getRecommendedFeatures = (category: string): (keyof WidgetBuilderFormData['features'])[] => {
  const recommendations: Record<string, (keyof WidgetBuilderFormData['features'])[]> = {
    'lead-generation': ['preChat', 'webhooks', 'analytics'],
    'customer-support': ['postChat', 'userRatings', 'conversationPersistence'],
    'sales': ['preChat', 'webhooks', 'analytics', 'autoOpen'],
    'feedback': ['postChat', 'userRatings', 'analytics'],
    'basic': ['analytics', 'mobileOptimization'],
  }

  return recommendations[category] || []
}

// Performance utilities
export const calculateWidgetComplexity = (formData: WidgetBuilderFormData): 'low' | 'medium' | 'high' => {
  const enabledFeatures = Object.values(formData.features).filter(Boolean).length
  const hasAdvancedFeatures = formData.features.customCSS || formData.features.webhooks

  if (enabledFeatures <= 3 && !hasAdvancedFeatures) return 'low'
  if (enabledFeatures <= 6 || hasAdvancedFeatures) return 'medium'
  return 'high'
}

export const getPerformanceRecommendations = (formData: WidgetBuilderFormData): string[] => {
  const recommendations: string[] = []
  const complexity = calculateWidgetComplexity(formData)

  if (complexity === 'high') {
    recommendations.push('Consider reducing the number of enabled features for better performance')
  }

  if (formData.features.customCSS) {
    recommendations.push('Optimize custom CSS for faster loading')
  }

  if (formData.advanced.integrations?.length > 5) {
    recommendations.push('Too many integrations may slow down the widget')
  }

  return recommendations
}

// Color utilities
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

export const rgbToHex = (r: number, g: number, b: number): string => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

// Widget position utilities
export const getPositionStyles = (position: string) => {
  const positions: Record<string, any> = {
    'bottom-right': { bottom: '20px', right: '20px' },
    'bottom-left': { bottom: '20px', left: '20px' },
    'top-right': { top: '20px', right: '20px' },
    'top-left': { top: '20px', left: '20px' },
    'center': { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }
  }

  return positions[position] || positions['bottom-right']
}
