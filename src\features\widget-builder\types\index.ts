/**
 * Widget Builder Types
 */

// Types for smart widget features
export interface WebhookConfig {
  url: string
  method: 'POST' | 'GET' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  secret?: string
  eventType: 'message_sent' | 'chat_start' | 'chat_end' | 'user_rating' | 'test'
}

export interface IntegrationConfig {
  id: string
  type: string
  name: string
  url: string
  active: boolean
  events: string[]
  secret?: string
  created_at: string
}

export interface AIModelConfig {
  id: number
  name: string
  provider: string
  model: string
  active: boolean
  fallback_models?: number[]
}

export interface PreChatFormConfig {
  enabled: boolean
  title?: string
  description?: string
  buttonText?: string
  required?: boolean
  fields?: Array<{
    id: string
    type: string
    label: string
    required: boolean
    options?: string[]
  }>
}

export interface PostChatSurveyConfig {
  enabled: boolean
  title?: string
  description?: string
  questions?: Array<{
    id: string
    type: string
    question: string
    required: boolean
    options?: string[]
  }>
}

export interface DomainRestrictionConfig {
  enabled: boolean
  allowedDomains: string[]
  blockMode: 'whitelist' | 'blacklist'
}

export interface MobileOptimizationConfig {
  enabled: boolean
  responsiveBreakpoint: number
  mobilePosition: string
  mobileSize: string
  touchOptimized: boolean
}

export interface ConversationPersistenceConfig {
  enabled: boolean
  storageType: 'session' | 'local' | 'server'
  retentionDays: number
  maxMessages: number
}

export interface CustomCSSConfig {
  enabled: boolean
  css: string
  variables?: Record<string, string>
}

export interface LogoUploadConfig {
  enabled: boolean
  logoUrl?: string
  logoFile?: File
  position: 'header' | 'chat' | 'both'
  size: 'small' | 'medium' | 'large'
}

export interface WidgetTemplate {
  id: string
  name: string
  description: string
  category: string
  preview: string
  config: Record<string, any>
}

export interface WidgetBuilderFormData {
  name: string
  description?: string
  primaryColor: string
  secondaryColor: string
  autoOpen: boolean
  userRatings: boolean
  contextRetention: boolean
  enableAnalytics: boolean
  webhooks: WebhookConfig[]
  integrations: IntegrationConfig[]
  aiModel: AIModelConfig
  preChatForm: PreChatFormConfig
  postChatSurvey: PostChatSurveyConfig
  domainRestrictions: DomainRestrictionConfig
  mobileOptimization: MobileOptimizationConfig
  conversationPersistence: ConversationPersistenceConfig
  customCSS: CustomCSSConfig
  logoUpload: LogoUploadConfig
}
