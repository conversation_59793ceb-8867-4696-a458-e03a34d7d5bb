/**
 * useIntegrations Hook
 * Custom hook for managing integrations state and operations
 */

import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { integrationService } from '../api/integration-service';
import { Integration, IntegrationFormData, IntegrationTestResult } from '../types/integration';
import { ControllerRenderProps } from 'react-hook-form';

interface UseIntegrationsProps {
    field: ControllerRenderProps<any, any>;
    webhookUrl?: string;
    widgetId?: string;
}

interface UseIntegrationsReturn {
    integrations: Integration[];
    currentIntegration: Partial<Integration> | null;
    isEditing: boolean;
    integrationTab: string;
    isTestingConnection: boolean;
    testResult: IntegrationTestResult | null;
    handleAddIntegration: (type: Integration['type']) => void;
    handleEditIntegration: (integration: Integration) => void;
    handleDeleteIntegration: (id: string) => void;
    handleToggleActive: (id: string) => void;
    handleSaveIntegration: () => void;
    handleCancelEdit: () => void;
    handleTestConnection: () => Promise<void>;
    handleIntegrationChange: (field: keyof Integration, value: any) => void;
    handleEventToggle: (event: string, checked: boolean) => void;
}

/**
 * Custom hook for managing integrations
 */
export function useIntegrations({
    field,
    webhookUrl,
    widgetId
}: UseIntegrationsProps): UseIntegrationsReturn {
    const [integrations, setIntegrations] = useState<Integration[]>([]);
    const [currentIntegration, setCurrentIntegration] = useState<Partial<Integration> | null>(null);
    const [isEditing, setIsEditing] = useState(false);
    const [integrationTab, setIntegrationTab] = useState('slack');
    const [isTestingConnection, setIsTestingConnection] = useState(false);
    const [testResult, setTestResult] = useState<IntegrationTestResult | null>(null);

    // Initialize with any existing webhookUrl or field value
    useEffect(() => {
        const initialIntegrations = [];

        if (webhookUrl && !integrations.length) {
            // Convert legacy webhookUrl to generic integration
            initialIntegrations.push({
                id: 'legacy',
                type: 'generic',
                name: 'Legacy Webhook',
                url: webhookUrl,
                active: true,
                events: ['message.new', 'rating.submit', 'session.start', 'session.end'],
                created_at: new Date().toISOString()
            });
        }

        // Load integrations from field.value if it exists and is properly formatted
        if (field.value && Array.isArray(field.value) && field.value.length > 0) {
            setIntegrations(field.value);
            return;
        }

        // Only set state if we actually have initial integrations
        if (initialIntegrations.length > 0) {
            setIntegrations(initialIntegrations);
        }
    }, [webhookUrl, field.value]);

    // Update form field when integrations change
    useEffect(() => {
        field.onChange(integrations);
    }, [integrations, field]);

    // Get default name for integration type
    const getDefaultNameForType = useCallback((type: Integration['type']) => {
        switch (type) {
            case 'slack': return 'Slack Channel';
            case 'ms-teams': return 'Teams Channel';
            case 'discord': return 'Discord Channel';
            case 'zapier': return 'Zapier Integration';
            case 'generic': return 'Webhook Endpoint';
            default: return 'New Integration';
        }
    }, []);

    // Add new integration
    const handleAddIntegration = useCallback((type: Integration['type']) => {
        setCurrentIntegration({
            id: `${type}-${Date.now()}`,
            type,
            name: getDefaultNameForType(type),
            url: '',
            active: true,
            events: ['message.new', 'rating.submit']
        });
        setIntegrationTab(type);
        setIsEditing(true);
    }, [getDefaultNameForType]);

    // Edit existing integration
    const handleEditIntegration = useCallback((integration: Integration) => {
        setCurrentIntegration(integration);
        setIntegrationTab(integration.type);
        setIsEditing(true);
    }, []);

    // Delete integration
    const handleDeleteIntegration = useCallback((id: string) => {
        const integration = integrations.find(i => i.id === id);
        setIntegrations(prev => prev.filter(i => i.id !== id));
        toast.success("Integration Removed", {
            description: "The integration has been removed successfully."
        });

        if (integration) {
            // Track deletion event if analytics is available
            if (typeof window !== 'undefined' && window.gtag) {
                window.gtag('event', 'integration_deleted', {
                    type: integration.type
                });
            }
        }
    }, [integrations]);

    // Toggle integration active status
    const handleToggleActive = useCallback((id: string) => {
        setIntegrations(prev => {
            const newIntegrations = prev.map(i => {
                if (i.id === id) {
                    const newStatus = !i.active;
                    // Track status change event if analytics is available
                    if (typeof window !== 'undefined' && window.gtag) {
                        window.gtag(newStatus ? 'integration_enabled' : 'integration_disabled', {
                            type: i.type
                        });
                    }
                    return { ...i, active: newStatus };
                }
                return i;
            });
            return newIntegrations;
        });
    }, []);

    // Save integration
    const handleSaveIntegration = useCallback(() => {
        if (!currentIntegration || !currentIntegration.url) {
            toast.error("Error", {
                description: "URL is required for all integrations"
            });
            return;
        }

        const integration = {
            ...currentIntegration,
            created_at: currentIntegration.created_at || new Date().toISOString()
        } as Integration;

        // Update existing or add new
        const exists = integrations.some(i => i.id === integration.id);

        if (exists) {
            setIntegrations(prev => prev.map(i => i.id === integration.id ? integration : i));
            toast.success("Integration Updated", {
                description: `${integration.name} has been updated successfully.`
            });
            // Track update event if analytics is available
            if (typeof window !== 'undefined' && window.gtag) {
                window.gtag('event', 'integration_updated', {
                    type: integration.type,
                    event_count: integration.events.length
                });
            }
        } else {
            setIntegrations(prev => [...prev, integration]);
            toast.success("Integration Added", {
                description: `${integration.name} has been added successfully.`
            });
            // Track addition event if analytics is available
            if (typeof window !== 'undefined' && window.gtag) {
                window.gtag('event', 'integration_added', {
                    type: integration.type,
                    event_count: integration.events.length
                });
            }
        }

        setCurrentIntegration(null);
        setIsEditing(false);
        setTestResult(null);
    }, [currentIntegration, integrations]);

    // Cancel edit
    const handleCancelEdit = useCallback(() => {
        setIsEditing(false);
        setCurrentIntegration(null);
        setTestResult(null);
    }, []);

    // Test connection
    const handleTestConnection = useCallback(async () => {
        if (!currentIntegration || !currentIntegration.url) {
            toast.error("Error", {
                description: "URL is required to test the connection"
            });
            return;
        }

        setIsTestingConnection(true);
        setTestResult(null);

        try {
            const result = await integrationService.testIntegration(currentIntegration);

            setTestResult({
                success: result.success,
                message: result.message || (result.success
                    ? `Successfully sent test message to ${currentIntegration.name}`
                    : 'Failed to connect to integration endpoint')
            });
        } catch (error) {
            setTestResult({
                success: false,
                message: error instanceof Error
                    ? error.message
                    : 'Failed to test integration. Please check the URL and try again.'
            });
        } finally {
            setIsTestingConnection(false);
        }
    }, [currentIntegration]);

    // Handle changing a field in the current integration
    const handleIntegrationChange = useCallback((field: keyof Integration, value: any) => {
        if (!currentIntegration) return;
        setCurrentIntegration(prev => ({ ...prev!, [field]: value }));
    }, [currentIntegration]);

    // Handle toggling an event in the current integration
    const handleEventToggle = useCallback((event: string, checked: boolean) => {
        if (!currentIntegration) return;

        const events = [...(currentIntegration.events || [])];
        if (checked) {
            if (!events.includes(event)) events.push(event);
        } else {
            const index = events.indexOf(event);
            if (index > -1) events.splice(index, 1);
        }

        setCurrentIntegration(prev => ({ ...prev!, events }));
    }, [currentIntegration]);

    return {
        integrations,
        currentIntegration,
        isEditing,
        integrationTab,
        isTestingConnection,
        testResult,
        handleAddIntegration,
        handleEditIntegration,
        handleDeleteIntegration,
        handleToggleActive,
        handleSaveIntegration,
        handleCancelEdit,
        handleTestConnection,
        handleIntegrationChange,
        handleEventToggle
    };
} 