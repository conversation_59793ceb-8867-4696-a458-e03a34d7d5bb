'use client'

import { <PERSON>ertCircle, CheckCircle2, ExternalLink, Info } from 'lucide-react'
import { Integration, IntegrationTestResult } from '@/features/widget/types/integration'
import { getIntegrationTypeDetails } from '@/features/widget/utils/integration-utils'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface IntegrationFormProps {
    integration: Partial<Integration>
    isTestingConnection: boolean
    testResult: IntegrationTestResult | null
    onCancel: () => void
    onSave: () => void
    onTest: () => Promise<void>
    onFieldChange: (field: keyof Integration, value: any) => void
    onEventToggle: (event: string, checked: boolean) => void
}

export function IntegrationForm({
    integration,
    isTestingConnection,
    testResult,
    onCancel,
    onSave,
    onTest,
    onFieldChange,
    onEventToggle
}: IntegrationFormProps) {
    if (!integration || !integration.type) return null;

    const typeDetails = getIntegrationTypeDetails(integration.type);

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <img
                        src={typeDetails.icon}
                        alt={typeDetails.name}
                        className="w-5 h-5"
                        onError={(e) => {
                            // If image fails to load, replace with a default icon
                            e.currentTarget.src = '/assets/integrations/webhook-icon.svg'
                        }}
                    />
                    Configure {typeDetails.name} Integration
                </CardTitle>
                <CardDescription>
                    {typeDetails.description}
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="integration-name">Integration Name</Label>
                    <Input
                        id="integration-name"
                        value={integration.name || ''}
                        onChange={(e) => onFieldChange('name', e.target.value)}
                        placeholder="My Slack Channel"
                    />
                </div>

                <div className="space-y-2">
                    <div className="flex justify-between">
                        <Label htmlFor="webhook-url">{typeDetails.name} Webhook URL</Label>
                        <a
                            href={typeDetails.helpUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-muted-foreground hover:text-primary flex items-center gap-1"
                        >
                            <Info className="w-3 h-3" /> How to get a webhook URL
                            <ExternalLink className="w-3 h-3" />
                        </a>
                    </div>
                    <Input
                        id="webhook-url"
                        value={integration.url || ''}
                        onChange={(e) => onFieldChange('url', e.target.value)}
                        placeholder={typeDetails.placeholder}
                    />
                </div>

                <div className="space-y-2">
                    <Label>Notification Events</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="flex items-center space-x-2">
                            <Switch
                                id="event-message"
                                checked={integration.events?.includes('message.new') || false}
                                onCheckedChange={(checked) => onEventToggle('message.new', checked)}
                            />
                            <Label htmlFor="event-message">New messages</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="event-rating"
                                checked={integration.events?.includes('rating.submit') || false}
                                onCheckedChange={(checked) => onEventToggle('rating.submit', checked)}
                            />
                            <Label htmlFor="event-rating">Message ratings</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="event-session-start"
                                checked={integration.events?.includes('session.start') || false}
                                onCheckedChange={(checked) => onEventToggle('session.start', checked)}
                            />
                            <Label htmlFor="event-session-start">Chat sessions started</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="event-session-end"
                                checked={integration.events?.includes('session.end') || false}
                                onCheckedChange={(checked) => onEventToggle('session.end', checked)}
                            />
                            <Label htmlFor="event-session-end">Chat sessions ended</Label>
                        </div>
                    </div>
                </div>

                {integration.type === 'generic' && (
                    <div className="space-y-2">
                        <Label htmlFor="secret-key">Secret Key (optional)</Label>
                        <Input
                            id="secret-key"
                            type="password"
                            value={integration.secret || ''}
                            onChange={(e) => onFieldChange('secret', e.target.value)}
                            placeholder="Secret key for signature verification"
                        />
                        <p className="text-xs text-muted-foreground">
                            If provided, a signature will be included in the X-Webhook-Signature header
                        </p>
                    </div>
                )}

                {testResult && (
                    <Alert variant={testResult.success ? "default" : "destructive"} className="mt-4">
                        {testResult.success ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                        <AlertTitle>{testResult.success ? "Success" : "Error"}</AlertTitle>
                        <AlertDescription>
                            {testResult.message}
                        </AlertDescription>
                    </Alert>
                )}
            </CardContent>
            <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={onCancel}>
                    Cancel
                </Button>
                <div className="flex gap-2">
                    <Button
                        variant="secondary"
                        onClick={onTest}
                        disabled={isTestingConnection || !integration.url}
                    >
                        {isTestingConnection ? "Testing..." : "Test Connection"}
                    </Button>
                    <Button onClick={onSave}>Save Integration</Button>
                </div>
            </CardFooter>
        </Card>
    )
} 