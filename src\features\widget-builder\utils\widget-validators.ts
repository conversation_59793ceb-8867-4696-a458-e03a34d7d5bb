/**
 * Widget Validation Utilities
 */

// Validation utilities
export const validateWebhookUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url)
    return ['http:', 'https:'].includes(urlObj.protocol)
  } catch {
    return false
  }
}

export const validateDomain = (domain: string): boolean => {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/
  return domainRegex.test(domain)
}

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validateCSS = (css: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Basic CSS validation
  if (css.includes('<script>') || css.includes('javascript:')) {
    errors.push('JavaScript is not allowed in CSS')
  }

  // Check for balanced braces
  const openBraces = (css.match(/{/g) || []).length
  const closeBraces = (css.match(/}/g) || []).length

  if (openBraces !== closeBraces) {
    errors.push('Unbalanced CSS braces')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

export const validatePhoneNumber = (phone: string): boolean => {
  // Basic phone number validation - adjust regex as needed
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

export const validateColor = (color: string): boolean => {
  // Validate hex color
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexRegex.test(color)
}
