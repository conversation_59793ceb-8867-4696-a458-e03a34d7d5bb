import React, { useState, useEffect } from 'react';
import { MessageSquare, X, Send, Minimize2, Maximize2, User, Bot } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SmartWidgetPreviewProps {
  config: any;
}

const SmartWidgetPreview = ({ config }: SmartWidgetPreviewProps) => {
  // Extract configuration values with proper fallbacks
  const primaryColor = config?.primaryColor || '#7E69AB';
  const secondaryColor = config?.secondaryColor || '#ffffff';
  const headerBgColor = config?.headerBgColor || primaryColor;
  const textColor = config?.textColor || '#111827';
  const widgetName = config?.name || config?.botName || 'AI Assistant';
  const welcomeMessage = config?.welcomeMessage || "Hello! How can I help you today?";
  const placeholderText = config?.placeholderText || "Type your message...";
  const position = config?.position || 'bottom-right';
  const theme = config?.theme || 'modern';
  const borderRadius = config?.borderRadius || 12;
  const showLogo = config?.behavior?.showLogo !== false;
  const showCloseButton = config?.behavior?.showCloseButton !== false;
  const darkMode = config?.behavior?.darkMode || false;
  const glassMorphism = config?.behavior?.glassMorphism || false;

  const [isOpen, setIsOpen] = useState(true); // Start open for preview
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: welcomeMessage,
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage = {
      id: messages.length + 1,
      text: inputValue,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages([...messages, newMessage]);
    setInputValue('');

    // Simulate bot response
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        text: "Thanks for your message! I'm here to help.",
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const toggleWidget = () => {
    if (isOpen) {
      setIsOpen(false);
      setIsMinimized(false);
    } else {
      setIsOpen(true);
      setIsMinimized(false);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  return (
    <div className="relative w-full h-full min-h-[500px] bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-lg overflow-hidden">
      {/* Debug indicator */}
      <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs z-50">
        Widget Preview Active
      </div>

      {/* Simulated website background */}
      <div className="absolute inset-0 p-8">
        <div className="w-full h-full bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-2/3"></div>
            <div className="h-32 bg-gray-100 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>

      {/* Widget Container */}
      <div className={`absolute ${positionClasses[position as keyof typeof positionClasses]} z-10`}>
        {/* Chat Widget */}
        {isOpen && (
          <div
            className={`mb-4 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ${isMinimized ? 'h-14' : 'h-96'
              }`}
            style={{
              width: '320px',
              borderRadius: `${borderRadius}px`
            }}
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-4 text-white"
              style={{ backgroundColor: headerBgColor }}
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4" />
                </div>
                <div>
                  <h3 className="font-medium text-sm">{widgetName}</h3>
                  <p className="text-xs opacity-90">Online</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-white hover:bg-white/20"
                  onClick={toggleMinimize}
                >
                  {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
                </Button>
                {showCloseButton && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-white hover:bg-white/20"
                    onClick={toggleWidget}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>

            {/* Messages Area */}
            {!isMinimized && (
              <>
                <div className="flex-1 p-4 space-y-3 h-64 overflow-y-auto bg-gray-50 dark:bg-gray-900">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className="flex items-start space-x-2 max-w-[80%]">
                        {message.sender === 'bot' && (
                          <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <Bot className="w-3 h-3" />
                          </div>
                        )}
                        <div
                          className={`px-3 py-2 rounded-lg text-sm ${message.sender === 'user'
                            ? 'text-white'
                            : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700'
                            }`}
                          style={{
                            backgroundColor: message.sender === 'user' ? primaryColor : undefined
                          }}
                        >
                          {message.text}
                        </div>
                        {message.sender === 'user' && (
                          <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <User className="w-3 h-3" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Input Area */}
                <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                  <div className="flex space-x-2">
                    <Input
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={placeholderText}
                      className="flex-1 text-sm"
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    />
                    <Button
                      size="sm"
                      onClick={handleSendMessage}
                      style={{ backgroundColor: primaryColor }}
                      className="text-white hover:opacity-90"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Widget Trigger Button */}
        <Button
          className="w-14 h-14 rounded-full shadow-lg text-white hover:scale-105 transition-all duration-200"
          style={{ backgroundColor: primaryColor }}
          onClick={toggleWidget}
        >
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <MessageSquare className="h-6 w-6" />
          )}
        </Button>
      </div>
    </div>
  );
};

export default SmartWidgetPreview;
