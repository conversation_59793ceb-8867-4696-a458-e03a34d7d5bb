/**
 * useDocuments Hook
 * 
 * Custom hook for managing documents in the knowledge base
 */

import { useState, useCallback, useEffect } from 'react';
import { knowledgeBaseService } from '../api/knowledge-base-service';
import { Document, UploadStatus, UploadProgress } from '../types';

interface UseDocumentsProps {
    projectId: number;
}

export function useDocuments({ projectId }: UseDocumentsProps) {
    const [documents, setDocuments] = useState<Document[]>([]);
    const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
        status: 'idle',
        progress: 0,
    });

    /**
     * Fetch all documents for the project
     */
    const fetchDocuments = useCallback(async () => {
        setIsLoading(true);
        setError(null);

        try {
            const fetchedDocuments = await knowledgeBaseService.getDocuments(projectId);
            setDocuments(fetchedDocuments);
        } catch (err) {
            setError('Failed to load documents. Please try again.');
            console.error('Error fetching documents:', err);
        } finally {
            setIsLoading(false);
        }
    }, [projectId]);

    /**
     * Upload a document
     */
    const uploadDocument = useCallback(async (file: File) => {
        setUploadProgress({
            status: 'uploading',
            progress: 0,
            message: 'Uploading document...',
        });

        try {
            // Simulate progress updates (in a real app, you'd use upload progress events)
            const progressInterval = setInterval(() => {
                setUploadProgress(prev => ({
                    ...prev,
                    progress: Math.min(prev.progress + 10, 90), // Max at 90% until complete
                }));
            }, 500);

            // Upload the document
            const newDocument = await knowledgeBaseService.uploadDocument(projectId, file);

            clearInterval(progressInterval);

            setUploadProgress({
                status: 'processing',
                progress: 95,
                message: 'Processing document...',
            });

            // Simulate processing time
            setTimeout(() => {
                setUploadProgress({
                    status: 'success',
                    progress: 100,
                    message: 'Document uploaded successfully!',
                });

                // Add the new document to the list
                setDocuments(prev => [...prev, newDocument]);

                // Reset progress after a delay
                setTimeout(() => {
                    setUploadProgress({
                        status: 'idle',
                        progress: 0,
                    });
                }, 2000);
            }, 1000);

            return newDocument;
        } catch (err) {
            setUploadProgress({
                status: 'error',
                progress: 0,
                message: 'Failed to upload document.',
                error: err instanceof Error ? err.message : 'Unknown error',
            });
            throw err;
        }
    }, [projectId]);

    /**
     * Delete a document
     */
    const deleteDocument = useCallback(async (documentId: string) => {
        try {
            await knowledgeBaseService.deleteDocument(documentId);
            // Remove the document from the list
            setDocuments(prev => prev.filter(doc => doc.id !== documentId));
            // If the selected document is being deleted, clear it
            if (selectedDocument?.id === documentId) {
                setSelectedDocument(null);
            }
            return { success: true };
        } catch (err) {
            setError('Failed to delete document. Please try again.');
            console.error('Error deleting document:', err);
            throw err;
        }
    }, [selectedDocument]);

    /**
     * Generate embeddings for a document
     */
    const generateEmbeddings = useCallback(async (documentId: string) => {
        try {
            await knowledgeBaseService.generateEmbeddings(documentId);
            // Update the document in the list to mark it as processed
            setDocuments(prev =>
                prev.map(doc =>
                    doc.id === documentId ? { ...doc, isProcessed: true } : doc
                )
            );
            return { success: true };
        } catch (err) {
            setError('Failed to generate embeddings. Please try again.');
            console.error('Error generating embeddings:', err);
            throw err;
        }
    }, []);

    /**
     * Select a document
     */
    const selectDocument = useCallback(async (documentId: string) => {
        try {
            const document = await knowledgeBaseService.getDocument(documentId);
            setSelectedDocument(document);
        } catch (err) {
            setError('Failed to load document details. Please try again.');
            console.error('Error selecting document:', err);
        }
    }, []);

    /**
     * Clear the selected document
     */
    const clearSelectedDocument = useCallback(() => {
        setSelectedDocument(null);
    }, []);

    // Fetch documents on mount and when projectId changes
    useEffect(() => {
        if (projectId) {
            fetchDocuments();
        }
    }, [projectId, fetchDocuments]);

    return {
        documents,
        selectedDocument,
        isLoading,
        error,
        uploadProgress,
        uploadDocument,
        deleteDocument,
        generateEmbeddings,
        selectDocument,
        clearSelectedDocument,
        refreshDocuments: fetchDocuments,
    };
} 