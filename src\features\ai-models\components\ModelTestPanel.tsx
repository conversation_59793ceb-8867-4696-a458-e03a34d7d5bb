/**
 * Model Test Panel Component
 * 
 * Component for testing AI models with sample prompts
 */

import { useState } from 'react'
import { TestModelResponse } from '../types'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle2, XCircle } from 'lucide-react'

interface ModelTestPanelProps {
    onTest: () => Promise<void>
    isTesting: boolean
    testResult: TestModelResponse | null
}

export function ModelTestPanel({ onTest, isTesting, testResult }: ModelTestPanelProps) {
    const [customPrompt, setCustomPrompt] = useState("Hello, can you introduce yourself briefly?")

    const getResponseSummary = () => {
        if (!testResult) return null

        if (!testResult.success) {
            return (
                <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
                    <div className="flex items-center mb-2">
                        <XCircle className="h-5 w-5 mr-2" />
                        <h3 className="font-medium">Test Failed</h3>
                    </div>
                    <p>{testResult.error || testResult.message}</p>
                </div>
            )
        }

        return (
            <div className="mt-4">
                <div className="flex items-center mb-2">
                    <CheckCircle2 className="h-5 w-5 mr-2 text-green-600" />
                    <h3 className="font-medium">Test Successful</h3>
                </div>

                {testResult.latency && (
                    <Badge variant="outline" className="mb-2">
                        Latency: {testResult.latency.toFixed(2)}ms
                    </Badge>
                )}

                {testResult.usage && (
                    <div className="flex gap-2 mb-2">
                        <Badge variant="outline">
                            Prompt Tokens: {testResult.usage.promptTokens || 0}
                        </Badge>
                        <Badge variant="outline">
                            Completion Tokens: {testResult.usage.completionTokens || 0}
                        </Badge>
                        <Badge variant="outline">
                            Total Tokens: {testResult.usage.totalTokens || 0}
                        </Badge>
                    </div>
                )}

                <Card className="mt-2">
                    <CardHeader className="py-3">
                        <CardTitle className="text-sm">Response</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="whitespace-pre-wrap text-sm bg-gray-50 p-3 rounded-md">
                            {testResult.response}
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Test Model</CardTitle>
                    <CardDescription>
                        Verify that your model is working properly with a test prompt
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-2">
                            Test Prompt
                        </label>
                        <Textarea
                            value={customPrompt}
                            onChange={(e) => setCustomPrompt(e.target.value)}
                            placeholder="Enter a prompt to test the model..."
                            rows={3}
                            className="w-full"
                        />
                    </div>

                    <Button
                        onClick={onTest}
                        disabled={isTesting}
                        className="w-full"
                    >
                        {isTesting ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Testing...
                            </>
                        ) : (
                            'Test Model'
                        )}
                    </Button>

                    {getResponseSummary()}
                </CardContent>
            </Card>

            <div className="text-sm text-gray-500">
                <p className="mb-2">Testing Tips:</p>
                <ul className="list-disc list-inside space-y-1">
                    <li>Try simple prompts first to verify basic functionality</li>
                    <li>Test with different model parameters to see how they affect responses</li>
                    <li>Check token usage to estimate costs</li>
                    <li>Verify that the model follows your system prompt instructions</li>
                </ul>
            </div>
        </div>
    )
}

export default ModelTestPanel 