/**
 * Document Utilities
 * 
 * Helper functions for working with knowledge base documents
 */

import { Document } from '../types';

/**
 * Format file size to human-readable format
 */
export function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file type icon name based on file type
 */
export function getFileTypeIcon(fileType: string): string {
    switch (fileType.toLowerCase()) {
        case 'pdf':
            return 'file-pdf';
        case 'docx':
        case 'doc':
            return 'file-word';
        case 'txt':
            return 'file-text';
        case 'md':
            return 'file-markdown';
        case 'csv':
            return 'file-spreadsheet';
        case 'xlsx':
        case 'xls':
            return 'file-excel';
        case 'json':
            return 'file-json';
        default:
            return 'file';
    }
}

/**
 * Format date string to human-readable format
 */
export function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    }).format(date);
}

/**
 * Check if file type is supported
 */
export function isFileTypeSupported(fileType: string): boolean {
    const supportedTypes = ['pdf', 'docx', 'doc', 'txt', 'md', 'csv', 'xlsx', 'xls', 'json'];
    return supportedTypes.includes(fileType.toLowerCase());
}

/**
 * Extract file type from file name
 */
export function getFileTypeFromName(fileName: string): string {
    return fileName.split('.').pop()?.toLowerCase() || '';
}

/**
 * Sort documents by specified field
 */
export function sortDocuments(documents: Document[], sortBy: keyof Document, sortDirection: 'asc' | 'desc'): Document[] {
    return [...documents].sort((a, b) => {
        const valueA = a[sortBy];
        const valueB = b[sortBy];

        if (typeof valueA === 'string' && typeof valueB === 'string') {
            return sortDirection === 'asc'
                ? valueA.localeCompare(valueB)
                : valueB.localeCompare(valueA);
        }

        if (valueA instanceof Date && valueB instanceof Date) {
            return sortDirection === 'asc'
                ? valueA.getTime() - valueB.getTime()
                : valueB.getTime() - valueA.getTime();
        }

        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
        }

        return 0;
    });
}

/**
 * Filter documents by search query
 */
export function filterDocuments(documents: Document[], searchQuery: string): Document[] {
    if (!searchQuery.trim()) {
        return documents;
    }

    const query = searchQuery.toLowerCase().trim();

    return documents.filter(doc =>
        doc.title.toLowerCase().includes(query) ||
        (doc.content && doc.content.toLowerCase().includes(query))
    );
}

/**
 * Truncate text to specified length
 */
export function truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
        return text;
    }

    return text.slice(0, maxLength) + '...';
}

/**
 * Get document status label
 */
export function getDocumentStatus(document: Document): {
    label: string;
    color: 'default' | 'primary' | 'secondary' | 'error' | 'warning' | 'success';
} {
    if (!document.isProcessed) {
        return {
            label: 'Not Processed',
            color: 'warning'
        };
    }

    return {
        label: 'Processed',
        color: 'success'
    };
} 