/**
 * usePermissions Hook
 * 
 * Custom hook for permission management
 */

import { useState, useCallback } from 'react'
import { permissionService } from '../api/permission-service'
import {
    Permission,
    PermissionCreateData,
    PermissionUpdateData
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function usePermissions() {
    const [permissions, setPermissions] = useState<Permission[]>([])
    const [currentPermission, setCurrentPermission] = useState<Permission | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isProcessing, setIsProcessing] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch all permissions
     */
    const fetchPermissions = useCallback(async () => {
        setIsLoading(true)
        setError(null)

        try {
            const response = await permissionService.getAllPermissions()
            setPermissions(response.data || [])
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a permission by ID
     */
    const fetchPermission = useCallback(async (id: number) => {
        setIsLoading(true)
        setError(null)

        try {
            const response = await permissionService.getPermission(id)
            setCurrentPermission(response.data)
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new permission
     */
    const createPermission = useCallback(async (permissionData: PermissionCreateData) => {
        setIsProcessing(true)
        setError(null)

        try {
            const response = await permissionService.createPermission(permissionData)
            setPermissions(prev => [...prev, response.data])
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsProcessing(false)
        }
    }, [])

    /**
     * Update a permission
     */
    const updatePermission = useCallback(async (id: number, permissionData: PermissionUpdateData) => {
        setIsProcessing(true)
        setError(null)

        try {
            const response = await permissionService.updatePermission(id, permissionData)

            // Update permissions list
            setPermissions(prev => prev.map(permission =>
                permission.id === id ? { ...permission, ...response.data } : permission
            ))

            // Update current permission if it's the one being edited
            if (currentPermission?.id === id) {
                setCurrentPermission(response.data)
            }

            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsProcessing(false)
        }
    }, [currentPermission])

    /**
     * Delete a permission
     */
    const deletePermission = useCallback(async (id: number) => {
        setIsProcessing(true)
        setError(null)

        try {
            const result = await permissionService.deletePermission(id)

            // If deletion was successful, remove from local state
            if (result.success) {
                setPermissions(prev => prev.filter(permission => permission.id !== id))

                // If the deleted permission was the current permission, clear current permission
                if (currentPermission?.id === id) {
                    setCurrentPermission(null)
                }
            }

            return result.success
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsProcessing(false)
        }
    }, [currentPermission])

    /**
     * Fetch permissions by category
     */
    const fetchPermissionsByCategory = useCallback(async (category: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const response = await permissionService.getPermissionsByCategory(category)
            return response.data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Set a permission as the current permission
     */
    const selectPermission = useCallback((permission: Permission) => {
        setCurrentPermission(permission)
    }, [])

    /**
     * Clear the current permission
     */
    const clearCurrentPermission = useCallback(() => {
        setCurrentPermission(null)
    }, [])

    /**
     * Group permissions by category
     */
    const getPermissionsByCategory = useCallback(() => {
        const grouped: Record<string, Permission[]> = {}

        permissions.forEach(permission => {
            if (!grouped[permission.category]) {
                grouped[permission.category] = []
            }
            grouped[permission.category].push(permission)
        })

        return grouped
    }, [permissions])

    return {
        permissions,
        currentPermission,
        isLoading,
        isProcessing,
        error,
        fetchPermissions,
        fetchPermission,
        createPermission,
        updatePermission,
        deletePermission,
        fetchPermissionsByCategory,
        selectPermission,
        clearCurrentPermission,
        getPermissionsByCategory
    }
} 