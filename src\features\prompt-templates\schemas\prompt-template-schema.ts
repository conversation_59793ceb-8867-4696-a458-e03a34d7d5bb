/**
 * Prompt Template Schemas
 * 
 * This file contains Zod validation schemas for prompt template functionality
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Prompt variable schema
 */
export const promptVariableSchema = z.object({
    name: z.string().min(1, 'Variable name is required'),
    description: z.string().optional(),
    required: z.boolean().default(true),
    defaultValue: z.string().optional(),
    type: z.enum(['string', 'number', 'boolean', 'array']).default('string'),
});

/**
 * Prompt template schema
 */
export const promptTemplateSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    content: z.string().min(10, 'Template content must be at least 10 characters'),
    category: z.enum(['system', 'user', 'assistant', 'function', 'custom']).default('system'),
    variables: z.array(promptVariableSchema).optional(),
    tags: z.array(z.string()).optional(),
    version: z.number().int().positive().default(1),
    isActive: z.boolean().default(true),
    isDefault: z.boolean().default(false),
    aiModelId: z.string().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Prompt template usage schema
 */
export const promptTemplateUsageSchema = z.object({
    templateId: z.string(),
    variables: z.record(z.string(), z.any()).optional(),
    context: z.record(z.string(), z.any()).optional(),
});

/**
 * Template evaluation schema
 */
export const templateEvaluationSchema = z.object({
    templateId: z.string(),
    result: z.string(),
    performance: z.object({
        tokensUsed: z.number().int().nonnegative(),
        processingTimeMs: z.number().nonnegative(),
    }),
    rating: z.number().min(1).max(5).optional(),
    feedback: z.string().optional(),
    timestamp: z.date().default(() => new Date()),
});

export default {
    promptVariableSchema,
    promptTemplateSchema,
    promptTemplateUsageSchema,
    templateEvaluationSchema,
}; 