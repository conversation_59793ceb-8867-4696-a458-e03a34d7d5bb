/**
 * AI Configuration Types
 */

export interface AiConfiguration {
  id: number
  widgetId: number
  provider: string
  model: string
  settings: ModelSettings
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ModelProvider {
  id: string
  name: string
  description: string
  isActive: boolean
  supportedModels: string[]
  requiredCredentials: string[]
  features: string[]
}

export interface ModelSettings {
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  systemPrompt?: string
  contextWindow?: number
  streaming?: boolean
  apiKey?: string
  baseUrl?: string
  customHeaders?: Record<string, string>
}

export interface AiUsageStats {
  totalRequests: number
  totalTokens: number
  averageResponseTime: number
  errorRate: number
  costEstimate: number
  period: string
}

export interface ModelCapabilities {
  maxTokens: number
  supportsStreaming: boolean
  supportsImages: boolean
  supportsTools: boolean
  contextWindow: number
  pricing: {
    input: number
    output: number
    unit: string
  }
}
