/**
 * AI Configuration Types
 */

export interface AiConfiguration {
  id: number
  widgetId: number
  provider: string
  model: string
  settings: ModelSettings
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ModelProvider {
  id: string
  name: string
  description: string
  isActive: boolean
  supportedModels: string[]
  requiredCredentials: string[]
  features: string[]
}

export interface ModelSettings {
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  systemPrompt?: string
  contextWindow?: number
  streaming?: boolean
  apiKey?: string
  baseUrl?: string
  customHeaders?: Record<string, string>
}

export interface AiUsageStats {
  totalRequests: number
  totalTokens: number
  averageResponseTime: number
  errorRate: number
  costEstimate: number
  period: string
}

export interface ModelCapabilities {
  maxTokens: number
  supportsStreaming: boolean
  supportsImages: boolean
  supportsTools: boolean
  contextWindow: number
  pricing: {
    input: number
    output: number
    unit: string
  }
}

export interface AIModelData {
  id?: number
  name: string
  provider: string
  description?: string
  api_key?: string
  settings?: {
    temperature?: number
    max_tokens?: number
    model_name?: string
    [key: string]: any
  }
  is_default?: boolean
  active?: boolean
  is_free?: boolean
  fallback_model_id?: number | null
  confidence_threshold?: number
  template_id?: number | null
  created_at?: string
  updated_at?: string
}

export interface AvailableModelData {
  name: string
  display_name?: string
  capabilities?: string[]
  max_tokens?: number
  description?: string
}
