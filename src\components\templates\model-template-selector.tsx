import React, { useState, useEffect } from "react"
import {
    <PERSON>,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    Card<PERSON>itle
} from "@/components/ui/card"
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { Check, Loader2, FileText } from "lucide-react"
import { templateService, Template } from "@/utils/template-service"
import { aiModelService, AIModelData } from "@/utils/ai-model-service"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TemplateTester } from "./template-tester"
import { Badge } from "@/components/ui/badge"

interface ModelTemplateSelectorProps {
    modelId: number
    onSuccess?: () => void
}

export function ModelTemplateSelector({ modelId, onSuccess }: ModelTemplateSelectorProps) {
    const { toast } = useToast()
    const [templates, setTemplates] = useState<Template[]>([])
    const [selectedTemplateId, setSelectedTemplateId] = useState<string>("")
    const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
    const [currentTemplateId, setCurrentTemplateId] = useState<number | null>(null)
    const [model, setModel] = useState<AIModelData | null>(null)
    const [loading, setLoading] = useState<boolean>(true)
    const [saving, setSaving] = useState<boolean>(false)
    const [activeTab, setActiveTab] = useState<string>("assign")

    useEffect(() => {
        loadData()
    }, [modelId])

    const loadData = async () => {
        setLoading(true)
        try {
            // Load all templates
            const allTemplates = await templateService.getTemplates({ status: "active" })
            setTemplates(allTemplates)

            // Load the model to get its current template
            const modelData = await aiModelService.getModel(modelId)
            setModel(modelData)

            if (modelData && modelData.template_id) {
                setCurrentTemplateId(modelData.template_id)
                setSelectedTemplateId(modelData.template_id.toString())

                // Load the selected template details
                const templateData = await templateService.getTemplate(modelData.template_id)
                setSelectedTemplate(templateData)
            } else {
                setCurrentTemplateId(null)
                setSelectedTemplateId("")
                setSelectedTemplate(null)
            }
        } catch (error) {
            console.error("Error loading templates:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to load templates. Please try again."
            })
        } finally {
            setLoading(false)
        }
    }

    const handleTemplateChange = (value: string) => {
        setSelectedTemplateId(value)

        if (value) {
            const template = templates.find(t => t.id.toString() === value)
            setSelectedTemplate(template || null)
        } else {
            setSelectedTemplate(null)
        }
    }

    const handleSave = async () => {
        if (!model) return

        setSaving(true)
        try {
            const templateId = selectedTemplateId ? parseInt(selectedTemplateId) : null

            await aiModelService.updateModel(modelId, {
                template_id: templateId
            })

            setCurrentTemplateId(templateId)

            toast({
                title: "Success",
                description: templateId
                    ? "Template assigned to model successfully."
                    : "Template removed from model successfully."
            })

            if (onSuccess) {
                onSuccess()
            }
        } catch (error) {
            console.error("Error saving template assignment:", error)
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to assign template. Please try again."
            })
        } finally {
            setSaving(false)
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {model ? `Template for ${model.name}` : "Model Template"}
                </CardTitle>
                <CardDescription>
                    Assign a template to this AI model to control how it responds to user queries.
                </CardDescription>
            </CardHeader>
            <CardContent>
                {loading ? (
                    <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                ) : (
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList className="mb-4">
                            <TabsTrigger value="assign">Assign Template</TabsTrigger>
                            <TabsTrigger value="preview" disabled={!selectedTemplate}>Preview Template</TabsTrigger>
                            <TabsTrigger value="test" disabled={!selectedTemplate}>Test Template</TabsTrigger>
                        </TabsList>

                        <TabsContent value="assign">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Select Template</label>
                                    <Select
                                        value={selectedTemplateId}
                                        onValueChange={handleTemplateChange}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a template" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                <SelectLabel>Templates</SelectLabel>
                                                {templates.map((template) => (
                                                    <SelectItem
                                                        key={template.id}
                                                        value={template.id.toString()}
                                                    >
                                                        <div className="flex items-center gap-2">
                                                            {template.name}
                                                            {template.is_default && (
                                                                <span className="text-xs text-gray-500">(Default)</span>
                                                            )}
                                                            {template.id === currentTemplateId && (
                                                                <Check className="h-4 w-4 text-primary" />
                                                            )}
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                                <SelectItem value="">
                                                    <span className="text-gray-500">-- No Template --</span>
                                                </SelectItem>
                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {currentTemplateId && (
                                    <div className="text-sm text-gray-500">
                                        Current template: {templates.find(t => t.id === currentTemplateId)?.name || "None"}
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="preview">
                            {selectedTemplate && (
                                <div className="space-y-4">
                                    <div>
                                        <h3 className="text-lg font-medium mb-2">Template Content</h3>
                                        <div className="p-4 border rounded-md bg-gray-50 whitespace-pre-wrap font-mono text-sm">
                                            {selectedTemplate.content}
                                        </div>
                                    </div>

                                    {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                                        <div>
                                            <h3 className="text-lg font-medium mb-2">Variables</h3>
                                            <div className="flex flex-wrap gap-2">
                                                {selectedTemplate.variables.map((variable) => (
                                                    <Badge key={variable} variant="secondary">
                                                        {variable}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="test">
                            {selectedTemplate && (
                                <TemplateTester
                                    templateId={selectedTemplate.id}
                                    templateContent={selectedTemplate.content}
                                    variables={selectedTemplate.variables}
                                />
                            )}
                        </TabsContent>
                    </Tabs>
                )}
            </CardContent>
            <CardFooter className="flex justify-end">
                <Button
                    onClick={handleSave}
                    disabled={saving || loading || selectedTemplateId === currentTemplateId?.toString()}
                >
                    {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {currentTemplateId === null && selectedTemplateId
                        ? "Assign Template"
                        : currentTemplateId && !selectedTemplateId
                            ? "Remove Template"
                            : "Update Template"}
                </Button>
            </CardFooter>
        </Card>
    )
}