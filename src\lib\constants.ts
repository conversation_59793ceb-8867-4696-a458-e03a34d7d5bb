/**
 * Application Constants
 * 
 * This file contains application-wide constants
 */

// API Base URL
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Common status values
export const STATUS = {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
    PROCESSING: 'processing',
    SUCCESS: 'success',
    ERROR: 'error',
    FAILED: 'failed',
};

// Pagination defaults
export const PAGINATION = {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    DEFAULT_SORT: 'createdAt',
    DEFAULT_ORDER: 'desc',
};

// Local storage keys
export const STORAGE_KEYS = {
    AUTH_TOKEN: 'auth_token',
    USER: 'user',
    THEME: 'theme',
    LANGUAGE: 'language',
};

// Timeout values (in milliseconds)
export const TIMEOUTS = {
    API_REQUEST: 30000, // 30 seconds
    DEBOUNCE_SEARCH: 300, // 300ms for search input debounce
    TOAST_DURATION: 5000, // 5 seconds for toast messages
};

// Default error messages
export const ERROR_MESSAGES = {
    GENERAL: 'Something went wrong. Please try again.',
    NETWORK: 'Network error. Please check your connection.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    NOT_FOUND: 'The requested resource was not found.',
    VALIDATION: 'Please check the form for errors.',
};

// Auth related constants
export const AUTH = {
    TOKEN_EXPIRY: 60 * 60 * 24 * 7, // 7 days in seconds
    REFRESH_THRESHOLD: 60 * 60, // 1 hour in seconds
};

// Feature flags
export const FEATURES = {
    DARK_MODE: true,
    FILE_UPLOADS: true,
    ANALYTICS: true,
    CHAT_HISTORY: true,
};

// Export all constants as default object
export default {
    API_BASE_URL,
    STATUS,
    PAGINATION,
    STORAGE_KEYS,
    TIMEOUTS,
    ERROR_MESSAGES,
    AUTH,
    FEATURES,
}; 