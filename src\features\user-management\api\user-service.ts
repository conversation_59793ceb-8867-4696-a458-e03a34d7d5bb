/**
 * User API Service
 * 
 * This file contains all API calls related to user management
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    User,
    UserUpdateData,
    UserFilters,
    UserPagination
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for users
 */
const USER_API = `${API_BASE_URL}/users`;

/**
 * User API Service
 */
export const userService = {
    /**
     * Get all users with pagination and filters
     */
    getUsers: async (page = 1, limit = 10, filters?: UserFilters): Promise<UserPagination> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(USER_API, {
                params: {
                    page,
                    limit,
                    ...filters
                }
            });
        }, 'getUsers');

        if (error || !response) {
            throw error || new Error('Failed to fetch users');
        }

        return response.data;
    },

    /**
     * Get user by ID
     */
    getUser: async (id: string | number): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${USER_API}/${id}`);
        }, 'getUser');

        if (error || !response) {
            throw error || new Error(`Failed to fetch user with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Create a new user
     */
    createUser: async (user: Omit<User, 'id'>): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(USER_API, user);
        }, 'createUser');

        if (error || !response) {
            throw error || new Error('Failed to create user');
        }

        return response.data;
    },

    /**
     * Update a user
     */
    updateUser: async (id: string | number, data: UserUpdateData): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${USER_API}/${id}`, data);
        }, 'updateUser');

        if (error || !response) {
            throw error || new Error(`Failed to update user with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a user
     */
    deleteUser: async (id: string | number): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${USER_API}/${id}`);
        }, 'deleteUser');

        if (error || !response) {
            throw error || new Error(`Failed to delete user with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Toggle user active status
     */
    toggleUserStatus: async (id: string | number, isActive: boolean): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${USER_API}/${id}/toggle-status`, { isActive });
        }, 'toggleUserStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle status for user with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Update user role
     */
    updateUserRole: async (id: string | number, role: string): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${USER_API}/${id}/role`, { role });
        }, 'updateUserRole');

        if (error || !response) {
            throw error || new Error(`Failed to update role for user with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Update user permissions
     */
    updateUserPermissions: async (id: string | number, permissions: string[]): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${USER_API}/${id}/permissions`, { permissions });
        }, 'updateUserPermissions');

        if (error || !response) {
            throw error || new Error(`Failed to update permissions for user with ID ${id}`);
        }

        return response.data;
    }
};

export default userService; 