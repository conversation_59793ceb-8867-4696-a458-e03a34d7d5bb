/**
 * Integration API Service
 * 
 * This file contains all API calls related to widget integrations
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    Integration,
    CreateIntegrationInput,
    UpdateIntegrationInput,
    IntegrationTestResult
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for integrations
 */
const INTEGRATION_API = `${API_BASE_URL}/integrations`;

/**
 * Integration API Service
 */
export const integrationService = {
    /**
     * Get all integrations for a widget
     */
    getIntegrations: async (widgetId: string): Promise<Integration[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(INTEGRATION_API, {
                params: { widgetId }
            });
        }, 'getIntegrations');

        if (error || !response) {
            throw error || new Error(`Failed to fetch integrations for widget ${widgetId}`);
        }

        return response.data;
    },

    /**
     * Get an integration by ID
     */
    getIntegration: async (integrationId: string): Promise<Integration> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${INTEGRATION_API}/${integrationId}`);
        }, 'getIntegration');

        if (error || !response) {
            throw error || new Error(`Failed to fetch integration with ID ${integrationId}`);
        }

        return response.data;
    },

    /**
     * Create a new integration
     */
    createIntegration: async (integration: CreateIntegrationInput): Promise<Integration> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(INTEGRATION_API, integration);
        }, 'createIntegration');

        if (error || !response) {
            throw error || new Error('Failed to create integration');
        }

        return response.data;
    },

    /**
     * Update an integration
     */
    updateIntegration: async (integrationId: string, integration: UpdateIntegrationInput): Promise<Integration> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${INTEGRATION_API}/${integrationId}`, integration);
        }, 'updateIntegration');

        if (error || !response) {
            throw error || new Error(`Failed to update integration with ID ${integrationId}`);
        }

        return response.data;
    },

    /**
     * Delete an integration
     */
    deleteIntegration: async (integrationId: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${INTEGRATION_API}/${integrationId}`);
        }, 'deleteIntegration');

        if (error || !response) {
            throw error || new Error(`Failed to delete integration with ID ${integrationId}`);
        }

        return { success: true };
    },

    /**
     * Toggle integration active status
     */
    toggleIntegrationStatus: async (integrationId: string, isActive: boolean): Promise<Integration> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${INTEGRATION_API}/${integrationId}/toggle`, { isActive });
        }, 'toggleIntegrationStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle integration status for ID ${integrationId}`);
        }

        return response.data;
    },

    /**
     * Test an integration connection
     */
    testIntegration: async (integration: Partial<Integration>): Promise<IntegrationTestResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${INTEGRATION_API}/test`, integration);
        }, 'testIntegration');

        if (error || !response) {
            return {
                success: false,
                message: error?.message || 'Failed to test integration connection',
                statusCode: 500
            };
        }

        return response.data;
    }
};

export default integrationService; 