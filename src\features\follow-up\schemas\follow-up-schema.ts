/**
 * Follow-up Schemas
 * 
 * This file contains Zod validation schemas for follow-up functionality
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Follow-up option schema
 */
export const followUpOptionSchema = z.object({
    id: z.string().optional(),
    text: commonSchemas.nonEmptyString,
    value: z.string(),
    order: z.number().int().nonnegative().default(0),
    icon: z.string().optional(),
    isDefault: z.boolean().default(false),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Follow-up question schema
 */
export const followUpQuestionSchema = createFormSchema({
    question: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    options: z.array(followUpOptionSchema).min(1, 'At least one option is required'),
    isMultiSelect: z.boolean().default(false),
    isRequired: z.boolean().default(true),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    placement: z.enum(['beginning', 'middle', 'end']).default('end'),
    conditions: z.array(z.object({
        field: z.string(),
        operator: z.enum(['equals', 'contains', 'startsWith', 'endsWith', 'regex']),
        value: z.string(),
    })).optional(),
    aiGenerated: z.boolean().default(false),
    isActive: z.boolean().default(true),
});

/**
 * Follow-up chain schema
 */
export const followUpChainSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    questions: z.array(followUpQuestionSchema).min(1, 'At least one question is required'),
    triggers: z.array(z.object({
        type: z.enum(['intent', 'keyword', 'sentiment', 'custom']),
        value: z.string(),
    })),
    isAutomatic: z.boolean().default(true),
    priority: z.number().int().nonnegative().default(0),
    cooldownMinutes: z.number().int().nonnegative().default(60),
    maxDisplayCount: z.number().int().nonnegative().default(3),
    relevanceThreshold: z.number().min(0).max(1).default(0.7),
    isActive: z.boolean().default(true),
});

/**
 * Follow-up response schema
 */
export const followUpResponseSchema = z.object({
    chainId: z.string(),
    questionId: z.string(),
    selectedOptions: z.array(z.string()),
    additionalFeedback: z.string().optional(),
    sessionId: z.string(),
    userId: z.string().optional(),
    timestamp: z.date().default(() => new Date()),
});

export default {
    followUpOptionSchema,
    followUpQuestionSchema,
    followUpChainSchema,
    followUpResponseSchema,
}; 