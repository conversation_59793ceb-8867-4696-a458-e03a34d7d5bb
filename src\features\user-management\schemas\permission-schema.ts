/**
 * Permission Schema Definitions
 * 
 * This file contains Zod schemas for permissions
 */

import { z } from 'zod'

/**
 * Permission type enum
 */
export const permissionTypeEnum = z.enum([
    'read',
    'write',
    'create',
    'update',
    'delete',
    'manage',
    'execute',
    'admin',
])

/**
 * Base permission schema
 */
export const permissionBaseSchema = z.object({
    name: z.string()
        .min(3, 'Permission name must be at least 3 characters')
        .max(100, 'Permission name must be less than 100 characters'),
    description: z.string().nullable().optional(),
    category: z.string()
        .min(2, 'Category must be at least 2 characters')
        .max(50, 'Category must be less than 50 characters'),
    type: permissionTypeEnum,
})

/**
 * Permission schema with ID and timestamps
 */
export const permissionSchema = permissionBaseSchema.extend({
    id: z.number(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
})

/**
 * Permission schema for creation
 */
export const permissionCreateSchema = permissionBaseSchema

/**
 * Permission schema for update
 */
export const permissionUpdateSchema = permissionBaseSchema.partial() 