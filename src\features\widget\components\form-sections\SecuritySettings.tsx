/**
 * Security Settings Form Section
 * 
 * Widget security and access control configuration
 */

import { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'
import { Widget } from '../../types'
import { FormField, FormLabel, FormControl, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { XIcon } from 'lucide-react'

interface SecuritySettingsProps {
    form: UseFormReturn<Widget>
}

export function SecuritySettings({ form }: SecuritySettingsProps) {
    const [newDomain, setNewDomain] = useState('')

    const addDomain = () => {
        if (!newDomain) return

        const domains = form.getValues('security.domains') || []

        // Skip if domain already exists
        if (domains.includes(newDomain)) {
            setNewDomain('')
            return
        }

        form.setValue('security.domains', [...domains, newDomain])
        setNewDomain('')
    }

    const removeDomain = (domain: string) => {
        const domains = form.getValues('security.domains') || []
        form.setValue('security.domains', domains.filter(d => d !== domain))
    }

    return (
        <div className="space-y-6">
            <h2 className="text-lg font-medium">Security Settings</h2>
            <p className="text-sm text-gray-500">
                Configure security and access control for your widget
            </p>

            <div className="space-y-8">
                {/* Domain Restriction Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Domain Restrictions</h3>

                    <FormField
                        control={form.control}
                        name="security.restrictByDomain"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Restrict By Domain</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Only allow widget to load on specific domains
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {form.watch('security.restrictByDomain') && (
                        <div className="space-y-4">
                            <div className="flex gap-2">
                                <Input
                                    placeholder="example.com"
                                    value={newDomain}
                                    onChange={(e) => setNewDomain(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="button" onClick={addDomain}>Add</Button>
                            </div>

                            <div className="flex flex-wrap gap-2 mt-2">
                                {form.watch('security.domains')?.map((domain) => (
                                    <Badge key={domain} variant="secondary" className="text-sm py-1 px-2">
                                        {domain}
                                        <XIcon
                                            className="ml-1 h-3 w-3 cursor-pointer"
                                            onClick={() => removeDomain(domain)}
                                        />
                                    </Badge>
                                ))}
                                {!form.watch('security.domains')?.length && (
                                    <p className="text-sm text-gray-500 italic">No domains added yet</p>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                {/* Data Protection Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Data Protection</h3>

                    <FormField
                        control={form.control}
                        name="security.dataSanitization"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Data Sanitization</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Sanitize user inputs to prevent XSS attacks
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="security.storageConsent"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Storage Consent</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Require user consent before storing chat data
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Rate Limiting Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Rate Limiting</h3>

                    <FormField
                        control={form.control}
                        name="security.maxRequestsPerMinute"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Max Requests Per Minute: {field.value}</FormLabel>
                                <FormControl>
                                    <Slider
                                        min={1}
                                        max={100}
                                        step={1}
                                        defaultValue={[field.value]}
                                        onValueChange={(vals) => field.onChange(vals[0])}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Privacy Policy Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Privacy Policy</h3>

                    <FormField
                        control={form.control}
                        name="security.privacyPolicyUrl"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Privacy Policy URL</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="https://example.com/privacy"
                                        {...field}
                                        value={field.value || ''}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="security.termsUrl"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Terms of Service URL</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="https://example.com/terms"
                                        {...field}
                                        value={field.value || ''}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
            </div>
        </div>
    )
}

export default SecuritySettings 