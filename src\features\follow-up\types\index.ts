/**
 * Follow-up Types
 * 
 * This file exports all type definitions for the follow-up feature
 */

import { z } from 'zod';
import {
    followUpOptionSchema,
    followUpQuestionSchema,
    followUpChainSchema,
    followUpResponseSchema
} from '../schemas/follow-up-schema';

/**
 * Follow-up option type
 */
export type FollowUpOption = z.infer<typeof followUpOptionSchema>;

/**
 * Follow-up question type
 */
export type FollowUpQuestion = z.infer<typeof followUpQuestionSchema>;

/**
 * Follow-up placement type
 */
export type FollowUpPlacement = 'beginning' | 'middle' | 'end';

/**
 * Follow-up condition operator type
 */
export type ConditionOperator = 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';

/**
 * Follow-up condition type
 */
export interface FollowUpCondition {
    field: string;
    operator: ConditionOperator;
    value: string;
}

/**
 * Follow-up chain type
 */
export type FollowUpChain = z.infer<typeof followUpChainSchema>;

/**
 * Follow-up trigger type type
 */
export type TriggerType = 'intent' | 'keyword' | 'sentiment' | 'custom';

/**
 * Follow-up trigger type
 */
export interface FollowUpTrigger {
    type: TriggerType;
    value: string;
}

/**
 * Follow-up response type
 */
export type FollowUpResponse = z.infer<typeof followUpResponseSchema>;

/**
 * Follow-up result type
 */
export interface FollowUpResult {
    chain: FollowUpChain;
    responses: FollowUpResponse[];
    metrics: {
        completionRate: number;
        averageTimeToComplete: number;
        mostSelectedOptions: Record<string, number>;
    };
}

/**
 * Follow-up filter options
 */
export interface FollowUpFilters {
    search?: string;
    isActive?: boolean;
    triggerType?: TriggerType;
    sortBy?: keyof FollowUpChain;
    sortDirection?: 'asc' | 'desc';
} 