/**
 * Widget Types
 *
 * This file contains all types related to the widget module.
 * These types are used throughout the application to ensure
 * consistency and type safety.
 */

/**
 * Widget Settings
 * Configuration options for a widget
 */
export interface WidgetSettings {
  // Appearance
  primaryColor?: string;
  secondaryColor?: string;
  fontFamily?: string;
  borderRadius?: number;
  chatIconSize?: number;
  width?: number;
  height?: number;

  // Position and layout
  position?: string;
  positionSettings?: {
    bottom?: number;
    right?: number;
    left?: number;
    top?: number;
  };

  // Content
  welcomeMessage?: string;
  headerTitle?: string;
  inputPlaceholder?: string;
  sendButtonText?: string;
  offlineMessage?: string;

  // Behavior
  autoOpenDelay?: number;
  startMinimized?: boolean;
  autoOpen?: boolean;
  showTypingIndicator?: boolean;
  enableUserRatings?: boolean;
  requireGuestInfo?: boolean;
  persistConversation?: boolean;
  preChat?: boolean;
  preChatSettings?: {
    templateId?: number;
    collectName?: boolean;
    collectEmail?: boolean;
    collectPhone?: boolean;
    collectMessage?: boolean;
  };
  postChat?: boolean;
  closeAfterInactivity?: boolean;
  inactivityTimeout?: number;

  // Mobile
  mobileBehavior?: string;

  // Advanced
  systemPrompt?: string;
  ai_model_id?: number | null;
  contextRetention?: 'session' | 'persistent' | 'none';
  maxMessagesStored?: number;
  customCSS?: string;

  // Avatar
  avatar?: {
    enabled?: boolean;
    imageUrl?: string;
    fallbackInitial?: string;
  };

  // Typography
  typography?: {
    fontSize?: number;
    fontWeight?: string;
    lineHeight?: number;
  };

  // Button customization
  buttonCustomization?: {
    animation?: 'none' | 'pulse' | 'bounce';
    icon?: string;
    text?: string;
    showText?: boolean;
  };

  // Visitor segmentation
  visitorSegmentation?: {
    enabled?: boolean;
    rules?: Array<{
      attribute: string;
      operator: string;
      value: string;
      action: string;
    }>;
  };

  // Auto-pop timing
  autoPopSettings?: {
    enabled?: boolean;
    timing?: number;
    scrollDepth?: number;
    exitIntent?: boolean;
    afterPageCount?: number;
  };

  // Security
  securitySettings?: {
    dataSanitization?: boolean;
    preventDataCollection?: boolean;
    cspEnabled?: boolean;
    enableSRI?: boolean;
  };
}

/**
 * Widget
 * Represents a widget in the system
 */
export interface Widget {
  id?: number;
  name: string;
  widget_id?: string;
  version?: string;
  ai_model_id?: number | null;
  settings?: WidgetSettings;
  allowed_domains?: string[];
  position_type?: 'fixed' | 'relative' | 'inline';
  position_settings?: Record<string, any>;
  custom_css?: string;
  behavior_rules?: Record<string, any>;
  logo_url?: string;
  typography?: Record<string, any>;
  button_customization?: Record<string, any>;
  mobile_settings?: Record<string, any>;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Analytics Summary
 * Summary of widget analytics
 */
export interface AnalyticsSummary {
  total_views: number;
  total_conversations: number;
  total_messages: number;
  engagement_rate: number;
  avg_messages_per_conversation: number;
  period: string;
}

/**
 * Analytics Event
 * Represents an analytics event
 */
export interface AnalyticsEvent {
  id: number;
  widget_id: number;
  event_type: string;
  visitor_id: string;
  url: string;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

/**
 * Pre-Chat Form Template
 * Template for pre-chat forms
 */
export interface PreChatFormTemplate {
  id: number;
  widgetId: number;
  title: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  fields: PreChatFormField[];
}

/**
 * Pre-Chat Form Field
 * Field in a pre-chat form
 */
export interface PreChatFormField {
  id: string;
  templateId: number;
  name: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'checkbox';
  placeholder?: string;
  options?: string[];
  isRequired: boolean;
  validationPattern?: string;
  errorMessage?: string;
  order: number;
}

/**
 * Post-Chat Survey
 * Survey shown after a chat session
 */
export interface PostChatSurvey {
  id: number;
  widget_id: number;
  title: string;
  description: string;
  thank_you_message: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  questions: SurveyQuestion[];
}

/**
 * Survey Question
 * Question in a post-chat survey
 */
export interface SurveyQuestion {
  id: number;
  survey_id: number;
  text: string;
  type: 'rating' | 'text' | 'select' | 'boolean';
  options?: string[];
  is_required: boolean;
  order: number;
}
