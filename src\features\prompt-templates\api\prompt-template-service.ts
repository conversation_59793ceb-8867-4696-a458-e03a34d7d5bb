/**
 * Prompt Template API Service
 * 
 * This file contains all API calls related to prompt template functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    PromptTemplate,
    TemplateFilters,
    PromptTemplateUsage,
    TemplateEvaluation,
    ProcessedTemplate
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for prompt templates
 */
const TEMPLATE_API = `${API_BASE_URL}/prompt-templates`;

/**
 * Prompt Template API Service
 */
export const promptTemplateService = {
    /**
     * Get all templates with optional filters
     */
    getTemplates: async (filters?: TemplateFilters): Promise<PromptTemplate[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(TEMPLATE_API, { params: filters });
        }, 'getTemplates');

        if (error || !response) {
            throw error || new Error('Failed to fetch prompt templates');
        }

        return response.data;
    },

    /**
     * Get a template by ID
     */
    getTemplate: async (id: string): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${TEMPLATE_API}/${id}`);
        }, 'getTemplate');

        if (error || !response) {
            throw error || new Error(`Failed to fetch prompt template with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Get templates for an AI model
     */
    getTemplatesForModel: async (modelId: string): Promise<PromptTemplate[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${TEMPLATE_API}/model/${modelId}`);
        }, 'getTemplatesForModel');

        if (error || !response) {
            throw error || new Error(`Failed to fetch templates for model with ID ${modelId}`);
        }

        return response.data;
    },

    /**
     * Get the default template
     */
    getDefaultTemplate: async (category: string): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${TEMPLATE_API}/default/${category}`);
        }, 'getDefaultTemplate');

        if (error || !response) {
            throw error || new Error(`Failed to fetch default template for category ${category}`);
        }

        return response.data;
    },

    /**
     * Create a new template
     */
    createTemplate: async (template: Omit<PromptTemplate, 'id'>): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(TEMPLATE_API, template);
        }, 'createTemplate');

        if (error || !response) {
            throw error || new Error('Failed to create template');
        }

        return response.data;
    },

    /**
     * Update a template
     */
    updateTemplate: async (id: string, template: Partial<PromptTemplate>): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${TEMPLATE_API}/${id}`, template);
        }, 'updateTemplate');

        if (error || !response) {
            throw error || new Error(`Failed to update template with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a template
     */
    deleteTemplate: async (id: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${TEMPLATE_API}/${id}`);
        }, 'deleteTemplate');

        if (error || !response) {
            throw error || new Error(`Failed to delete template with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Set a template as default for its category
     */
    setDefaultTemplate: async (id: string): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${TEMPLATE_API}/${id}/default`, { isDefault: true });
        }, 'setDefaultTemplate');

        if (error || !response) {
            throw error || new Error(`Failed to set template with ID ${id} as default`);
        }

        return response.data;
    },

    /**
     * Toggle template active status
     */
    toggleTemplateStatus: async (id: string, isActive: boolean): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${TEMPLATE_API}/${id}/toggle-status`, { isActive });
        }, 'toggleTemplateStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle status for template with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Process a template with variables
     */
    processTemplate: async (usage: PromptTemplateUsage): Promise<ProcessedTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${TEMPLATE_API}/process`, usage);
        }, 'processTemplate');

        if (error || !response) {
            throw error || new Error('Failed to process template');
        }

        return response.data;
    },

    /**
     * Submit template evaluation
     */
    evaluateTemplate: async (evaluation: Omit<TemplateEvaluation, 'id' | 'timestamp'>): Promise<TemplateEvaluation> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${TEMPLATE_API}/evaluate`, evaluation);
        }, 'evaluateTemplate');

        if (error || !response) {
            throw error || new Error('Failed to submit template evaluation');
        }

        return response.data;
    },

    /**
     * Clone a template
     */
    cloneTemplate: async (id: string, updates?: Partial<PromptTemplate>): Promise<PromptTemplate> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${TEMPLATE_API}/${id}/clone`, updates || {});
        }, 'cloneTemplate');

        if (error || !response) {
            throw error || new Error(`Failed to clone template with ID ${id}`);
        }

        return response.data;
    }
};

export default promptTemplateService; 