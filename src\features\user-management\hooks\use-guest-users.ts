/**
 * useGuestUsers Hook
 * 
 * Custom hook for guest user management
 */

import { useState, useCallback } from 'react'
import { guestUserService } from '../api/guest-user-service'
import { GuestUser, ChatMessage } from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useGuestUsers() {
  const [guestUsers, setGuestUsers] = useState<GuestUser[]>([])
  const [currentGuest, setCurrentGuest] = useState<GuestUser | null>(null)
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isDeleting, setIsDeleting] = useState<boolean>(false)
  const [isLoadingChat, setIsLoadingChat] = useState<boolean>(false)
  const [error, setError] = useState<AppError | null>(null)

  /**
   * Fetch all guest users
   */
  const fetchGuestUsers = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await guestUserService.getAllGuestUsers()
      setGuestUsers(response.data || [])
      return response.data
    } catch (err) {
      const appError = toAppError(err)
      setError(appError)
      return []
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Fetch a guest user by ID
   */
  const fetchGuestUser = useCallback(async (id: number) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await guestUserService.getGuestUserDetails(id)
      setCurrentGuest(response.data)
      return response.data
    } catch (err) {
      const appError = toAppError(err)
      setError(appError)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Delete a guest user
   */
  const deleteGuestUser = useCallback(async (id: number) => {
    setIsDeleting(true)
    setError(null)

    try {
      const result = await guestUserService.deleteGuestUser(id)
      
      // If deletion was successful, remove from local state
      if (result.success) {
        setGuestUsers(prev => prev.filter(user => user.id !== id))
        
        // If the deleted user was the current user, clear current user
        if (currentGuest?.id === id) {
          setCurrentGuest(null)
        }
      }
      
      return result.success
    } catch (err) {
      const appError = toAppError(err)
      setError(appError)
      return false
    } finally {
      setIsDeleting(false)
    }
  }, [currentGuest])

  /**
   * Fetch chat history for a guest user by session ID
   */
  const fetchChatHistory = useCallback(async (sessionId: string) => {
    setIsLoadingChat(true)
    setError(null)

    try {
      const response = await guestUserService.getGuestUserChatHistory(sessionId)
      setChatHistory(response.data || [])
      return response.data
    } catch (err) {
      const appError = toAppError(err)
      setError(appError)
      return []
    } finally {
      setIsLoadingChat(false)
    }
  }, [])

  /**
   * Set a guest user as the current guest
   */
  const selectGuestUser = useCallback((guest: GuestUser) => {
    setCurrentGuest(guest)
  }, [])

  /**
   * Clear the current guest user
   */
  const clearCurrentGuest = useCallback(() => {
    setCurrentGuest(null)
  }, [])

  /**
   * Clear chat history
   */
  const clearChatHistory = useCallback(() => {
    setChatHistory([])
  }, [])

  return {
    guestUsers,
    currentGuest,
    chatHistory,
    isLoading,
    isDeleting,
    isLoadingChat,
    error,
    fetchGuestUsers,
    fetchGuestUser,
    deleteGuestUser,
    fetchChatHistory,
    selectGuestUser,
    clearCurrentGuest,
    clearChatHistory
  }
} 