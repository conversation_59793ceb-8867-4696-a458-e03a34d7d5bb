/**
 * Branding API Service
 * 
 * This file contains all API calls related to branding functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    Branding,
    BrandingFilters,
    CreateBrandingInput,
    UpdateBrandingInput,
    MessageFormat,
    MessageFormatFilters,
    CreateMessageFormatInput,
    UpdateMessageFormatInput,
    FormatMessageInput
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for branding
 */
const BRANDING_API = `${API_BASE_URL}/branding`;

/**
 * Branding API Service
 */
export const brandingService = {
    /**
     * Get all brandings with optional filters
     */
    getBrandings: async (filters?: BrandingFilters): Promise<Branding[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(BRANDING_API, { params: filters });
        }, 'getBrandings');

        if (error || !response) {
            throw error || new Error('Failed to fetch brandings');
        }

        return response.data;
    },

    /**
     * Get a branding by ID
     */
    getBranding: async (id: string): Promise<Branding> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${BRANDING_API}/${id}`);
        }, 'getBranding');

        if (error || !response) {
            throw error || new Error(`Failed to fetch branding with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Get the default branding
     */
    getDefaultBranding: async (): Promise<Branding> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${BRANDING_API}/default`);
        }, 'getDefaultBranding');

        if (error || !response) {
            throw error || new Error('Failed to fetch default branding');
        }

        return response.data;
    },

    /**
     * Create a new branding
     */
    createBranding: async (branding: CreateBrandingInput): Promise<Branding> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(BRANDING_API, branding);
        }, 'createBranding');

        if (error || !response) {
            throw error || new Error('Failed to create branding');
        }

        return response.data;
    },

    /**
     * Update a branding
     */
    updateBranding: async (id: string, branding: UpdateBrandingInput): Promise<Branding> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${BRANDING_API}/${id}`, branding);
        }, 'updateBranding');

        if (error || !response) {
            throw error || new Error(`Failed to update branding with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a branding
     */
    deleteBranding: async (id: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${BRANDING_API}/${id}`);
        }, 'deleteBranding');

        if (error || !response) {
            throw error || new Error(`Failed to delete branding with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Set a branding as default
     */
    setDefaultBranding: async (id: string): Promise<Branding> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${BRANDING_API}/${id}/default`, { isDefault: true });
        }, 'setDefaultBranding');

        if (error || !response) {
            throw error || new Error(`Failed to set branding with ID ${id} as default`);
        }

        return response.data;
    },

    /**
     * Get all message formats with optional filters
     */
    getMessageFormats: async (filters?: MessageFormatFilters): Promise<MessageFormat[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${BRANDING_API}/formats`, { params: filters });
        }, 'getMessageFormats');

        if (error || !response) {
            throw error || new Error('Failed to fetch message formats');
        }

        return response.data;
    },

    /**
     * Get a message format by ID
     */
    getMessageFormat: async (id: string): Promise<MessageFormat> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${BRANDING_API}/formats/${id}`);
        }, 'getMessageFormat');

        if (error || !response) {
            throw error || new Error(`Failed to fetch message format with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Create a new message format
     */
    createMessageFormat: async (format: CreateMessageFormatInput): Promise<MessageFormat> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${BRANDING_API}/formats`, format);
        }, 'createMessageFormat');

        if (error || !response) {
            throw error || new Error('Failed to create message format');
        }

        return response.data;
    },

    /**
     * Update a message format
     */
    updateMessageFormat: async (id: string, format: UpdateMessageFormatInput): Promise<MessageFormat> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${BRANDING_API}/formats/${id}`, format);
        }, 'updateMessageFormat');

        if (error || !response) {
            throw error || new Error(`Failed to update message format with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a message format
     */
    deleteMessageFormat: async (id: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${BRANDING_API}/formats/${id}`);
        }, 'deleteMessageFormat');

        if (error || !response) {
            throw error || new Error(`Failed to delete message format with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Format a message using a template
     */
    formatMessage: async (data: FormatMessageInput): Promise<string> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${BRANDING_API}/formats/${data.formatId}/format`, { variables: data.variables });
        }, 'formatMessage');

        if (error || !response) {
            throw error || new Error(`Failed to format message using template with ID ${data.formatId}`);
        }

        return response.data.formattedMessage;
    },

    /**
     * Generate CSS variables from branding
     */
    generateCssVariables: async (id: string): Promise<string> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${BRANDING_API}/${id}/css-variables`);
        }, 'generateCssVariables');

        if (error || !response) {
            throw error || new Error(`Failed to generate CSS variables for branding with ID ${id}`);
        }

        return response.data.css;
    }
};

export default brandingService; 