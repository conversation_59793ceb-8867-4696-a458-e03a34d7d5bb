/**
 * useAuth Hook
 *
 * Custom hook for authentication management
 */

import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { authService } from '../api/auth-service'
import { User, LoginCredentials, RegistrationData, AuthResult } from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useAuth() {
    const [user, setUser] = useState<User | null>(null)
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false)
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [error, setError] = useState<AppError | null>(null)
    const navigate = useNavigate()

    /**
     * Initialize auth state
     */
    const initAuth = useCallback(async () => {
        setIsLoading(true)
        setError(null)

        if (authService.isAuthenticated()) {
            try {
                // Get stored user first for immediate UI update
                const storedUser = authService.getStoredUser()
                if (storedUser) {
                    setUser(storedUser)
                    setIsAuthenticated(true)
                }

                // Then fetch fresh user data from API
                const currentUser = await authService.getCurrentUser()
                setUser(currentUser)
                setIsAuthenticated(true)
            } catch (err) {
                console.error('Failed to initialize auth state', err)
                // If API call fails, still use stored user if available
                const storedUser = authService.getStoredUser()
                if (storedUser) {
                    setUser(storedUser)
                    setIsAuthenticated(true)
                } else {
                    setUser(null)
                    setIsAuthenticated(false)
                    // Clear token if getting user fails and no stored user
                    localStorage.removeItem('authToken')
                }
            }
        } else {
            setUser(null)
            setIsAuthenticated(false)
        }

        setIsLoading(false)
    }, [])

    /**
     * Login user
     */
    const login = async (credentials: LoginCredentials): Promise<AuthResult | null> => {
        setIsLoading(true)
        setError(null)

        try {
            const result = await authService.login(credentials)
            setUser(result.user)
            setIsAuthenticated(true)
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }

    /**
     * Register new user
     */
    const register = async (data: RegistrationData): Promise<AuthResult | null> => {
        setIsLoading(true)
        setError(null)

        try {
            const result = await authService.register(data)
            setUser(result.user)
            setIsAuthenticated(true)
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }

    /**
     * Logout user
     */
    const logout = async (redirectUrl: string = '/login'): Promise<void> => {
        setIsLoading(true)
        setError(null)

        try {
            await authService.logout()
            setUser(null)
            setIsAuthenticated(false)

            if (redirectUrl) {
                navigate(redirectUrl)
            }
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)

            // Even if API call fails, clear local state
            setUser(null)
            setIsAuthenticated(false)
        } finally {
            setIsLoading(false)
        }
    }

    /**
     * Refresh token and user data
     */
    const refreshAuth = async (): Promise<boolean> => {
        setIsLoading(true)
        setError(null)

        try {
            const result = await authService.refreshToken()
            setUser(result.user)
            setIsAuthenticated(true)
            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }

    /**
     * Update user in state
     */
    const updateUserState = (updatedUser: User): void => {
        setUser(updatedUser)
        // Update local storage
        localStorage.setItem('user', JSON.stringify(updatedUser))
    }

    /**
     * Clear error
     */
    const clearError = (): void => {
        setError(null)
    }

    // Initialize auth state on mount
    useEffect(() => {
        initAuth()
    }, [initAuth])

    return {
        user,
        isAuthenticated,
        isLoading,
        error,
        login,
        register,
        logout,
        refreshAuth,
        updateUserState,
        clearError
    }
}