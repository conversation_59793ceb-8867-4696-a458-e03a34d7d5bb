/**
 * AI Model Form Component
 * 
 * Main form for creating and editing AI models
 */

"use client"

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { aiModelSchema } from '../schemas/ai-model-schema'
import { AIModel, ModelParameters } from '../types'
import { useAIModel } from '../hooks'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FormField, FormLabel, FormControl, FormItem, FormMessage, Form } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, Check } from 'lucide-react'
import { ModelTestPanel } from './ModelTestPanel'

interface AIModelFormProps {
    initialData?: Partial<AIModel>
    modelId?: string
    onSuccess?: (model: AIModel) => void
}

export function AIModelForm({ initialData, modelId, onSuccess }: AIModelFormProps) {
    const [activeTab, setActiveTab] = useState('general')
    const [showApiKey, setShowApiKey] = useState(false)

    const {
        createModel,
        updateModel,
        testModel,
        isSaving,
        isTesting,
        testResult,
        error,
        validationErrors,
        clearErrors
    } = useAIModel()

    const form = useForm<AIModel>({
        resolver: zodResolver(aiModelSchema),
        defaultValues: initialData || {
            name: '',
            provider: 'openai',
            apiKey: '',
            modelId: 'gpt-4o',
            parameters: {
                temperature: 0.7,
                maxTokens: 2048,
                topP: 1,
                frequencyPenalty: 0,
                presencePenalty: 0
            },
            isActive: true,
            isDefault: false,
            capabilities: ['chat'],
            maxConcurrentRequests: 10
        }
    })

    const onSubmit = async (data: AIModel) => {
        clearErrors()

        try {
            let result

            if (modelId) {
                result = await updateModel(modelId, data)
            } else {
                result = await createModel(data)
            }

            if (result && onSuccess) {
                onSuccess(result)
            }
        } catch (err) {
            // Error handling is done by the hook
        }
    }

    const handleTest = async () => {
        const modelData = form.getValues()
        await testModel({
            modelId: modelData.modelId,
            prompt: "Hello, can you introduce yourself briefly?",
            parameters: modelData.parameters
        })
    }

    const providerOptions = [
        { value: 'openai', label: 'OpenAI' },
        { value: 'anthropic', label: 'Anthropic' },
        { value: 'gemini', label: 'Google Gemini' },
        { value: 'mistral', label: 'Mistral AI' },
        { value: 'custom', label: 'Custom Provider' }
    ]

    const capabilityOptions = [
        { value: 'chat', label: 'Chat' },
        { value: 'completion', label: 'Completion' },
        { value: 'embeddings', label: 'Embeddings' },
        { value: 'images', label: 'Image Generation' },
        { value: 'function-calling', label: 'Function Calling' }
    ]

    return (
        <div className="w-full max-w-4xl mx-auto">
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    <div className="space-y-6">
                        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
                            <TabsList className="grid grid-cols-3">
                                <TabsTrigger value="general">General Settings</TabsTrigger>
                                <TabsTrigger value="parameters">Model Parameters</TabsTrigger>
                                <TabsTrigger value="test">Test Model</TabsTrigger>
                            </TabsList>

                            <TabsContent value="general" className="space-y-6 pt-4">
                                <div className="grid grid-cols-2 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Model Name</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="My GPT-4 Model" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="provider"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Provider</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select a provider" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {providerOptions.map(option => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="modelId"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Model ID</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="gpt-4o" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="apiKey"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>API Key</FormLabel>
                                                <div className="flex gap-2">
                                                    <FormControl>
                                                        <Input
                                                            type={showApiKey ? "text" : "password"}
                                                            placeholder="sk-..."
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => setShowApiKey(!showApiKey)}
                                                    >
                                                        {showApiKey ? "Hide" : "Show"}
                                                    </Button>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {form.watch('provider') === 'custom' && (
                                        <FormField
                                            control={form.control}
                                            name="apiEndpoint"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>API Endpoint URL</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            placeholder="https://api.example.com/v1/chat/completions"
                                                            {...field}
                                                            value={field.value || ''}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    )}

                                    <FormField
                                        control={form.control}
                                        name="description"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Description</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="A brief description of this model"
                                                        rows={3}
                                                        {...field}
                                                        value={field.value || ''}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="capabilities"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Capabilities</FormLabel>
                                                <div className="flex flex-wrap gap-2 mt-2">
                                                    {capabilityOptions.map(option => {
                                                        const isSelected = field.value?.includes(option.value as any)
                                                        return (
                                                            <Badge
                                                                key={option.value}
                                                                variant={isSelected ? "default" : "outline"}
                                                                className="cursor-pointer"
                                                                onClick={() => {
                                                                    const currentValues = field.value || []
                                                                    const newValues = isSelected
                                                                        ? currentValues.filter(v => v !== option.value)
                                                                        : [...currentValues, option.value]
                                                                    field.onChange(newValues)
                                                                }}
                                                            >
                                                                {option.label}
                                                                {isSelected && <Check className="ml-1 h-3 w-3" />}
                                                            </Badge>
                                                        )
                                                    })}
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="isActive"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                                                <div className="space-y-0.5">
                                                    <FormLabel className="text-base">Active</FormLabel>
                                                    <p className="text-sm text-gray-500">
                                                        Enable or disable this model
                                                    </p>
                                                </div>
                                                <FormControl>
                                                    <Switch
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="isDefault"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                                                <div className="space-y-0.5">
                                                    <FormLabel className="text-base">Default Model</FormLabel>
                                                    <p className="text-sm text-gray-500">
                                                        Set as the system default
                                                    </p>
                                                </div>
                                                <FormControl>
                                                    <Switch
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </TabsContent>

                            <TabsContent value="parameters" className="space-y-6 pt-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Model Parameters</CardTitle>
                                        <CardDescription>
                                            Configure the behavior of this AI model
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <FormField
                                            control={form.control}
                                            name="parameters.temperature"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Temperature: {field.value}</FormLabel>
                                                    <FormControl>
                                                        <Slider
                                                            min={0}
                                                            max={2}
                                                            step={0.1}
                                                            defaultValue={[field.value]}
                                                            onValueChange={(vals) => field.onChange(vals[0])}
                                                        />
                                                    </FormControl>
                                                    <p className="text-sm text-gray-500">
                                                        Controls randomness: 0 is deterministic, 2 is very random
                                                    </p>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="parameters.maxTokens"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Max Tokens: {field.value}</FormLabel>
                                                    <FormControl>
                                                        <Slider
                                                            min={1}
                                                            max={8192}
                                                            step={1}
                                                            defaultValue={[field.value]}
                                                            onValueChange={(vals) => field.onChange(vals[0])}
                                                        />
                                                    </FormControl>
                                                    <p className="text-sm text-gray-500">
                                                        Maximum length of the response
                                                    </p>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="parameters.topP"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Top P: {field.value}</FormLabel>
                                                    <FormControl>
                                                        <Slider
                                                            min={0}
                                                            max={1}
                                                            step={0.01}
                                                            defaultValue={[field.value]}
                                                            onValueChange={(vals) => field.onChange(vals[0])}
                                                        />
                                                    </FormControl>
                                                    <p className="text-sm text-gray-500">
                                                        Controls diversity via nucleus sampling
                                                    </p>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <div className="grid grid-cols-2 gap-4">
                                            <FormField
                                                control={form.control}
                                                name="parameters.frequencyPenalty"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>Frequency Penalty: {field.value}</FormLabel>
                                                        <FormControl>
                                                            <Slider
                                                                min={0}
                                                                max={2}
                                                                step={0.1}
                                                                defaultValue={[field.value]}
                                                                onValueChange={(vals) => field.onChange(vals[0])}
                                                            />
                                                        </FormControl>
                                                        <p className="text-sm text-gray-500">
                                                            Reduces repetition of tokens
                                                        </p>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="parameters.presencePenalty"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>Presence Penalty: {field.value}</FormLabel>
                                                        <FormControl>
                                                            <Slider
                                                                min={0}
                                                                max={2}
                                                                step={0.1}
                                                                defaultValue={[field.value]}
                                                                onValueChange={(vals) => field.onChange(vals[0])}
                                                            />
                                                        </FormControl>
                                                        <p className="text-sm text-gray-500">
                                                            Reduces topic repetition
                                                        </p>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        <FormField
                                            control={form.control}
                                            name="systemPrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>System Prompt</FormLabel>
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="You are a helpful assistant..."
                                                            rows={4}
                                                            {...field}
                                                            value={field.value || ''}
                                                        />
                                                    </FormControl>
                                                    <p className="text-sm text-gray-500">
                                                        Instructions that prime the model's behavior
                                                    </p>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="maxConcurrentRequests"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Max Concurrent Requests: {field.value}</FormLabel>
                                                    <FormControl>
                                                        <Slider
                                                            min={1}
                                                            max={50}
                                                            step={1}
                                                            defaultValue={[field.value]}
                                                            onValueChange={(vals) => field.onChange(vals[0])}
                                                        />
                                                    </FormControl>
                                                    <p className="text-sm text-gray-500">
                                                        Maximum number of simultaneous requests
                                                    </p>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="test" className="space-y-6 pt-4">
                                <ModelTestPanel
                                    onTest={handleTest}
                                    isTesting={isTesting}
                                    testResult={testResult}
                                />
                            </TabsContent>
                        </Tabs>

                        {error && (
                            <div className="flex items-center p-4 mb-4 bg-red-50 text-red-700 rounded-md">
                                <AlertCircle className="h-5 w-5 mr-2" />
                                <span>{error.message}</span>
                            </div>
                        )}

                        <div className="flex justify-end space-x-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => form.reset()}
                                disabled={isSaving}
                            >
                                Reset
                            </Button>
                            <Button
                                type="submit"
                                disabled={isSaving}
                            >
                                {isSaving ? 'Saving...' : modelId ? 'Update Model' : 'Create Model'}
                            </Button>
                        </div>
                    </div>
                </form>
            </Form>
        </div>
    )
}

export default AIModelForm 