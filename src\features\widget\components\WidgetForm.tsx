/**
 * Widget Form Component
 * 
 * Main form for creating and editing widgets
 */

"use client"

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Tab } from '@headlessui/react'
import { widgetSchema } from '../schemas/widget-schema'
import { Widget } from '../types'
import { useWidget } from '../hooks'

import { GeneralSettings } from './form-sections/GeneralSettings'
import { AppearanceSettings } from './form-sections/AppearanceSettings'
import { BehaviorSettings } from './form-sections/BehaviorSettings'
import { SecuritySettings } from './form-sections/SecuritySettings'
import { AdvancedSettings } from './form-sections/AdvancedSettings'

interface WidgetFormProps {
    initialData?: Partial<Widget>
    widgetId?: string
    onSuccess?: (widget: Widget) => void
}

export function WidgetForm({ initialData, widgetId, onSuccess }: WidgetFormProps) {
    const [activeTab, setActiveTab] = useState(0)

    const {
        createWidget,
        updateWidget,
        isSaving,
        error,
        validationErrors,
        clearErrors
    } = useWidget()

    const form = useForm({
        resolver: zodResolver(widgetSchema),
        defaultValues: initialData || {
            name: '',
            description: '',
            isActive: true,
            projectId: 1,
            appearance: {
                theme: {
                    primary: '#6366F1',
                    background: '#FFFFFF',
                    text: '#1F2937'
                },
                branding: {
                    name: 'My Widget',
                    showBranding: true
                },
                layout: {
                    position: 'right',
                    width: 380,
                    height: 600,
                    borderRadius: 8
                },
                typography: {
                    fontSize: 14
                }
            },
            behavior: {
                welcomeMessage: 'How can I help you today?',
                showTimestamp: true,
                autoOpen: false,
                autoOpenDelay: 5,
                persistHistory: true,
                attachments: {
                    enabled: true,
                    maxSize: 5,
                    allowedTypes: ['image/*', 'application/pdf']
                },
                feedback: {
                    enabled: true,
                    showRatings: true,
                    showComments: true
                }
            },
            security: {
                domains: [],
                restrictByDomain: true,
                dataSanitization: true,
                maxRequestsPerMinute: 30,
                storageConsent: true
            }
        }
    })

    const onSubmit = async (data: Widget) => {
        clearErrors()

        try {
            let result

            if (widgetId) {
                result = await updateWidget(widgetId, data)
            } else {
                result = await createWidget(data)
            }

            if (result && onSuccess) {
                onSuccess(result)
            }
        } catch (err) {
            // Error handling is done by the hook
        }
    }

    const tabs = [
        { name: 'General', component: <GeneralSettings form={form} /> },
        { name: 'Appearance', component: <AppearanceSettings form={form} /> },
        { name: 'Behavior', component: <BehaviorSettings form={form} /> },
        { name: 'Security', component: <SecuritySettings form={form} /> },
        { name: 'Advanced', component: <AdvancedSettings form={form} /> }
    ]

    return (
        <div className="w-full max-w-5xl mx-auto">
            <form onSubmit={form.handleSubmit(onSubmit)}>
                <div className="mb-8">
                    <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
                        <Tab.List className="flex space-x-1 rounded-xl bg-gray-100 p-1">
                            {tabs.map((tab) => (
                                <Tab
                                    key={tab.name}
                                    className={({ selected }) =>
                                        `w-full rounded-lg py-2.5 text-sm font-medium leading-5 
                    ${selected
                                            ? 'bg-white shadow text-indigo-600'
                                            : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                                        }`
                                    }
                                >
                                    {tab.name}
                                </Tab>
                            ))}
                        </Tab.List>
                        <Tab.Panels className="mt-6">
                            {tabs.map((tab, idx) => (
                                <Tab.Panel key={idx}>{tab.component}</Tab.Panel>
                            ))}
                        </Tab.Panels>
                    </Tab.Group>
                </div>

                {error && (
                    <div className="mb-4 p-4 bg-red-50 text-red-700 rounded-md">
                        {error.message}
                    </div>
                )}

                <div className="flex justify-end space-x-4">
                    <button
                        type="button"
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
                        onClick={() => form.reset()}
                        disabled={isSaving}
                    >
                        Reset
                    </button>
                    <button
                        type="submit"
                        className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-70"
                        disabled={isSaving}
                    >
                        {isSaving ? 'Saving...' : widgetId ? 'Update Widget' : 'Create Widget'}
                    </button>
                </div>
            </form>
        </div>
    )
}

export default WidgetForm 