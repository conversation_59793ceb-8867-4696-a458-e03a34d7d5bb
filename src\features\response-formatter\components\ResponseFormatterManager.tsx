/**
 * Response Formatter Manager Component
 * 
 * This component provides UI for managing response formatters
 */

import { useState } from 'react'
import { useResponseFormatter } from '../hooks/use-response-formatter'
import { ResponseFormatter, FormatterType, StyleSettings } from '../types'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { Code, Eye, FileText, Plus, Save, Settings, Check, X } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { responseFormatterSchema } from '../schemas/response-formatter-schema'

export interface ResponseFormatterManagerProps {
  formatterType?: FormatterType;
  onFormatterSelect?: (formatter: ResponseFormatter) => void;
}

export function ResponseFormatterManager({ formatterType, onFormatterSelect }: ResponseFormatterManagerProps) {
  const [activeTab, setActiveTab] = useState<string>('formatting')
  const [previewContent, setPreviewContent] = useState<string>('This is a sample response that will be formatted according to your settings. It includes some basic formatting like **bold text**, *italic text*, and [links](https://example.com).')
  const [editMode, setEditMode] = useState<boolean>(false)
  
  const { toast } = useToast()
  
  const {
    formatters,
    currentFormatter,
    formattedResponse,
    isLoading,
    isSaving,
    isFormatting,
    error,
    fetchFormatters,
    fetchFormatter,
    fetchDefaultFormatter,
    createFormatter,
    updateFormatter,
    deleteFormatter,
    setAsDefaultFormatter,
    toggleFormatterStatus,
    formatResponse,
    previewFormatter,
    cloneFormatter,
    clearCurrentFormatter
  } = useResponseFormatter()

  const form = useForm<ResponseFormatter>({
    resolver: zodResolver(responseFormatterSchema),
    defaultValues: {
      name: '',
      description: '',
      type: 'standard',
      isActive: true,
      isDefault: false,
      allowUserCustomization: false,
      styleSettings: {
        headingStyle: 'normal',
        headingSize: 'medium',
        fontStyle: 'normal',
        listStyle: 'bullet',
        includeEmoji: false,
        colorScheme: 'default'
      }
    }
  })

  // Handle form submission
  const onSubmit = async (data: ResponseFormatter) => {
    try {
      if (currentFormatter?.id) {
        await updateFormatter(currentFormatter.id, data)
        toast({
          title: 'Formatter updated',
          description: 'The response formatter has been updated successfully'
        })
      } else {
        await createFormatter(data)
        toast({
          title: 'Formatter created',
          description: 'The new response formatter has been created successfully'
        })
      }
      setEditMode(false)
      await fetchFormatters(formatterType ? { type: formatterType } : undefined)
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to save formatter',
        variant: 'destructive'
      })
    }
  }

  // Handle formatter preview
  const handlePreview = async (id: string) => {
    try {
      const result = await previewFormatter(id, previewContent)
      if (result) {
        toast({
          title: 'Preview generated',
          description: 'The formatter preview has been generated'
        })
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to generate preview',
        variant: 'destructive'
      })
    }
  }

  // Handle creating a new formatter
  const handleCreateFormatter = () => {
    clearCurrentFormatter()
    form.reset({
      name: 'New Formatter',
      description: 'Enter a description for this formatter',
      type: formatterType || 'standard',
      isActive: true,
      isDefault: false,
      allowUserCustomization: false,
      styleSettings: {
        headingStyle: 'normal',
        headingSize: 'medium',
        fontStyle: 'normal',
        listStyle: 'bullet',
        includeEmoji: false,
        colorScheme: 'default'
      }
    })
    setEditMode(true)
  }

  // Handle selecting a formatter
  const handleSelectFormatter = async (id: string) => {
    const formatter = await fetchFormatter(id)
    if (formatter && onFormatterSelect) {
      onFormatterSelect(formatter)
    }
    form.reset(formatter || {})
    setEditMode(false)
  }

  // Handle deleting a formatter
  const handleDeleteFormatter = async (id: string) => {
    if (confirm('Are you sure you want to delete this formatter?')) {
      try {
        await deleteFormatter(id)
        toast({
          title: 'Formatter deleted',
          description: 'The formatter has been deleted successfully'
        })
        await fetchFormatters(formatterType ? { type: formatterType } : undefined)
      } catch (err) {
        toast({
          title: 'Error',
          description: 'Failed to delete formatter',
          variant: 'destructive'
        })
      }
    }
  }

  // Render content
  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="formatting">Formatting</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        
        <TabsContent value="formatting" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Formatters List */}
            <Card className="md:col-span-1">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">Formatters</CardTitle>
                  <Button variant="ghost" size="sm" onClick={handleCreateFormatter}>
                    <Plus className="h-4 w-4 mr-2" /> New
                  </Button>
                </div>
                <CardDescription>
                  Manage how responses are formatted
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="text-center py-4">Loading...</div>
                  ) : formatters.length > 0 ? (
                    formatters.map(formatter => (
                      <div 
                        key={formatter.id}
                        className={`p-3 border rounded-md cursor-pointer transition-colors hover:bg-muted ${currentFormatter?.id === formatter.id ? 'border-primary bg-muted/50' : ''}`}
                        onClick={() => handleSelectFormatter(formatter.id)}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">{formatter.name}</h3>
                            <p className="text-xs text-muted-foreground">
                              {formatter.type}
                              {formatter.isDefault && <span className="ml-2 text-primary">Default</span>}
                              {!formatter.isActive && <span className="ml-2 text-muted-foreground">(Inactive)</span>}
                            </p>
                          </div>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteFormatter(formatter.id);
                            }}
                          >
                            <X className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No formatters found
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Formatter Editor */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>
                  {editMode ? 'Edit Formatter' : (currentFormatter?.name || 'Select a formatter')}
                </CardTitle>
                <CardDescription>
                  {editMode ? 'Modify the formatter settings' : (currentFormatter?.description || 'Choose a formatter from the list or create a new one')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {editMode || currentFormatter ? (
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name</FormLabel>
                            <FormControl>
                              <Input {...field} disabled={!editMode} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Textarea {...field} disabled={!editMode} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Format Type</FormLabel>
                            <FormControl>
                              <Select 
                                value={field.value} 
                                onValueChange={field.onChange}
                                disabled={!editMode}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select format type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="standard">Standard</SelectItem>
                                  <SelectItem value="structured">Structured</SelectItem>
                                  <SelectItem value="bullet-list">Bullet List</SelectItem>
                                  <SelectItem value="numbered-list">Numbered List</SelectItem>
                                  <SelectItem value="table">Table</SelectItem>
                                  <SelectItem value="card">Card</SelectItem>
                                  <SelectItem value="custom">Custom</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription>
                              Choose the structure for formatting responses
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="isActive"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Active</FormLabel>
                                <FormDescription>
                                  Enable this formatter for use
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!editMode}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="isDefault"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Default</FormLabel>
                                <FormDescription>
                                  Set as default for its type
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!editMode}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="allowUserCustomization"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <div className="space-y-0.5">
                              <FormLabel>Allow User Customization</FormLabel>
                              <FormDescription>
                                Let users modify formatting options
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={!editMode}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      {editMode && (
                        <div className="flex justify-end gap-2">
                          <Button type="button" variant="outline" onClick={() => setEditMode(false)}>
                            Cancel
                          </Button>
                          <Button type="submit" disabled={isSaving}>
                            <Save className="h-4 w-4 mr-2" /> Save
                          </Button>
                        </div>
                      )}
                      
                      {!editMode && currentFormatter && (
                        <div className="flex justify-end gap-2">
                          <Button 
                            type="button" 
                            variant="outline" 
                            onClick={() => setEditMode(true)}
                          >
                            Edit
                          </Button>
                          <Button 
                            type="button"
                            variant="outline"
                            onClick={() => handlePreview(currentFormatter.id)}
                            disabled={isFormatting}
                          >
                            <Eye className="h-4 w-4 mr-2" /> Preview
                          </Button>
                        </div>
                      )}
                    </form>
                  </Form>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
                    <h3 className="text-lg font-medium">No formatter selected</h3>
                    <p className="text-muted-foreground mt-1">
                      Select a formatter from the list or create a new one
                    </p>
                    <Button className="mt-4" onClick={handleCreateFormatter}>
                      <Plus className="h-4 w-4 mr-2" /> Create Formatter
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Appearance Settings</CardTitle>
              <CardDescription>
                Configure the visual style of your formatted responses
              </CardDescription>
            </CardHeader>
            <CardContent>
              {currentFormatter ? (
                <Form {...form}>
                  <form className="space-y-6">
                    <FormField
                      control={form.control}
                      name="styleSettings.headingStyle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Heading Style</FormLabel>
                          <FormControl>
                            <RadioGroup 
                              value={field.value} 
                              onValueChange={field.onChange}
                              className="flex flex-wrap gap-4"
                              disabled={!editMode}
                            >
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="normal" />
                                </FormControl>
                                <FormLabel className="font-normal">Normal</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="uppercase" />
                                </FormControl>
                                <FormLabel className="font-normal uppercase">Uppercase</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="lowercase" />
                                </FormControl>
                                <FormLabel className="font-normal lowercase">Lowercase</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="capitalize" />
                                </FormControl>
                                <FormLabel className="font-normal capitalize">Capitalize</FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Separator />
                    
                    <FormField
                      control={form.control}
                      name="styleSettings.headingSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Heading Size</FormLabel>
                          <FormControl>
                            <RadioGroup 
                              value={field.value} 
                              onValueChange={field.onChange}
                              className="flex flex-wrap gap-4"
                              disabled={!editMode}
                            >
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="small" />
                                </FormControl>
                                <FormLabel className="font-normal text-sm">Small</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="medium" />
                                </FormControl>
                                <FormLabel className="font-normal">Medium</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="large" />
                                </FormControl>
                                <FormLabel className="font-normal text-lg">Large</FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Separator />
                    
                    <FormField
                      control={form.control}
                      name="styleSettings.fontStyle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Font Style</FormLabel>
                          <FormControl>
                            <RadioGroup 
                              value={field.value} 
                              onValueChange={field.onChange}
                              className="flex flex-wrap gap-4"
                              disabled={!editMode}
                            >
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="normal" />
                                </FormControl>
                                <FormLabel className="font-normal">Normal</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="italic" />
                                </FormControl>
                                <FormLabel className="italic font-normal">Italic</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="bold" />
                                </FormControl>
                                <FormLabel className="font-bold">Bold</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="bold-italic" />
                                </FormControl>
                                <FormLabel className="font-bold italic">Bold Italic</FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Select a formatter to view and edit appearance settings
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Templates</CardTitle>
              <CardDescription>
                Configure templates for different types of responses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Standard</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Basic formatting with headers, paragraphs, and emphasis
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Structured</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Clearly defined sections with titles and content blocks
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">List</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Bullet or numbered lists for items
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Table</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Tabular data with rows and columns
                      </p>
                    </CardContent>
                  </Card>
                </div>
                
                <Separator />
                
                <div>
                  <Label className="mb-2 block">Custom Template</Label>
                  <Textarea 
                    placeholder="Enter custom template markup"
                    className="font-mono h-32"
                    disabled={!editMode}
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    Use variables like {`{{content}}`}, {`{{title}}`}, and {`{{sections}}`} in your template
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>
                See how your formatter will display content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Sample Content</Label>
                  <Textarea 
                    value={previewContent}
                    onChange={(e) => setPreviewContent(e.target.value)}
                    placeholder="Enter sample content to preview formatting"
                    className="h-32"
                  />
                </div>
                
                {currentFormatter && (
                  <Button 
                    onClick={() => handlePreview(currentFormatter.id)}
                    disabled={isFormatting || !previewContent}
                  >
                    <Eye className="h-4 w-4 mr-2" /> Generate Preview
                  </Button>
                )}
                
                {formattedResponse && (
                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-2">Formatted Result</h3>
                    <div className="border rounded-md p-4 bg-muted/20">
                      <div dangerouslySetInnerHTML={{ __html: formattedResponse.html || formattedResponse.formattedContent }} />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 