/**
 * Integration Utilities
 * 
 * Helper functions for working with widget integrations
 */

import { Integration, IntegrationTypeInfo } from '../types';

/**
 * Get details about an integration type
 */
export function getIntegrationTypeDetails(type: Integration['type']): IntegrationTypeInfo {
    switch (type) {
        case 'slack':
            return {
                type: 'slack',
                name: 'Slack',
                description: 'Receive notifications in your Slack workspace',
                icon: 'slack',
                color: '#4A154B',
                documentationUrl: 'https://api.slack.com/messaging/webhooks',
            };

        case 'discord':
            return {
                type: 'discord',
                name: 'Discord',
                description: 'Get chat notifications in your Discord server',
                icon: 'discord',
                color: '#5865F2',
                documentationUrl: 'https://discord.com/developers/docs/resources/webhook',
            };

        case 'ms-teams':
            return {
                type: 'ms-teams',
                name: 'Microsoft Teams',
                description: 'Receive notifications in your Teams channels',
                icon: 'microsoft',
                color: '#6264A7',
                documentationUrl: 'https://docs.microsoft.com/en-us/microsoftteams/platform/webhooks-and-connectors/how-to/connectors-using',
            };

        case 'zapier':
            return {
                type: 'zapier',
                name: '<PERSON>ap<PERSON>',
                description: 'Connect to thousands of apps through Zapier',
                icon: 'zap',
                color: '#FF4A00',
                documentationUrl: 'https://zapier.com/apps/webhook/integrations',
            };

        case 'generic':
        default:
            return {
                type: 'generic',
                name: 'Custom Webhook',
                description: 'Connect to any service that supports webhooks',
                icon: 'webhook',
                color: '#0099E5',
                documentationUrl: 'https://en.wikipedia.org/wiki/Webhook',
            };
    }
}

/**
 * Generate test payload for an integration type
 */
export function generateTestPayload(integration: Integration): Record<string, any> {
    // Base payload data
    const baseData = {
        test: true,
        timestamp: new Date().toISOString(),
        user: {
            name: 'Test User',
            email: '<EMAIL>',
        },
        message: 'This is a test message from your chat widget',
        session_id: `test-session-${Math.floor(Date.now() / 1000)}`,
    };

    // Format payload based on integration type
    switch (integration.type) {
        case 'slack':
            return {
                text: "This is a test message from your chat widget",
                username: "Test User",
                icon_emoji: ":bell:",
                blocks: [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "*Test Message*\nThis is a test message from your chat widget."
                        }
                    },
                    {
                        "type": "context",
                        "elements": [
                            {
                                "type": "mrkdwn",
                                "text": "Sent by: Test User"
                            }
                        ]
                    }
                ]
            };

        case 'ms-teams':
            return {
                "@type": "MessageCard",
                "@context": "http://schema.org/extensions",
                "themeColor": "0076D7",
                "summary": "Test Message",
                "sections": [{
                    "activityTitle": "Test Notification",
                    "activitySubtitle": "From Chat Widget",
                    "text": "This is a test message from your chat widget"
                }]
            };

        case 'discord':
            return {
                content: "This is a test message from your chat widget",
                username: "Chat Widget",
                embeds: [{
                    title: "Test Notification",
                    description: "This is a test message from your chat widget",
                    color: 3447003
                }]
            };

        case 'zapier':
        case 'generic':
        default:
            // Generic webhook format
            return baseData;
    }
}

/**
 * Format event name for display
 */
export function formatEventName(event: string): string {
    return event
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * Get event description
 */
export function getEventDescription(event: string): string {
    switch (event) {
        case 'message_sent':
            return 'When a user sends a new message';
        case 'conversation_started':
            return 'When a new conversation begins';
        case 'conversation_ended':
            return 'When a conversation is ended or timed out';
        case 'feedback_received':
            return 'When a user provides feedback or a rating';
        case 'error_occurred':
            return 'When an error occurs during a conversation';
        default:
            return 'Event notification';
    }
}

/**
 * Get supported events for an integration type
 */
export function getSupportedEvents(type: Integration['type']): string[] {
    // All integration types support these events
    const commonEvents = [
        'message_sent',
        'conversation_started',
        'conversation_ended',
    ];

    // Add type-specific events
    switch (type) {
        case 'slack':
        case 'ms-teams':
        case 'discord':
            // These platforms support all events
            return [
                ...commonEvents,
                'feedback_received',
                'error_occurred',
            ];

        case 'zapier':
        case 'generic':
        default:
            // Generic webhooks support all events
            return [
                ...commonEvents,
                'feedback_received',
                'error_occurred',
            ];
    }
} 