/**
 * useUsers Hook
 * 
 * Custom hook for user management
 */

import { useState, useCallback } from 'react'
import { userService } from '../api/user-service'
import {
    User,
    UserUpdateData,
    UserFilters,
    UserPagination
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useUsers() {
    const [users, setUsers] = useState<User[]>([])
    const [userPagination, setUserPagination] = useState<Omit<UserPagination, 'users'>>({
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
    })
    const [currentUser, setCurrentUser] = useState<User | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isSaving, setIsSaving] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch users with pagination and filters
     */
    const fetchUsers = useCallback(async (page = 1, limit = 10, filters?: UserFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const result = await userService.getUsers(page, limit, filters)
            setUsers(result.users)
            setUserPagination({
                total: result.total,
                page: result.page,
                limit: result.limit,
                totalPages: result.totalPages
            })
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single user
     */
    const fetchUser = useCallback(async (id: string | number) => {
        setIsLoading(true)
        setError(null)

        try {
            const user = await userService.getUser(id)
            setCurrentUser(user)
            return user
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new user
     */
    const createUser = useCallback(async (user: Omit<User, 'id'>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newUser = await userService.createUser(user)
            setUsers(prev => [...prev, newUser])
            return newUser
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a user
     */
    const updateUser = useCallback(async (id: string | number, data: UserUpdateData) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedUser = await userService.updateUser(id, data)
            setUsers(prev => prev.map(u => u.id === id ? updatedUser : u))

            if (currentUser?.id === id) {
                setCurrentUser(updatedUser)
            }

            return updatedUser
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentUser])

    /**
     * Delete a user
     */
    const deleteUser = useCallback(async (id: string | number) => {
        setIsLoading(true)
        setError(null)

        try {
            await userService.deleteUser(id)
            setUsers(prev => prev.filter(u => u.id !== id))

            if (currentUser?.id === id) {
                setCurrentUser(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentUser])

    /**
     * Toggle user status
     */
    const toggleUserStatus = useCallback(async (id: string | number, isActive: boolean) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedUser = await userService.toggleUserStatus(id, isActive)
            setUsers(prev => prev.map(u => u.id === id ? updatedUser : u))

            if (currentUser?.id === id) {
                setCurrentUser(updatedUser)
            }

            return updatedUser
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentUser])

    /**
     * Update user role
     */
    const updateUserRole = useCallback(async (id: string | number, role: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedUser = await userService.updateUserRole(id, role)
            setUsers(prev => prev.map(u => u.id === id ? updatedUser : u))

            if (currentUser?.id === id) {
                setCurrentUser(updatedUser)
            }

            return updatedUser
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentUser])

    /**
     * Clear current user
     */
    const clearCurrentUser = useCallback(() => {
        setCurrentUser(null)
    }, [])

    /**
     * Clear error
     */
    const clearError = useCallback(() => {
        setError(null)
    }, [])

    return {
        users,
        userPagination,
        currentUser,
        isLoading,
        isSaving,
        error,
        fetchUsers,
        fetchUser,
        createUser,
        updateUser,
        deleteUser,
        toggleUserStatus,
        updateUserRole,
        clearCurrentUser,
        clearError
    }
} 