/**
 * Appearance Settings Form Section
 * 
 * Widget visual customization form fields
 */

import { UseFormReturn } from 'react-hook-form'
import { Widget } from '../../types'
import { FormField, FormLabel, FormControl, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'

interface AppearanceSettingsProps {
    form: UseFormReturn<Widget>
}

export function AppearanceSettings({ form }: AppearanceSettingsProps) {
    return (
        <div className="space-y-6">
            <h2 className="text-lg font-medium">Appearance Settings</h2>
            <p className="text-sm text-gray-500">
                Customize how your widget looks
            </p>

            <div className="space-y-8">
                {/* Theme Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Theme Colors</h3>

                    <FormField
                        control={form.control}
                        name="appearance.theme.primary"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Primary Color</FormLabel>
                                <div className="flex items-center gap-4">
                                    <div
                                        className="w-10 h-10 rounded border"
                                        style={{ backgroundColor: field.value }}
                                    />
                                    <FormControl>
                                        <Input type="color" {...field} />
                                    </FormControl>
                                </div>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="appearance.theme.background"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Background Color</FormLabel>
                                <div className="flex items-center gap-4">
                                    <div
                                        className="w-10 h-10 rounded border"
                                        style={{ backgroundColor: field.value }}
                                    />
                                    <FormControl>
                                        <Input type="color" {...field} />
                                    </FormControl>
                                </div>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="appearance.theme.text"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Text Color</FormLabel>
                                <div className="flex items-center gap-4">
                                    <div
                                        className="w-10 h-10 rounded border"
                                        style={{ backgroundColor: field.value }}
                                    />
                                    <FormControl>
                                        <Input type="color" {...field} />
                                    </FormControl>
                                </div>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Layout Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Layout</h3>

                    <FormField
                        control={form.control}
                        name="appearance.layout.position"
                        render={({ field }) => (
                            <FormItem className="space-y-3">
                                <FormLabel>Widget Position</FormLabel>
                                <FormControl>
                                    <RadioGroup
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                        className="flex space-x-4"
                                    >
                                        <FormItem className="flex items-center space-x-2">
                                            <FormControl>
                                                <RadioGroupItem value="left" />
                                            </FormControl>
                                            <FormLabel className="font-normal">Left</FormLabel>
                                        </FormItem>
                                        <FormItem className="flex items-center space-x-2">
                                            <FormControl>
                                                <RadioGroupItem value="right" />
                                            </FormControl>
                                            <FormLabel className="font-normal">Right</FormLabel>
                                        </FormItem>
                                        <FormItem className="flex items-center space-x-2">
                                            <FormControl>
                                                <RadioGroupItem value="bottom" />
                                            </FormControl>
                                            <FormLabel className="font-normal">Bottom</FormLabel>
                                        </FormItem>
                                    </RadioGroup>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="appearance.layout.borderRadius"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Border Radius: {field.value}px</FormLabel>
                                <FormControl>
                                    <Slider
                                        min={0}
                                        max={24}
                                        step={1}
                                        defaultValue={[field.value]}
                                        onValueChange={(vals) => field.onChange(vals[0])}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Branding Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Branding</h3>

                    <FormField
                        control={form.control}
                        name="appearance.branding.name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Brand Name</FormLabel>
                                <FormControl>
                                    <Input {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="appearance.branding.logo"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Logo URL</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="https://example.com/logo.png"
                                        {...field}
                                        value={field.value || ''}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="appearance.branding.showBranding"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Show Branding</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Display your brand identity in the widget
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
            </div>
        </div>
    )
}

export default AppearanceSettings 