/**
 * Centralized Zod Schema Definitions
 * 
 * This file contains commonly used Zod schemas and utilities for form validation
 * across the application. It helps maintain consistent validation patterns and
 * error messages.
 */

import { z } from 'zod';

// Common validation error messages
export const errorMessages = {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    url: 'Please enter a valid URL',
    min: (field: string, length: number) => `${field} must be at least ${length} characters`,
    max: (field: string, length: number) => `${field} must be at most ${length} characters`,
    integer: 'Please enter a whole number',
    positive: 'Please enter a positive number',
    date: 'Please enter a valid date',
    phone: 'Please enter a valid phone number',
    passwordMatch: 'Passwords do not match',
    invalidFormat: (format: string) => `Invalid format. Expected: ${format}`
};

// Common schema patterns
export const commonSchemas = {
    email: z.string().email(errorMessages.email),
    url: z.string().url(errorMessages.url),
    phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, errorMessages.phone),
    password: z.string().min(8, errorMessages.min('Password', 8)),
    nonEmptyString: z.string().min(1, errorMessages.required),
    integer: z.number().int(errorMessages.integer),
    positiveNumber: z.number().positive(errorMessages.positive),
    date: z.date({
        required_error: errorMessages.required,
        invalid_type_error: errorMessages.date,
    }),
    boolean: z.boolean(),
    uuid: z.string().uuid(),
    hexColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Please enter a valid hex color (e.g. #FF0000)'),
};

// Helper function to create schema with common validations
export function createFormSchema<T extends z.ZodRawShape>(shape: T) {
    return z.object(shape);
}

// Common schema transformers
export const transformers = {
    // Trim whitespace
    trim: (schema: z.ZodString) => schema.transform(s => s.trim()),

    // Convert to lowercase
    lowercase: (schema: z.ZodString) => schema.transform(s => s.toLowerCase()),

    // Convert to uppercase
    uppercase: (schema: z.ZodString) => schema.transform(s => s.toUpperCase()),

    // Parse string to number
    toNumber: (schema: z.ZodString) => schema.transform(s => {
        const parsed = Number(s);
        if (isNaN(parsed)) throw new Error('Invalid number');
        return parsed;
    }),

    // Parse string to boolean
    toBoolean: (schema: z.ZodString) => schema.transform(s => {
        if (s.toLowerCase() === 'true') return true;
        if (s.toLowerCase() === 'false') return false;
        throw new Error('Invalid boolean');
    }),

    // Parse string to date
    toDate: (schema: z.ZodString) => schema.transform(s => {
        const date = new Date(s);
        if (isNaN(date.getTime())) throw new Error('Invalid date');
        return date;
    }),
};

// Common form schema examples

// User form example
export const userSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    email: commonSchemas.email,
    password: commonSchemas.password,
    confirmPassword: commonSchemas.password,
}).refine(data => data.password === data.confirmPassword, {
    message: errorMessages.passwordMatch,
    path: ['confirmPassword'],
});

// AI Model form example
export const aiModelSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    provider: commonSchemas.nonEmptyString,
    apiKey: commonSchemas.nonEmptyString,
    temperature: z.number().min(0).max(1),
    maxTokens: commonSchemas.integer,
    isDefault: commonSchemas.boolean.optional(),
});

// Widget form example
export const widgetSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    position: z.enum(['left', 'right', 'bottom']),
    theme: commonSchemas.hexColor,
    isActive: commonSchemas.boolean,
    welcomeMessage: z.string().optional(),
});

// Export all schemas and utilities
export default {
    errorMessages,
    commonSchemas,
    createFormSchema,
    transformers,
    // Example schemas
    userSchema,
    aiModelSchema,
    widgetSchema,
}; 