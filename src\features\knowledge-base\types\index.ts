/**
 * Knowledge Base Types
 * 
 * This file exports all type definitions for the knowledge base feature
 */

/**
 * Document type definition
 */
export interface Document {
    id: string;
    title: string;
    content: string;
    fileType: 'pdf' | 'docx' | 'txt' | 'md';
    fileSize: number;
    createdAt: string;
    updatedAt: string;
    isProcessed: boolean;
    projectId: number;
    metadata?: Record<string, any>;
}

/**
 * Project type definition
 */
export interface Project {
    id: number;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
}

/**
 * Embedding vector type
 */
export interface Embedding {
    id: string;
    documentId: string;
    vector: number[];
    text: string;
    metadata?: Record<string, any>;
}

/**
 * Scraped URL type
 */
export interface ScrapedUrl {
    id: number;
    url: string;
    status: 'active' | 'inactive' | 'pending' | 'failed';
    lastScrapedAt: string;
    projectId: number;
    content?: string;
}

/**
 * Database connection type
 */
export interface DatabaseConnection {
    id: number;
    name: string;
    type: 'mysql' | 'postgres' | 'sqlserver' | 'mongodb';
    host: string;
    port: number;
    username: string;
    password?: string;
    database: string;
    status: 'active' | 'inactive';
    projectId: number;
    tables: string[];
}

/**
 * Search result item
 */
export interface SearchResult {
    id: string;
    content: string;
    documentId?: string;
    documentTitle?: string;
    score: number;
    sourceType: 'document' | 'web' | 'database';
    sourceId: string;
    metadata?: Record<string, any>;
}

/**
 * Knowledge base context
 */
export interface KnowledgeBaseContext {
    projectId: number;
    threshold: number;
    maxResults: number;
    preferredSources?: string[];
    includeSources?: boolean;
}

/**
 * Upload status
 */
export type UploadStatus = 'idle' | 'uploading' | 'processing' | 'success' | 'error';

/**
 * Upload progress
 */
export interface UploadProgress {
    status: UploadStatus;
    progress: number;
    message?: string;
    error?: string;
} 