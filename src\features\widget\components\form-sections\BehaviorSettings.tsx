/**
 * Behavior Settings Form Section
 * 
 * Widget behavior and functionality configuration
 */

import { UseFormReturn } from 'react-hook-form'
import { Widget } from '../../types'
import { FormField, FormLabel, FormControl, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'

interface BehaviorSettingsProps {
    form: UseFormReturn<Widget>
}

export function BehaviorSettings({ form }: BehaviorSettingsProps) {
    return (
        <div className="space-y-6">
            <h2 className="text-lg font-medium">Behavior Settings</h2>
            <p className="text-sm text-gray-500">
                Configure how your widget behaves and interacts with users
            </p>

            <div className="space-y-8">
                {/* Messages Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Messages</h3>

                    <FormField
                        control={form.control}
                        name="behavior.welcomeMessage"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Welcome Message</FormLabel>
                                <FormControl>
                                    <Textarea
                                        placeholder="How can I help you today?"
                                        rows={3}
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="behavior.initialMessage"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Initial Bot Message</FormLabel>
                                <FormControl>
                                    <Textarea
                                        placeholder="Optional message sent when chat first opens"
                                        rows={3}
                                        {...field}
                                        value={field.value || ''}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="behavior.showTimestamp"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Show Timestamps</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Display time when messages are sent
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Auto-behavior Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Auto Behavior</h3>

                    <FormField
                        control={form.control}
                        name="behavior.autoOpen"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Auto Open</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Automatically open the chat after delay
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {form.watch('behavior.autoOpen') && (
                        <FormField
                            control={form.control}
                            name="behavior.autoOpenDelay"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Auto Open Delay: {field.value} seconds</FormLabel>
                                    <FormControl>
                                        <Slider
                                            min={0}
                                            max={60}
                                            step={1}
                                            defaultValue={[field.value]}
                                            onValueChange={(vals) => field.onChange(vals[0])}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    )}

                    <FormField
                        control={form.control}
                        name="behavior.persistHistory"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Persist Chat History</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Save conversation when user returns
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Attachments Section */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium">Attachments</h3>

                    <FormField
                        control={form.control}
                        name="behavior.attachments.enabled"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                    <FormLabel className="text-base">Allow Attachments</FormLabel>
                                    <p className="text-sm text-gray-500">
                                        Let users upload files in chat
                                    </p>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {form.watch('behavior.attachments.enabled') && (
                        <>
                            <FormField
                                control={form.control}
                                name="behavior.attachments.maxSize"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Max File Size: {field.value} MB</FormLabel>
                                        <FormControl>
                                            <Slider
                                                min={1}
                                                max={20}
                                                step={1}
                                                defaultValue={[field.value]}
                                                onValueChange={(vals) => field.onChange(vals[0])}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}

export default BehaviorSettings 