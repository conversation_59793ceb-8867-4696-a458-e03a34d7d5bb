/**
 * AI Model API Service
 * 
 * This file contains all API calls related to AI model functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    AIModel,
    CreateAIModelInput,
    UpdateAIModelInput,
    AIModelFilters,
    TestModelRequest,
    TestModelResponse,
    AIModelWithStats
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for AI models
 */
const AI_MODEL_API = `${API_BASE_URL}/ai-models`;

/**
 * AI Model API Service
 */
export const aiModelService = {
    /**
     * Get all AI models with optional filters
     */
    getModels: async (filters?: AIModelFilters): Promise<AIModel[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(AI_MODEL_API, { params: filters });
        }, 'getModels');

        if (error || !response) {
            throw error || new Error('Failed to fetch AI models');
        }

        return response.data;
    },

    /**
     * Get an AI model by ID
     */
    getModel: async (modelId: string): Promise<AIModel> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${AI_MODEL_API}/${modelId}`);
        }, 'getModel');

        if (error || !response) {
            throw error || new Error(`Failed to fetch AI model with ID ${modelId}`);
        }

        return response.data;
    },

    /**
     * Get AI model with statistics
     */
    getModelWithStats: async (modelId: string): Promise<AIModelWithStats> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${AI_MODEL_API}/${modelId}/stats`);
        }, 'getModelWithStats');

        if (error || !response) {
            throw error || new Error(`Failed to fetch AI model stats for ID ${modelId}`);
        }

        return response.data;
    },

    /**
     * Create a new AI model
     */
    createModel: async (model: CreateAIModelInput): Promise<AIModel> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(AI_MODEL_API, model);
        }, 'createModel');

        if (error || !response) {
            throw error || new Error('Failed to create AI model');
        }

        return response.data;
    },

    /**
     * Update an AI model
     */
    updateModel: async (modelId: string, model: UpdateAIModelInput): Promise<AIModel> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${AI_MODEL_API}/${modelId}`, model);
        }, 'updateModel');

        if (error || !response) {
            throw error || new Error(`Failed to update AI model with ID ${modelId}`);
        }

        return response.data;
    },

    /**
     * Delete an AI model
     */
    deleteModel: async (modelId: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${AI_MODEL_API}/${modelId}`);
        }, 'deleteModel');

        if (error || !response) {
            throw error || new Error(`Failed to delete AI model with ID ${modelId}`);
        }

        return { success: true };
    },

    /**
     * Toggle AI model active status
     */
    toggleModelStatus: async (modelId: string, isActive: boolean): Promise<AIModel> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${AI_MODEL_API}/${modelId}/toggle`, { isActive });
        }, 'toggleModelStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle AI model status for ID ${modelId}`);
        }

        return response.data;
    },

    /**
     * Set AI model as default
     */
    setAsDefault: async (modelId: string): Promise<AIModel> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${AI_MODEL_API}/${modelId}/default`, { isDefault: true });
        }, 'setAsDefault');

        if (error || !response) {
            throw error || new Error(`Failed to set AI model as default for ID ${modelId}`);
        }

        return response.data;
    },

    /**
     * Test an AI model
     */
    testModel: async (request: TestModelRequest): Promise<TestModelResponse> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AI_MODEL_API}/test`, request);
        }, 'testModel');

        if (error || !response) {
            return {
                success: false,
                message: error?.message || 'Failed to test AI model',
                error: error?.message
            };
        }

        return response.data;
    },

    /**
     * Get available model IDs for a provider
     */
    getAvailableModels: async (provider: string): Promise<string[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${AI_MODEL_API}/available-models/${provider}`);
        }, 'getAvailableModels');

        if (error || !response) {
            throw error || new Error(`Failed to fetch available models for provider ${provider}`);
        }

        return response.data;
    }
};

export default aiModelService; 