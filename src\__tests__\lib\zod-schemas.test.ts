/**
 * Zod Schemas Tests
 * 
 * Tests for the centralized Zod schema definitions
 */

import {
    createFormSchema,
    commonSchemas,
    errorMessages,
    transformers,
    userSchema,
    aiModelSchema,
    widgetSchema
} from '@/lib/zod-schemas';
import { z } from 'zod';

describe('Zod Schema Utils', () => {
    describe('errorMessages', () => {
        it('should provide consistent error messages', () => {
            expect(errorMessages.required).toBe('This field is required');
            expect(errorMessages.email).toBe('Please enter a valid email address');
            expect(errorMessages.min('Password', 8)).toBe('Password must be at least 8 characters');
        });
    });

    describe('commonSchemas', () => {
        it('should validate email correctly', () => {
            const { email } = commonSchemas;

            expect(() => email.parse('<EMAIL>')).not.toThrow();
            expect(() => email.parse('invalid-email')).toThrow();
        });

        it('should validate URL correctly', () => {
            const { url } = commonSchemas;

            expect(() => url.parse('https://example.com')).not.toThrow();
            expect(() => url.parse('not-a-url')).toThrow();
        });

        it('should validate hex color correctly', () => {
            const { hexColor } = commonSchemas;

            expect(() => hexColor.parse('#FF0000')).not.toThrow();
            expect(() => hexColor.parse('#f00')).toThrow();
            expect(() => hexColor.parse('red')).toThrow();
        });
    });

    describe('createFormSchema', () => {
        it('should create a valid Zod schema', () => {
            const schema = createFormSchema({
                name: z.string(),
                age: z.number(),
            });

            expect(() => schema.parse({ name: 'John', age: 25 })).not.toThrow();
            expect(() => schema.parse({ name: 'John' })).toThrow();
        });
    });

    describe('transformers', () => {
        it('should trim whitespace', () => {
            const schema = transformers.trim(z.string());

            expect(schema.parse('  hello  ')).toBe('hello');
        });

        it('should convert to lowercase', () => {
            const schema = transformers.lowercase(z.string());

            expect(schema.parse('HELLO')).toBe('hello');
        });

        it('should convert to uppercase', () => {
            const schema = transformers.uppercase(z.string());

            expect(schema.parse('hello')).toBe('HELLO');
        });

        it('should convert string to number', () => {
            const schema = transformers.toNumber(z.string());

            expect(schema.parse('42')).toBe(42);
            expect(() => schema.parse('not-a-number')).toThrow();
        });
    });
});

describe('Predefined Schemas', () => {
    describe('userSchema', () => {
        it('should validate a valid user', () => {
            const validUser = {
                name: 'John Doe',
                email: '<EMAIL>',
                password: 'password123',
                confirmPassword: 'password123',
            };

            expect(() => userSchema.parse(validUser)).not.toThrow();
        });

        it('should reject when passwords do not match', () => {
            const invalidUser = {
                name: 'John Doe',
                email: '<EMAIL>',
                password: 'password123',
                confirmPassword: 'different',
            };

            expect(() => userSchema.parse(invalidUser)).toThrow();
        });
    });

    describe('aiModelSchema', () => {
        it('should validate a valid AI model', () => {
            const validModel = {
                name: 'GPT-4',
                provider: 'OpenAI',
                apiKey: 'sk-**********',
                temperature: 0.7,
                maxTokens: 2048,
                isDefault: true,
            };

            expect(() => aiModelSchema.parse(validModel)).not.toThrow();
        });

        it('should reject invalid temperature', () => {
            const invalidModel = {
                name: 'GPT-4',
                provider: 'OpenAI',
                apiKey: 'sk-**********',
                temperature: 1.5, // Outside valid range
                maxTokens: 2048,
            };

            expect(() => aiModelSchema.parse(invalidModel)).toThrow();
        });
    });

    describe('widgetSchema', () => {
        it('should validate a valid widget', () => {
            const validWidget = {
                name: 'Chat Widget',
                position: 'right',
                theme: '#FF0000',
                isActive: true,
            };

            expect(() => widgetSchema.parse(validWidget)).not.toThrow();
        });

        it('should reject invalid position', () => {
            const invalidWidget = {
                name: 'Chat Widget',
                position: 'top', // Not in enum
                theme: '#FF0000',
                isActive: true,
            };

            expect(() => widgetSchema.parse(invalidWidget)).toThrow();
        });
    });
}); 