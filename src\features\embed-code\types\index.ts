/**
 * Embed Code Types
 */

export interface EmbedConfiguration {
  id?: number
  widgetId: number
  embedType: 'inline' | 'popup' | 'floating'
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  allowedDomains: string[]
  customCss?: string
  autoOpen?: boolean
  openDelay?: number
  triggerSelector?: string
  zIndex?: number
  responsive?: boolean
  mobileBreakpoint?: number
  analytics?: boolean
  cookieConsent?: boolean
  gdprCompliant?: boolean
  customAttributes?: Record<string, string>
}

export interface EmbedCode {
  html: string
  javascript: string
  css?: string
  instructions: string
  previewUrl: string
  configuration: EmbedConfiguration
}

export interface EmbedStats {
  totalViews: number
  uniqueVisitors: number
  conversions: number
  averageSessionDuration: number
  topDomains: Array<{
    domain: string
    views: number
    percentage: number
  }>
  deviceBreakdown: {
    desktop: number
    mobile: number
    tablet: number
  }
  period: string
}

export interface EmbedValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
}

export interface EmbedTheme {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  textColor: string
  borderRadius: number
  fontFamily: string
  fontSize: number
  shadows: boolean
  animations: boolean
}
