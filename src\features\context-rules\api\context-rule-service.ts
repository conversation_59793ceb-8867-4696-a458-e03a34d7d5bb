/**
 * Context Rule API Service
 * 
 * This file contains all API calls related to context rules functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    ContextRule,
    RuleFilters,
    RuleEvaluationInput,
    ContextMatchResult,
    ContextData
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for context rules
 */
const CONTEXT_RULE_API = `${API_BASE_URL}/context-rules`;

/**
 * Context Rule API Service
 */
export const contextRuleService = {
    /**
     * Get all rules with optional filters
     */
    getRules: async (filters?: RuleFilters): Promise<ContextRule[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(CONTEXT_RULE_API, { params: filters });
        }, 'getRules');

        if (error || !response) {
            throw error || new Error('Failed to fetch context rules');
        }

        return response.data;
    },

    /**
     * Get a rule by ID
     */
    getRule: async (id: string): Promise<ContextRule> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${CONTEXT_RULE_API}/${id}`);
        }, 'getRule');

        if (error || !response) {
            throw error || new Error(`Failed to fetch context rule with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Create a new rule
     */
    createRule: async (rule: Omit<ContextRule, 'id'>): Promise<ContextRule> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(CONTEXT_RULE_API, rule);
        }, 'createRule');

        if (error || !response) {
            throw error || new Error('Failed to create context rule');
        }

        return response.data;
    },

    /**
     * Update a rule
     */
    updateRule: async (id: string, rule: Partial<ContextRule>): Promise<ContextRule> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${CONTEXT_RULE_API}/${id}`, rule);
        }, 'updateRule');

        if (error || !response) {
            throw error || new Error(`Failed to update context rule with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a rule
     */
    deleteRule: async (id: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${CONTEXT_RULE_API}/${id}`);
        }, 'deleteRule');

        if (error || !response) {
            throw error || new Error(`Failed to delete context rule with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Toggle rule active status
     */
    toggleRuleStatus: async (id: string, isActive: boolean): Promise<ContextRule> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.patch(`${CONTEXT_RULE_API}/${id}/toggle-status`, { isActive });
        }, 'toggleRuleStatus');

        if (error || !response) {
            throw error || new Error(`Failed to toggle status for context rule with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Evaluate rules against input
     */
    evaluateRules: async (input: RuleEvaluationInput): Promise<ContextMatchResult[]> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${CONTEXT_RULE_API}/evaluate`, input);
        }, 'evaluateRules');

        if (error || !response) {
            throw error || new Error('Failed to evaluate context rules');
        }

        return response.data;
    },

    /**
     * Get context data for a session
     */
    getContextData: async (sessionId: string, scope?: string): Promise<ContextData> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${CONTEXT_RULE_API}/data/${sessionId}`, { params: { scope } });
        }, 'getContextData');

        if (error || !response) {
            throw error || new Error(`Failed to fetch context data for session ${sessionId}`);
        }

        return response.data;
    },

    /**
     * Update context data for a session
     */
    updateContextData: async (sessionId: string, data: ContextData, scope?: string): Promise<ContextData> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${CONTEXT_RULE_API}/data/${sessionId}`, { data, scope });
        }, 'updateContextData');

        if (error || !response) {
            throw error || new Error(`Failed to update context data for session ${sessionId}`);
        }

        return response.data;
    },

    /**
     * Clear context data for a session
     */
    clearContextData: async (sessionId: string, scope?: string): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${CONTEXT_RULE_API}/data/${sessionId}`, { params: { scope } });
        }, 'clearContextData');

        if (error || !response) {
            throw error || new Error(`Failed to clear context data for session ${sessionId}`);
        }

        return { success: true };
    },

    /**
     * Test a rule against sample input
     */
    testRule: async (rule: ContextRule, input: RuleEvaluationInput): Promise<ContextMatchResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${CONTEXT_RULE_API}/test`, { rule, input });
        }, 'testRule');

        if (error || !response) {
            throw error || new Error('Failed to test context rule');
        }

        return response.data;
    }
};

export default contextRuleService; 