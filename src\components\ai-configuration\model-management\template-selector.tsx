import React, { useState, useEffect } from "react"
import {
  <PERSON>,
  Card<PERSON><PERSON>nt,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Loader2, FileText, PlusCircle } from "lucide-react"
import { templateService } from "@/utils/template-service"
import { TemplateTester } from "@/components/templates/template-tester"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ModelTemplateSelectorProps {
  modelId: number
  currentTemplateId?: number
  onTemplateAssigned?: (templateId: number) => void
}

interface Template {
  id: number
  name: string
  description: string
  category: string
  status: string
  is_default: boolean
  content: string
  variables: string[]
}

export function ModelTemplateSelector({
  modelId,
  currentTemplateId,
  onTemplateAssigned
}: ModelTemplateSelectorProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState<boolean>(false)
  const [templates, setTemplates] = useState<Template[]>([])
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | undefined>(currentTemplateId)
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)

  useEffect(() => {
    loadTemplates()
  }, [])

  useEffect(() => {
    if (selectedTemplateId) {
      loadTemplateDetails(selectedTemplateId)
    } else {
      setSelectedTemplate(null)
    }
  }, [selectedTemplateId])

  const loadTemplates = async () => {
    setLoading(true)
    try {
      const response = await templateService.getTemplates({ status: 'active' })
      if (response) {
        setTemplates(response)
        if (!selectedTemplateId && response.length > 0) {
          // If no template is selected and we have default template, select it
          const defaultTemplate = response.find(t => t.is_default)
          if (defaultTemplate) {
            setSelectedTemplateId(defaultTemplate.id)
          }
        }
      }
    } catch (error) {
      console.error("Error loading templates:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load templates. Please try again."
      })
    } finally {
      setLoading(false)
    }
  }

  const loadTemplateDetails = async (id: number) => {
    try {
      const template = templates.find(t => t.id === id)
      if (template) {
        setSelectedTemplate(template)
      } else {
        const response = await templateService.getTemplate(id)
        if (response) {
          setSelectedTemplate(response)
        }
      }
    } catch (error) {
      console.error("Error loading template details:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load template details."
      })
    }
  }

  const handleTemplateChange = (id: string) => {
    setSelectedTemplateId(parseInt(id))
  }

  const assignTemplateToModel = async () => {
    if (!selectedTemplateId || !modelId) return

    setLoading(true)
    try {
      await templateService.assignTemplateToModel(modelId, selectedTemplateId)
      toast({
        title: "Success",
        description: "Template assigned to model successfully."
      })

      if (onTemplateAssigned) {
        onTemplateAssigned(selectedTemplateId)
      }
    } catch (error) {
      console.error("Error assigning template:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to assign template to model. Please try again."
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Template Selection
        </CardTitle>
        <CardDescription>
          Choose a template to use with this AI model for consistent responses.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {loading && templates.length === 0 ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="template">Select Template</Label>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      <PlusCircle className="h-4 w-4" />
                      Create New
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <div className="py-4">
                      <iframe
                        src="/templates/new"
                        className="w-full h-[80vh]"
                        title="Create New Template"
                      />
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              <Select
                value={selectedTemplateId?.toString() || ""}
                onValueChange={handleTemplateChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
                <SelectContent>
                  {templates.length > 0 ? (
                    templates.map((template) => (
                      <SelectItem
                        key={template.id}
                        value={template.id.toString()}
                      >
                        {template.name} {template.is_default && "(Default)"}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>
                      No templates available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            {selectedTemplate && (
              <Tabs defaultValue="details">
                <TabsList className="mb-4">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="test">Test Template</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Template Details</h3>
                    <div className="mt-2 p-4 border rounded-md bg-gray-50">
                      <p className="text-gray-700 font-medium">{selectedTemplate.name}</p>
                      <p className="text-gray-500 text-sm mt-1">{selectedTemplate.description}</p>
                      <div className="mt-2 flex flex-wrap gap-2">
                        <Badge variant="outline">{selectedTemplate.category}</Badge>
                        <Badge variant="outline">{selectedTemplate.status}</Badge>
                        {selectedTemplate.is_default && (
                          <Badge variant="secondary">Default</Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium">Template Content</h3>
                    <div className="mt-2 p-4 border rounded-md bg-gray-50 whitespace-pre-wrap font-mono text-sm">
                      {selectedTemplate.content}
                    </div>
                  </div>

                  {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium">Variables</h3>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {selectedTemplate.variables.map((variable) => (
                          <Badge key={variable} variant="secondary">
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="test">
                  <TemplateTester
                    templateId={selectedTemplate.id}
                    templateContent={selectedTemplate.content}
                    variables={selectedTemplate.variables}
                  />
                </TabsContent>
              </Tabs>
            )}
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          onClick={assignTemplateToModel}
          disabled={loading || !selectedTemplateId}
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Assign Template to Model
        </Button>
      </CardFooter>
    </Card>
  )
}
