/**
 * useAIModel Hook
 * 
 * Custom hook for managing AI models
 */

import { useState, useCallback, useEffect } from 'react';
import {
    AIModel,
    AIModelFilters,
    CreateAIModelInput,
    UpdateAIModelInput,
    TestModelRequest,
    TestModelResponse
} from '../types';
import { aiModelSchema } from '../schemas/ai-model-schema';
import { aiModelService } from '../api/ai-model-service';
import { toAppError, AppError } from '@/lib/error-handler';
import { z } from 'zod';

interface UseAIModelProps {
    initialModelId?: string;
}

export function useAIModel({ initialModelId }: UseAIModelProps = {}) {
    const [models, setModels] = useState<AIModel[]>([]);
    const [currentModel, setCurrentModel] = useState<AIModel | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isTesting, setIsTesting] = useState(false);
    const [testResult, setTestResult] = useState<TestModelResponse | null>(null);
    const [error, setError] = useState<AppError | null>(null);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    /**
     * Fetch all AI models with optional filters
     */
    const fetchModels = useCallback(async (filters?: AIModelFilters) => {
        setIsLoading(true);
        setError(null);

        try {
            const fetchedModels = await aiModelService.getModels(filters);
            setModels(fetchedModels);
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Fetch a single AI model by ID
     */
    const fetchModel = useCallback(async (modelId: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const model = await aiModelService.getModel(modelId);
            setCurrentModel(model);
            return model;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return null;
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Create a new AI model
     */
    const createModel = useCallback(async (modelData: CreateAIModelInput) => {
        setIsSaving(true);
        setError(null);
        setValidationErrors({});

        try {
            // Validate with Zod schema
            aiModelSchema.parse(modelData);

            const newModel = await aiModelService.createModel(modelData);
            setModels(prev => [...prev, newModel]);
            setCurrentModel(newModel);
            return newModel;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);

            // Handle Zod validation errors
            if (err instanceof z.ZodError) {
                const errors: Record<string, string> = {};
                err.errors.forEach(e => {
                    const path = e.path.join('.');
                    errors[path] = e.message;
                });
                setValidationErrors(errors);
            }

            return null;
        } finally {
            setIsSaving(false);
        }
    }, []);

    /**
     * Update an existing AI model
     */
    const updateModel = useCallback(async (modelId: string, modelData: UpdateAIModelInput) => {
        setIsSaving(true);
        setError(null);
        setValidationErrors({});

        try {
            // Get current model data to merge with updates for validation
            const currentData = currentModel && currentModel.id === modelId
                ? currentModel
                : await aiModelService.getModel(modelId);

            // Merge current data with updates
            const mergedData = { ...currentData, ...modelData };

            // Validate with Zod schema
            aiModelSchema.parse(mergedData);

            const updatedModel = await aiModelService.updateModel(modelId, modelData);

            // Update models list
            setModels(prev =>
                prev.map(m => m.id === modelId ? updatedModel : m)
            );

            // Update current model if it's the one being edited
            if (currentModel?.id === modelId) {
                setCurrentModel(updatedModel);
            }

            return updatedModel;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);

            // Handle Zod validation errors
            if (err instanceof z.ZodError) {
                const errors: Record<string, string> = {};
                err.errors.forEach(e => {
                    const path = e.path.join('.');
                    errors[path] = e.message;
                });
                setValidationErrors(errors);
            }

            return null;
        } finally {
            setIsSaving(false);
        }
    }, [currentModel]);

    /**
     * Delete an AI model
     */
    const deleteModel = useCallback(async (modelId: string) => {
        setIsLoading(true);
        setError(null);

        try {
            await aiModelService.deleteModel(modelId);

            // Remove from models list
            setModels(prev => prev.filter(m => m.id !== modelId));

            // Clear current model if it's the one being deleted
            if (currentModel?.id === modelId) {
                setCurrentModel(null);
            }

            return true;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return false;
        } finally {
            setIsLoading(false);
        }
    }, [currentModel]);

    /**
     * Toggle AI model active status
     */
    const toggleModelStatus = useCallback(async (modelId: string, isActive: boolean) => {
        setError(null);

        try {
            const updatedModel = await aiModelService.toggleModelStatus(modelId, isActive);

            // Update models list
            setModels(prev =>
                prev.map(m => m.id === modelId ? updatedModel : m)
            );

            // Update current model if it's the one being toggled
            if (currentModel?.id === modelId) {
                setCurrentModel(updatedModel);
            }

            return updatedModel;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return null;
        }
    }, [currentModel]);

    /**
     * Set a model as default
     */
    const setAsDefault = useCallback(async (modelId: string) => {
        setError(null);

        try {
            const updatedModel = await aiModelService.setAsDefault(modelId);

            // Update all models to ensure only one is default
            fetchModels();

            // Update current model if it's the one being set as default
            if (currentModel?.id === modelId) {
                setCurrentModel(updatedModel);
            }

            return updatedModel;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return null;
        }
    }, [currentModel, fetchModels]);

    /**
     * Test an AI model
     */
    const testModel = useCallback(async (request: TestModelRequest) => {
        setIsTesting(true);
        setError(null);
        setTestResult(null);

        try {
            const result = await aiModelService.testModel(request);
            setTestResult(result);
            return result;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);

            const errorResult: TestModelResponse = {
                success: false,
                message: appError.message,
                error: appError.message
            };

            setTestResult(errorResult);
            return errorResult;
        } finally {
            setIsTesting(false);
        }
    }, []);

    /**
     * Get available model IDs for a provider
     */
    const getAvailableModels = useCallback(async (provider: string) => {
        setIsLoading(true);
        setError(null);

        try {
            return await aiModelService.getAvailableModels(provider);
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return [];
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Clear current model
     */
    const clearCurrentModel = useCallback(() => {
        setCurrentModel(null);
        setValidationErrors({});
        setTestResult(null);
    }, []);

    /**
     * Clear all errors
     */
    const clearErrors = useCallback(() => {
        setError(null);
        setValidationErrors({});
        setTestResult(null);
    }, []);

    // Fetch initial data if modelId is provided
    useEffect(() => {
        if (initialModelId) {
            fetchModel(initialModelId);
        } else {
            fetchModels();
        }
    }, [initialModelId, fetchModel, fetchModels]);

    return {
        models,
        currentModel,
        isLoading,
        isSaving,
        isTesting,
        testResult,
        error,
        validationErrors,
        fetchModels,
        fetchModel,
        createModel,
        updateModel,
        deleteModel,
        toggleModelStatus,
        setAsDefault,
        testModel,
        getAvailableModels,
        clearCurrentModel,
        clearErrors,
    };
} 