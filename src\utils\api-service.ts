// This file is a compatibility layer for code that imports from api-service.ts
// It re-exports everything from the new modular files to maintain backward compatibility

// Import the API instance
import api from "./api";

// Import services from their dedicated files
import * as authServiceModule from './authService';
import { guestUserAdminService as guestUserAdminServiceModule } from './guest-user-service';
import { widgetService as widgetServiceModule } from './widgetService';
import { roleService as roleServiceModule } from './roleService';
import { permissionService as permissionServiceModule } from './permissionService';

// Re-export the API instance as default
export default api;

// Re-export auth services
export const authService = {
  login: authServiceModule.login,
  register: authServiceModule.register,
  logout: authServiceModule.logout,
  getCurrentUser: authServiceModule.getUser,
  getCsrfToken: authServiceModule.getCsrfToken,
  checkAuth: authServiceModule.checkAuth,
};

// User management services
export const userService = {
  getAllUsers: async () => {
    return api.get("users");
  },

  getUser: async (id: number) => {
    return api.get(`users/${id}`);
  },

  createUser: async (userData: { name: string; email: string; password: string; status: string }) => {
    return api.post("users", userData);
  },

  updateUser: async (id: number, userData: { name?: string; email?: string; password?: string; status?: string }) => {
    return api.put(`users/${id}`, userData);
  },

  deleteUser: async (id: number) => {
    return api.delete(`users/${id}`);
  },

  assignRoles: async (userId: number, roles: number[]) => {
    return api.post(`users/${userId}/roles`, { roles });
  }
};

// Re-export role and permission services
export const roleService = roleServiceModule;
export const permissionService = permissionServiceModule;

// Re-export guest user admin services
export const guestUserAdminService = guestUserAdminServiceModule;

// Guest user services
export const guestUserService = {
  register: async (data: { fullname: string; email?: string; phone: string; widget_id: string }) => {
    return api.post('guest/register', data);
  },

  validateSession: async (sessionId: string) => {
    return api.post('guest/validate', { session_id: sessionId });
  },

  getDetails: async (sessionId: string) => {
    return api.post('guest/details', { session_id: sessionId });
  },
};

// Re-export widget services
export const widgetService = widgetServiceModule;

// Widget statistics services
export const statisticsService = {
  getWidgetStats: async (widgetId: string, period: string = '30d') => {
    return api.get(`widgets/${widgetId}/statistics?period=${period}`);
  },

  getRealTimeStats: async (widgetId: string) => {
    return api.get(`widgets/${widgetId}/statistics/realtime`);
  },
};

// Pre-chat form services
export const preChatFormService = {
  getTemplates: async (widgetId: string) => {
    return api.get(`widgets/${widgetId}/pre-chat-templates`);
  },

  createTemplate: async (widgetId: string, templateData: any) => {
    return api.post(`widgets/${widgetId}/pre-chat-templates`, templateData);
  },

  updateTemplate: async (widgetId: string, templateId: number, templateData: any) => {
    return api.put(`widgets/${widgetId}/pre-chat-templates/${templateId}`, templateData);
  },

  deleteTemplate: async (widgetId: string, templateId: number) => {
    return api.delete(`widgets/${widgetId}/pre-chat-templates/${templateId}`);
  },

  activateTemplate: async (widgetId: string, templateId: number) => {
    return api.post(`widgets/${widgetId}/pre-chat-templates/${templateId}/activate`);
  },

  getForm: async (widgetId: string) => {
    return api.get(`pre-chat-form?widget_id=${widgetId}`);
  },

  submitForm: async (data: any) => {
    return api.post('pre-chat-form/submit', data);
  },
};

// Post-chat survey services
export const postChatSurveyService = {
  getSurveys: async (widgetId: string) => {
    return api.get(`widgets/${widgetId}/post-chat-surveys`);
  },

  createSurvey: async (widgetId: string, surveyData: any) => {
    return api.post(`widgets/${widgetId}/post-chat-surveys`, surveyData);
  },

  updateSurvey: async (widgetId: string, surveyId: number, surveyData: any) => {
    return api.put(`widgets/${widgetId}/post-chat-surveys/${surveyId}`, surveyData);
  },

  deleteSurvey: async (widgetId: string, surveyId: number) => {
    return api.delete(`widgets/${widgetId}/post-chat-surveys/${surveyId}`);
  },

  activateSurvey: async (widgetId: string, surveyId: number) => {
    return api.post(`widgets/${widgetId}/post-chat-surveys/${surveyId}/activate`);
  },

  getSurvey: async (widgetId: string) => {
    return api.get(`post-chat-survey?widget_id=${widgetId}`);
  },

  submitSurvey: async (data: any) => {
    return api.post('post-chat-survey/submit', data);
  },
};

// Message rating service
export const messageRatingService = {
  rateMessage: async (data: {
    message_id: number;
    rating: 'thumbsUp' | 'thumbsDown';
    session_id: string;
    feedback?: string;
  }) => {
    return api.post('chat/rate', data);
  },
};

// Note: preChatService and postChatService are now consolidated into preChatFormService and postChatSurveyService above

// Export the main API instance for direct use
export const apiService = api;
