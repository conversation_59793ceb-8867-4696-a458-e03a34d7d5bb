/**
 * Error Handler Utility
 * 
 * This file provides standardized error handling utilities for the application.
 * It helps with logging, formatting, and presenting errors in a consistent way.
 */

import { ERROR_MESSAGES } from './constants';
import { ZodError } from 'zod';

// Types of errors we want to handle specifically
export enum ErrorType {
  API = 'api',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  NETWORK = 'network',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

// Structured error object
export interface AppError {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: Record<string, any>;
  originalError?: unknown;
}

/**
 * Convert an error to a standardized AppError
 */
export function toAppError(error: unknown): AppError {
  // Handle Axios errors
  if (error && typeof error === 'object' && 'isAxiosError' in error && error.isAxiosError) {
    // Network error
    if (!error.response) {
      return {
        type: ErrorType.NETWORK,
        message: ERROR_MESSAGES.NETWORK,
        originalError: error,
      };
    }

    // Server errors
    const status = error.response.status;
    const data = error.response.data;

    if (status === 401) {
      return {
        type: ErrorType.AUTHENTICATION,
        message: data?.message || ERROR_MESSAGES.UNAUTHORIZED,
        code: status,
        details: data,
        originalError: error,
      };
    }

    if (status === 403) {
      return {
        type: ErrorType.AUTHORIZATION,
        message: data?.message || ERROR_MESSAGES.UNAUTHORIZED,
        code: status,
        details: data,
        originalError: error,
      };
    }

    if (status === 404) {
      return {
        type: ErrorType.NOT_FOUND,
        message: data?.message || ERROR_MESSAGES.NOT_FOUND,
        code: status,
        details: data,
        originalError: error,
      };
    }

    if (status === 422) {
      return {
        type: ErrorType.VALIDATION,
        message: data?.message || ERROR_MESSAGES.VALIDATION,
        code: status,
        details: data?.errors || {},
        originalError: error,
      };
    }

    // Generic API error
    return {
      type: ErrorType.API,
      message: data?.message || ERROR_MESSAGES.GENERAL,
      code: status,
      details: data,
      originalError: error,
    };
  }

  // Handle Zod errors
  if (error instanceof ZodError) {
    const details: Record<string, string[]> = {};
    
    error.errors.forEach((err) => {
      const path = err.path.join('.');
      if (!details[path]) {
        details[path] = [];
      }
      details[path].push(err.message);
    });

    return {
      type: ErrorType.VALIDATION,
      message: ERROR_MESSAGES.VALIDATION,
      details,
      originalError: error,
    };
  }

  // Handle regular Error objects
  if (error instanceof Error) {
    return {
      type: ErrorType.UNKNOWN,
      message: error.message || ERROR_MESSAGES.GENERAL,
      originalError: error,
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      type: ErrorType.UNKNOWN,
      message: error,
    };
  }

  // Fallback for completely unknown errors
  return {
    type: ErrorType.UNKNOWN,
    message: ERROR_MESSAGES.GENERAL,
    originalError: error,
  };
}

/**
 * Format validation errors from Zod or API for display
 */
export function formatValidationErrors(error: AppError): Record<string, string> {
  if (!error.details) {
    return {};
  }

  const result: Record<string, string> = {};

  // Handle different validation error formats
  Object.entries(error.details).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      // Format: { field: ['Error 1', 'Error 2'] }
      result[key] = value[0];
    } else if (typeof value === 'string') {
      // Format: { field: 'Error message' }
      result[key] = value;
    } else if (value && typeof value === 'object') {
      // Nested objects - flatten one level
      Object.entries(value).forEach(([nestedKey, nestedValue]) => {
        const fullKey = `${key}.${nestedKey}`;
        if (Array.isArray(nestedValue)) {
          result[fullKey] = nestedValue[0];
        } else if (typeof nestedValue === 'string') {
          result[fullKey] = nestedValue;
        }
      });
    }
  });

  return result;
}

/**
 * Log an error with standardized format
 */
export function logError(error: unknown, context?: string): void {
  const appError = toAppError(error);
  
  console.error(
    `[${appError.type.toUpperCase()}]${context ? ` [${context}]` : ''}: ${appError.message}`,
    {
      code: appError.code,
      details: appError.details,
      originalError: appError.originalError,
    }
  );
}

/**
 * Safely try to execute a function and return a result or error
 */
export async function tryCatch<T>(
  fn: () => Promise<T>,
  context?: string
): Promise<[T | null, AppError | null]> {
  try {
    const result = await fn();
    return [result, null];
  } catch (error) {
    const appError = toAppError(error);
    logError(appError, context);
    return [null, appError];
  }
}

// Export all error handling utilities
export default {
  ErrorType,
  toAppError,
  formatValidationErrors,
  logError,
  tryCatch,
}; 