/**
 * Context Rules Types
 * 
 * This file exports all type definitions for the context rules feature
 */

import { z } from 'zod';
import {
    contextConditionSchema,
    contextActionSchema,
    contextRuleSchema,
    contextMatchResultSchema,
    contextDataSchema
} from '../schemas/context-rule-schema';

/**
 * Context condition type
 */
export type ContextCondition = z.infer<typeof contextConditionSchema>;

/**
 * Condition type enum
 */
export type ConditionType = 'message' | 'metadata' | 'intent' | 'entity' | 'session' | 'custom';

/**
 * Condition operator enum
 */
export type ConditionOperator = 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'exists' | 'greaterThan' | 'lessThan' | 'regex';

/**
 * Context action type
 */
export type ContextAction = z.infer<typeof contextActionSchema>;

/**
 * Action type enum
 */
export type ActionType = 'set' | 'append' | 'remove' | 'increment' | 'decrement' | 'clear';

/**
 * Context rule type
 */
export type ContextRule = z.infer<typeof contextRuleSchema>;

/**
 * Rule scope enum
 */
export type RuleScope = 'global' | 'user' | 'session' | 'conversation';

/**
 * Context match result type
 */
export type ContextMatchResult = z.infer<typeof contextMatchResultSchema>;

/**
 * Applied action result
 */
export interface AppliedActionResult {
    actionId: string;
    field: string;
    success: boolean;
    error?: string;
}

/**
 * Context data type
 */
export type ContextData = z.infer<typeof contextDataSchema>;

/**
 * Rule evaluation input
 */
export interface RuleEvaluationInput {
    message?: string;
    metadata?: Record<string, any>;
    sessionId: string;
    userId?: string;
    conversationId?: string;
    intent?: string;
    entities?: Record<string, any>;
    customData?: Record<string, any>;
}

/**
 * Rule filters
 */
export interface RuleFilters {
    search?: string;
    isActive?: boolean;
    scope?: RuleScope;
    tags?: string[];
    sortBy?: keyof ContextRule;
    sortDirection?: 'asc' | 'desc';
} 