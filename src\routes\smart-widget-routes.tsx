import { lazy } from 'react';
import { Route } from 'react-router-dom';

// Lazy load the Smart Widget Builder page for better performance
const SmartWidgetBuilderPage = lazy(() => import('@/pages/SmartWidgetBuilderPage'));

/**
 * Smart Widget Builder Routes
 *
 * These routes can be added to your existing router configuration
 * to enable the new Smart Widget Builder alongside the current system.
 *
 * Example integration with React Router:
 *
 * ```tsx
 * import { smartWidgetRoutes } from '@/routes/smart-widget-routes';
 *
 * function App() {
 *   return (
 *     <Router>
 *       <Routes>
 *         {/* Existing routes */}
 * <Route path="/widgets" element={<WidgetsPage />} />
  * <Route path="/widgets/:id/edit" element={<WidgetEditPage />} />
  *
 * {/* New Smart Widget Builder routes */ }
  * { smartWidgetRoutes }
  *       </Routes >
 *     </Router >
 *   );
 * }
 * ```
 */
export const smartWidgetRoutes = [
  // Create new widget with Smart Builder
  <Route
    key="smart-widget-new"
    path="/widgets/smart/new"
    element={<SmartWidgetBuilderPage />}
  />,

  // Edit existing widget with Smart Builder
  <Route
    key="smart-widget-edit"
    path="/widgets/smart/:widgetId/edit"
    element={<SmartWidgetBuilderPage />}
  />,
];

/**
 * Alternative: Direct route configuration
 *
 * If you prefer to add routes directly to your router:
 */
export const smartWidgetRouteConfig = {
  // Create new widget
  newWidget: {
    path: '/widgets/smart/new',
    component: SmartWidgetBuilderPage,
  },

  // Edit existing widget
  editWidget: {
    path: '/widgets/smart/:widgetId/edit',
    component: SmartWidgetBuilderPage,
  },
};

/**
 * Navigation helpers
 *
 * Use these functions to navigate to the Smart Widget Builder
 */
export const navigateToSmartBuilder = {
  // Navigate to create new widget
  createNew: () => '/widgets/smart/new',

  // Navigate to edit existing widget
  edit: (widgetId: string | number) => `/ widgets / smart / ${ widgetId }/edit`,

// Navigate back to widgets list
back: () => '/dashboard/widgets',
};

/**
 * Example usage in a component:
 *
 * ```tsx
 * import { useNavigate } from 'react-router-dom';
 * import { navigateToSmartBuilder } from '@/routes/smart-widget-routes';
 *
 * function WidgetsList() {
 *   const navigate = useNavigate();
 *
 *   const handleCreateNew = () => {
 *     navigate(navigateToSmartBuilder.createNew());
 *   };
 *
 *   const handleEdit = (widgetId) => {
 *     navigate(navigateToSmartBuilder.edit(widgetId));
 *   };
 *
 *   return (
 *     <div>
 *       <Button onClick={handleCreateNew}>
 *         Create New Widget (Smart Builder)
 *       </Button>
 *
 *       {widgets.map(widget => (
 *         <div key={widget.id}>
 *           <span>{widget.name}</span>
 *           <Button onClick={() => handleEdit(widget.id)}>
 *             Edit with Smart Builder
 *           </Button>
 *         </div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
