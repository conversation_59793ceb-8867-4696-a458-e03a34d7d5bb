import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ProjectSelector } from '@/components/knowledge-base/project-selector'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import {
    Globe, RefreshCw, Link2, Table, FileJson,
    FileText, CheckCircle2, XCircle, Eye, Trash2,
    Database, BookOpen, ChevronDown, ChevronUp, Download,
    Info
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { AIModelSelector } from '@/components/ai-models/ai-model-selector'

export default function WebScrapingTab({
    projects,
    selectedProjectId,
    setSelectedProjectId,
    refreshTrigger,
    onRefresh
}) {
    // State
    const [url, setUrl] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [scrapedContent, setScrapedContent] = useState(null)
    const [scraperTab, setScraperTab] = useState('text')
    const [savedUrls, setSavedUrls] = useState([])
    const [showSavedUrls, setShowSavedUrls] = useState(false)
    const [selectedModel, setSelectedModel] = useState(null)
    const [tableName, setTableName] = useState('')
    const [title, setTitle] = useState('')
    const [savedUrlsLoading, setSavedUrlsLoading] = useState(false)
    const [advancedOptions, setAdvancedOptions] = useState(false)
    const [renderJs, setRenderJs] = useState(true)
    const [useCache, setUseCache] = useState(true)
    const [scrollPage, setScrollPage] = useState(false)
    const [waitForSelector, setWaitForSelector] = useState('')
    const [scrapedContentTables, setScrapedContentTables] = useState([])
    const [isLoadingTables, setIsLoadingTables] = useState(false)

    // Fetch saved URLs when project changes
    useEffect(() => {
        if (!selectedProjectId || !showSavedUrls) return

        fetchSavedUrls()
    }, [selectedProjectId, showSavedUrls, refreshTrigger])

    // Fetch scraped content tables when component mounts
    useEffect(() => {
        fetchScrapedContentTables()
    }, [refreshTrigger])

    // Fetch scraped content tables
    const fetchScrapedContentTables = async () => {
        setIsLoadingTables(true)

        try {
            const response = await knowledgeBaseService.getScrapedContentTables()

            if (response?.data) {
                setScrapedContentTables(response.data)

                // If we have tables and no table name is set, use the first one
                if (response.data.length > 0 && !tableName) {
                    setTableName(response.data[0].name)
                }
            }
        } catch (error) {
            console.error('Failed to load scraped content tables:', error)
            toast.error('Failed to load scraped content tables')
        } finally {
            setIsLoadingTables(false)
        }
    }

    const fetchSavedUrls = async () => {
        if (!selectedProjectId) return

        setSavedUrlsLoading(true)

        try {
            const response = await knowledgeBaseService.getScrapedUrls({
                project_id: selectedProjectId
            })

            setSavedUrls(Array.isArray(response.data) ? response.data : [])
        } catch (error) {
            console.error('Failed to load saved URLs:', error)
            toast.error('Failed to load saved URLs')
            setSavedUrls([])
        } finally {
            setSavedUrlsLoading(false)
        }
    }

    // Handle URL scraping
    const handleScrapeUrl = async () => {
        if (!url || !selectedProjectId) {
            return toast.error('Please enter a URL and select a project')
        }

        setIsLoading(true)
        setScrapedContent(null)

        try {
            // Prepare options for scraping
            const scrapeOptions = {
                project_id: selectedProjectId,
                model_id: selectedModel,
                table_name: tableName
            }

            // Add advanced options if enabled
            if (advancedOptions) {
                scrapeOptions.render_js = renderJs
                scrapeOptions.use_cache = useCache
                scrapeOptions.scroll_page = scrollPage

                if (waitForSelector) {
                    scrapeOptions.wait_for_selector = waitForSelector
                }
            }

            // Log scraping attempt
            console.log('Scraping URL with options:', scrapeOptions)

            const response = await knowledgeBaseService.scrapeUrl(url, scrapeOptions)

            if (response?.success) {
                const responseData = {
                    title: response.title || 'Scraped Content',
                    text: response.text || '',
                    raw: response.raw || '',
                    table: response.table || [],
                    json: response.json || {},
                    metadata: response.metadata || {},
                    links: response.links || [],
                    images: response.images || [],
                    is_js_heavy: response.is_js_heavy || false,
                    scraped_at: response.scraped_at || new Date().toISOString()
                }

                setScrapedContent(responseData)
                setTitle(responseData.title)

                // Show appropriate success message
                if (responseData.is_js_heavy) {
                    toast.success('JavaScript-heavy page scraped successfully' + (renderJs ? ' with JS rendering' : ''))
                } else {
                    toast.success('URL scraped successfully')
                }
            } else {
                toast.error('Failed to scrape URL: ' + (response?.message || 'Unknown error'))
            }
        } catch (error) {
            console.error('Scrape error:', error)
            toast.error('Error scraping URL: ' + (error.response?.data?.message || error.message))
        } finally {
            setIsLoading(false)
        }
    }

    // Handle saving scraped content
    const handleSaveContent = async () => {
        if (!scrapedContent || !selectedProjectId || !tableName) {
            return toast.error('Please enter a table name')
        }

        setIsLoading(true)

        try {
            // Select content based on active tab
            const content =
                scraperTab === 'text' ? scrapedContent.text :
                    scraperTab === 'raw' ? scrapedContent.raw :
                        scraperTab === 'table' ? JSON.stringify(scrapedContent.table) :
                            scraperTab === 'json' ? JSON.stringify(scrapedContent.json) :
                                scrapedContent.text

            // Prepare metadata
            const metadata = {
                ...scrapedContent.metadata,
                is_js_heavy: scrapedContent.is_js_heavy,
                scraped_at: scrapedContent.scraped_at,
                saved_format: scraperTab,
                links_count: scrapedContent.links?.length || 0,
                images_count: scrapedContent.images?.length || 0,
                content_length: content.length,
                advanced_options: advancedOptions ? {
                    render_js: renderJs,
                    use_cache: useCache,
                    scroll_page: scrollPage,
                    wait_for_selector: waitForSelector || null
                } : null
            }

            const response = await knowledgeBaseService.saveScrape({
                content: content,
                format: scraperTab,
                url: url,
                title: title,
                project_id: selectedProjectId,
                table_name: tableName,
                model_id: selectedModel,
                metadata: metadata
            })

            if (response?.success) {
                toast.success('Content saved successfully')
                setScrapedContent(null)
                setUrl('')
                fetchSavedUrls()
                fetchScrapedContentTables()
                onRefresh()
            } else {
                toast.error('Failed to save content: ' + (response?.message || 'Unknown error'))
            }
        } catch (error) {
            console.error('Save error:', error)
            toast.error('Error saving content: ' + (error.response?.data?.message || error.message))
        } finally {
            setIsLoading(false)
        }
    }

    // Handle deleting saved URL
    const handleDeleteUrl = async (id) => {
        if (!confirm('Are you sure you want to delete this scraped URL?')) return

        try {
            const response = await knowledgeBaseService.deleteScrapedUrl(id)

            if (response.data?.success) {
                toast.success('Scraped URL deleted successfully')
                fetchSavedUrls()
            } else {
                toast.error('Failed to delete scraped URL')
            }
        } catch (error) {
            console.error('Delete error:', error)
            toast.error('Error deleting scraped URL')
        }
    }

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString()
    }

    return (
        <div className="space-y-6">
            {/* Project selector and controls */}
            <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                    <ProjectSelector
                        projects={projects}
                        selectedProjectId={selectedProjectId}
                        setSelectedProjectId={setSelectedProjectId}
                    />

                    <Button
                        variant="outline"
                        size="icon"
                        onClick={onRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                </div>

                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSavedUrls(!showSavedUrls)}
                    className="gap-2"
                >
                    {showSavedUrls ? (
                        <>
                            <ChevronUp className="h-4 w-4" /> Hide Saved URLs
                        </>
                    ) : (
                        <>
                            <ChevronDown className="h-4 w-4" /> Show Saved URLs
                        </>
                    )}
                </Button>
            </div>

            {/* Saved URLs Panel (collapsible) */}
            {showSavedUrls && (
                <Card>
                    <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                            <Globe className="h-5 w-5" /> Saved Scraped URLs
                        </CardTitle>
                        <CardDescription>
                            Previously scraped URLs saved to the knowledge base
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {savedUrlsLoading ? (
                            <div className="text-center py-6">
                                <RefreshCw className="h-8 w-8 mx-auto mb-3 animate-spin text-muted-foreground" />
                                <p className="text-muted-foreground">Loading saved URLs...</p>
                            </div>
                        ) : savedUrls.length === 0 ? (
                            <div className="text-center py-6 text-muted-foreground">
                                <Globe className="h-8 w-8 mx-auto mb-3 opacity-20" />
                                <p>No saved URLs found for this project</p>
                            </div>
                        ) : (
                            <div className="border rounded-md divide-y max-h-[300px] overflow-y-auto">
                                {savedUrls.map(item => (
                                    <div key={item.id} className="p-3 hover:bg-muted/30">
                                        <div className="flex items-center justify-between gap-2">
                                            <div className="flex-1 min-w-0">
                                                <h3 className="font-medium truncate">{item.title || 'Scraped Content'}</h3>
                                                <p className="text-sm flex items-center gap-1 text-muted-foreground truncate">
                                                    <Link2 className="h-3 w-3 flex-shrink-0" />
                                                    <a
                                                        href={item.url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="hover:underline truncate"
                                                    >
                                                        {item.url}
                                                    </a>
                                                </p>
                                            </div>

                                            <div className="flex items-center gap-1 flex-shrink-0">
                                                <Badge variant="outline" className="text-xs">
                                                    {item.format === 'text' && 'Text'}
                                                    {item.format === 'raw' && 'HTML'}
                                                    {item.format === 'table' && 'Table'}
                                                    {item.format === 'json' && 'JSON'}
                                                </Badge>

                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-7 w-7"
                                                    onClick={() => handleDeleteUrl(item.id)}
                                                >
                                                    <Trash2 className="h-4 w-4 text-destructive" />
                                                </Button>
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                                            <span>Table: {item.table_name || 'Not specified'}</span>
                                            <span>{formatDate(item.created_at)}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}

            {/* URL Scraper Panel */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                        <Globe className="h-5 w-5" /> Web URL Scraper
                    </CardTitle>
                    <CardDescription>
                        Scrape content from a web URL to use as an AI knowledge source
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-6">
                        {/* URL Input */}
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div className="md:col-span-3 space-y-2">
                                    <Label htmlFor="url-input">URL to Scrape</Label>
                                    <div className="flex space-x-2">
                                        <Input
                                            id="url-input"
                                            placeholder="https://example.com/page-to-scrape"
                                            value={url}
                                            onChange={e => setUrl(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            onClick={handleScrapeUrl}
                                            disabled={isLoading || !url || !selectedProjectId}
                                        >
                                            {isLoading ? (
                                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                            ) : (
                                                <Globe className="h-4 w-4 mr-2" />
                                            )}
                                            Scrape
                                        </Button>
                                    </div>
                                </div>

                                <div className="md:col-span-2 space-y-2">
                                    <Label htmlFor="ai-model-selector">Processing Model (Optional)</Label>
                                    <AIModelSelector
                                        id="ai-model-selector"
                                        selectedModelId={selectedModel}
                                        onModelChange={(value) => {
                                            console.log("Model changed:", value);
                                            setSelectedModel(value);
                                        }}
                                        placeholder="Select AI model"
                                        isOptional={true}
                                        className="w-full"
                                    />
                                </div>
                            </div>

                            {/* Advanced Options Toggle */}
                            <div className="pt-4">
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="advanced-options"
                                        checked={advancedOptions}
                                        onCheckedChange={setAdvancedOptions}
                                    />
                                    <Label htmlFor="advanced-options" className="cursor-pointer">
                                        Advanced Scraping Options
                                    </Label>
                                </div>
                            </div>

                            {/* Advanced Options Panel */}
                            {advancedOptions && (
                                <div className="border rounded-md p-4 bg-muted/5 space-y-4 mt-4">
                                    <h3 className="font-medium">Advanced Scraping Options</h3>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label htmlFor="render-js" className="cursor-pointer">
                                                    Render JavaScript
                                                </Label>
                                                <Switch
                                                    id="render-js"
                                                    checked={renderJs}
                                                    onCheckedChange={setRenderJs}
                                                />
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Use headless browser to render JavaScript-heavy pages
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label htmlFor="use-cache" className="cursor-pointer">
                                                    Use Cache
                                                </Label>
                                                <Switch
                                                    id="use-cache"
                                                    checked={useCache}
                                                    onCheckedChange={setUseCache}
                                                />
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Use cached content if available (faster)
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label htmlFor="scroll-page" className="cursor-pointer">
                                                    Scroll Page
                                                </Label>
                                                <Switch
                                                    id="scroll-page"
                                                    checked={scrollPage}
                                                    onCheckedChange={setScrollPage}
                                                />
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Scroll to bottom to trigger lazy loading
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="wait-for-selector">Wait for Selector</Label>
                                            <Input
                                                id="wait-for-selector"
                                                placeholder=".content, #main, etc."
                                                value={waitForSelector}
                                                onChange={e => setWaitForSelector(e.target.value)}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                CSS selector to wait for before capturing content
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Scraped Content Preview */}
                        {scrapedContent && (
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium">Scraped Content</h3>

                                    <div className="flex items-center gap-2">
                                        <Badge variant="outline">
                                            {scrapedContent.title ? 'Title detected' : 'No title detected'}
                                        </Badge>

                                        {scrapedContent.is_js_heavy && (
                                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                                                JavaScript-heavy page
                                            </Badge>
                                        )}

                                        {scrapedContent.links && scrapedContent.links.length > 0 && (
                                            <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">
                                                {scrapedContent.links.length} links
                                            </Badge>
                                        )}

                                        {scrapedContent.images && scrapedContent.images.length > 0 && (
                                            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                                                {scrapedContent.images.length} images
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                <Tabs value={scraperTab} onValueChange={setScraperTab}>
                                    <TabsList className="grid w-full grid-cols-5">
                                        <TabsTrigger value="text" className="flex items-center gap-1">
                                            <FileText className="h-4 w-4" /> Text
                                        </TabsTrigger>
                                        <TabsTrigger value="raw" className="flex items-center gap-1">
                                            <BookOpen className="h-4 w-4" /> HTML
                                        </TabsTrigger>
                                        <TabsTrigger value="table" className="flex items-center gap-1">
                                            <Table className="h-4 w-4" /> Table
                                        </TabsTrigger>
                                        <TabsTrigger value="json" className="flex items-center gap-1">
                                            <FileJson className="h-4 w-4" /> JSON
                                        </TabsTrigger>
                                        <TabsTrigger value="metadata" className="flex items-center gap-1">
                                            <Info className="h-4 w-4" /> Metadata
                                        </TabsTrigger>
                                    </TabsList>

                                    <TabsContent value="text" className="mt-4">
                                        <div className="border rounded-md p-3 bg-muted/10 max-h-[400px] overflow-y-auto">
                                            <pre className="text-sm whitespace-pre-wrap font-mono">
                                                {scrapedContent.text || 'No text content extracted'}
                                            </pre>
                                        </div>
                                    </TabsContent>

                                    <TabsContent value="raw" className="mt-4">
                                        <div className="border rounded-md p-3 bg-muted/10 max-h-[400px] overflow-y-auto">
                                            <pre className="text-sm whitespace-pre-wrap font-mono">
                                                {scrapedContent.raw || 'No raw HTML content extracted'}
                                            </pre>
                                        </div>
                                    </TabsContent>

                                    <TabsContent value="table" className="mt-4">
                                        <div className="border rounded-md p-3 bg-muted/10 max-h-[400px] overflow-y-auto">
                                            {scrapedContent.table ? (
                                                <div className="overflow-x-auto">
                                                    <pre className="text-sm whitespace-pre-wrap font-mono">
                                                        {JSON.stringify(scrapedContent.table, null, 2)}
                                                    </pre>
                                                </div>
                                            ) : (
                                                <p className="text-center py-4 text-muted-foreground">
                                                    No table content extracted
                                                </p>
                                            )}
                                        </div>
                                    </TabsContent>

                                    <TabsContent value="json" className="mt-4">
                                        <div className="border rounded-md p-3 bg-muted/10 max-h-[400px] overflow-y-auto">
                                            {scrapedContent.json ? (
                                                <pre className="text-sm whitespace-pre-wrap font-mono">
                                                    {JSON.stringify(scrapedContent.json, null, 2)}
                                                </pre>
                                            ) : (
                                                <p className="text-center py-4 text-muted-foreground">
                                                    No JSON content extracted
                                                </p>
                                            )}
                                        </div>
                                    </TabsContent>

                                    <TabsContent value="metadata" className="mt-4">
                                        <div className="border rounded-md p-3 bg-muted/10 max-h-[400px] overflow-y-auto">
                                            <div className="space-y-6">
                                                {/* Page Metadata */}
                                                <div>
                                                    <h3 className="text-sm font-medium mb-2">Page Metadata</h3>
                                                    <div className="bg-card rounded-md border p-3">
                                                        {scrapedContent.metadata && Object.keys(scrapedContent.metadata).length > 0 ? (
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                                {Object.entries(scrapedContent.metadata).map(([key, value]) => (
                                                                    <div key={key} className="flex flex-col">
                                                                        <span className="text-xs font-medium text-muted-foreground">{key}</span>
                                                                        <span className="text-sm truncate">{String(value)}</span>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            <p className="text-sm text-muted-foreground">No metadata available</p>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Links */}
                                                <div>
                                                    <h3 className="text-sm font-medium mb-2">Links ({scrapedContent.links?.length || 0})</h3>
                                                    <div className="bg-card rounded-md border p-3 max-h-[200px] overflow-y-auto">
                                                        {scrapedContent.links && scrapedContent.links.length > 0 ? (
                                                            <div className="space-y-2">
                                                                {scrapedContent.links.map((link, index) => (
                                                                    <div key={index} className="flex items-start space-x-2 text-sm border-b pb-2 last:border-0 last:pb-0">
                                                                        <Link2 className="h-4 w-4 flex-shrink-0 mt-0.5 text-blue-500" />
                                                                        <div className="flex-1 min-w-0">
                                                                            <div className="font-medium truncate">{link.text || 'No text'}</div>
                                                                            <a
                                                                                href={link.url}
                                                                                target="_blank"
                                                                                rel="noopener noreferrer"
                                                                                className="text-xs text-blue-500 hover:underline truncate block"
                                                                            >
                                                                                {link.url}
                                                                            </a>
                                                                            {link.is_internal && (
                                                                                <Badge variant="outline" className="text-xs mt-1">Internal</Badge>
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            <p className="text-sm text-muted-foreground">No links extracted</p>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Images */}
                                                <div>
                                                    <h3 className="text-sm font-medium mb-2">Images ({scrapedContent.images?.length || 0})</h3>
                                                    <div className="bg-card rounded-md border p-3 max-h-[200px] overflow-y-auto">
                                                        {scrapedContent.images && scrapedContent.images.length > 0 ? (
                                                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                                                {scrapedContent.images.map((image, index) => (
                                                                    <div key={index} className="flex flex-col space-y-1">
                                                                        <div className="relative bg-muted rounded-md h-20 overflow-hidden">
                                                                            <img
                                                                                src={image.src}
                                                                                alt={image.alt || 'Image'}
                                                                                className="object-cover w-full h-full"
                                                                                onError={(e) => {
                                                                                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNlZWVlZWUiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBhbGlnbm1lbnQtYmFzZWxpbmU9Im1pZGRsZSI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pjwvc3ZnPg==';
                                                                                }}
                                                                            />
                                                                        </div>
                                                                        <div className="text-xs truncate" title={image.alt || image.title || ''}>
                                                                            {image.alt || image.title || 'No description'}
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            <p className="text-sm text-muted-foreground">No images extracted</p>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </TabsContent>
                                </Tabs>

                                <div className="border rounded-md p-4 bg-muted/5">
                                    <h3 className="font-medium mb-4">Save to Knowledge Base</h3>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-3">
                                            <div className="space-y-2">
                                                <Label htmlFor="title-input">Title</Label>
                                                <Input
                                                    id="title-input"
                                                    placeholder="Enter a title for this content"
                                                    value={title}
                                                    onChange={e => setTitle(e.target.value)}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="table-name-input">Table Name</Label>
                                                {isLoadingTables ? (
                                                    <div className="flex items-center space-x-2">
                                                        <Input
                                                            id="table-name-input"
                                                            placeholder="Loading tables..."
                                                            disabled
                                                        />
                                                        <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
                                                    </div>
                                                ) : scrapedContentTables.length > 0 ? (
                                                    <Select value={tableName} onValueChange={setTableName}>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select a table" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {scrapedContentTables.map(table => (
                                                                <SelectItem key={table.name} value={table.name}>
                                                                    {table.name} ({table.count} records)
                                                                </SelectItem>
                                                            ))}
                                                            <SelectItem value="new_table">
                                                                + Create new table
                                                            </SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                ) : (
                                                    <Input
                                                        id="table-name-input"
                                                        placeholder="Enter a table name"
                                                        value={tableName}
                                                        onChange={e => setTableName(e.target.value)}
                                                    />
                                                )}
                                                {tableName === 'new_table' && (
                                                    <Input
                                                        className="mt-2"
                                                        placeholder="Enter new table name"
                                                        onChange={e => setTableName(e.target.value)}
                                                    />
                                                )}
                                                <p className="text-xs text-muted-foreground">
                                                    Content will be saved to this table name in the database
                                                </p>
                                            </div>
                                        </div>

                                        <div className="space-y-3">
                                            <div className="space-y-2">
                                                <Label>Selected Format</Label>
                                                <div className="flex items-center gap-2">
                                                    <Badge className="bg-primary/10 text-primary hover:bg-primary/20 border-primary">
                                                        {scraperTab === 'text' && 'Text Content'}
                                                        {scraperTab === 'raw' && 'HTML Content'}
                                                        {scraperTab === 'table' && 'Table Content'}
                                                        {scraperTab === 'json' && 'JSON Content'}
                                                    </Badge>
                                                    <span className="text-xs text-muted-foreground">
                                                        Change format by selecting a different tab above
                                                    </span>
                                                </div>
                                            </div>

                                            <div className="flex flex-col justify-end h-full">
                                                <Button
                                                    onClick={handleSaveContent}
                                                    disabled={isLoading || !tableName}
                                                    className="w-full mt-8"
                                                >
                                                    <Database className="h-4 w-4 mr-2" />
                                                    Save to Knowledge Base
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}