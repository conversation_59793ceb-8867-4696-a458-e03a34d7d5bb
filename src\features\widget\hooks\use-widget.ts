/**
 * useWidget Hook
 * 
 * Custom hook for managing widgets
 */

import { useState, useCallback, useEffect } from 'react';
import {
    Widget,
    WidgetFilters,
    CreateWidgetInput,
    UpdateWidgetInput
} from '../types';
import { widgetService } from '../api/widget-service';
import { toAppError, AppError } from '@/lib/error-handler';
import { widgetSchema } from '../schemas/widget-schema';
import { z } from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';

interface UseWidgetProps {
    initialWidgetId?: string;
}

const WIDGET_QUERY_KEY = 'widgets';

export function useWidget({ initialWidgetId }: UseWidgetProps = {}) {
    const queryClient = useQueryClient();
    const [widgets, setWidgets] = useState<Widget[]>([]);
    const [currentWidget, setCurrentWidget] = useState<Widget | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<AppError | null>(null);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    // Queries
    const { data: widgetsQuery = [], isLoading: widgetsLoading } = useQuery({
        queryKey: [WIDGET_QUERY_KEY],
        queryFn: () => widgetService.getAll()
    });

    // Mutations
    const createMutation = useMutation({
        mutationFn: (widget: WidgetCreate) => widgetService.create(widget),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [WIDGET_QUERY_KEY] });
            toast({ title: 'Widget created successfully' });
        },
        onError: (error) => {
            toast({ title: 'Failed to create widget', variant: 'destructive' });
            console.error('Create widget error:', error);
        }
    });

    const updateMutation = useMutation({
        mutationFn: ({ id, widget }: { id: string; widget: WidgetUpdate }) =>
            widgetService.update(id, widget),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [WIDGET_QUERY_KEY] });
            toast({ title: 'Widget updated successfully' });
        },
        onError: (error) => {
            toast({ title: 'Failed to update widget', variant: 'destructive' });
            console.error('Update widget error:', error);
        }
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => widgetService.delete(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [WIDGET_QUERY_KEY] });
            toast({ title: 'Widget deleted successfully' });
        },
        onError: (error) => {
            toast({ title: 'Failed to delete widget', variant: 'destructive' });
            console.error('Delete widget error:', error);
        }
    });

    const toggleActiveMutation = useMutation({
        mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) =>
            widgetService.toggleActive(id, isActive),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [WIDGET_QUERY_KEY] });
            toast({ title: 'Widget status updated successfully' });
        },
        onError: (error) => {
            toast({ title: 'Failed to update widget status', variant: 'destructive' });
            console.error('Toggle widget error:', error);
        }
    });

    /**
     * Fetch all widgets with optional filters
     */
    const fetchWidgets = useCallback(async (filters?: WidgetFilters) => {
        setIsLoading(true);
        setError(null);

        try {
            const fetchedWidgets = await widgetService.getWidgets(filters);
            setWidgets(fetchedWidgets);
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Fetch a single widget by ID
     */
    const fetchWidget = useCallback(async (widgetId: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const widget = await widgetService.getWidget(widgetId);
            setCurrentWidget(widget);
            return widget;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return null;
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Create a new widget
     */
    const createWidget = useCallback((widget: WidgetCreate) => {
        createMutation.mutate(widget);
    }, [createMutation]);

    /**
     * Update an existing widget
     */
    const updateWidget = useCallback((id: string, widget: WidgetUpdate) => {
        updateMutation.mutate({ id, widget });
    }, [updateMutation]);

    /**
     * Delete a widget
     */
    const deleteWidget = useCallback((id: string) => {
        deleteMutation.mutate(id);
    }, [deleteMutation]);

    /**
     * Toggle widget active status
     */
    const toggleWidgetStatus = useCallback((id: string, isActive: boolean) => {
        toggleActiveMutation.mutate({ id, isActive });
    }, [toggleActiveMutation]);

    /**
     * Duplicate a widget
     */
    const duplicateWidget = useCallback(async (widgetId: string, newName?: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const duplicatedWidget = await widgetService.duplicateWidget(widgetId, newName);

            // Add to widgets list
            setWidgets(prev => [...prev, duplicatedWidget]);

            return duplicatedWidget;
        } catch (err) {
            const appError = toAppError(err);
            setError(appError);
            return null;
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Get embed code for a widget
     */
    const getEmbedCode = useCallback(async (id: string) => {
        try {
            const code = await widgetService.generateEmbedCode(id);
            toast({ title: 'Embed code generated successfully' });
            return code;
        } catch (error) {
            toast({ title: 'Failed to generate embed code', variant: 'destructive' });
            console.error('Generate embed code error:', error);
            return null;
        }
    }, []);

    /**
     * Clear current widget
     */
    const clearCurrentWidget = useCallback(() => {
        setCurrentWidget(null);
        setValidationErrors({});
    }, []);

    /**
     * Set current widget from the widgets list
     */
    const setCurrentWidgetById = useCallback((widgetId: string) => {
        const widget = widgets.find(w => w.id === widgetId);
        if (widget) {
            setCurrentWidget(widget);
        } else {
            fetchWidget(widgetId);
        }
    }, [widgets, fetchWidget]);

    /**
     * Clear all errors
     */
    const clearErrors = useCallback(() => {
        setError(null);
        setValidationErrors({});
    }, []);

    // Fetch initial data if widgetId is provided
    useEffect(() => {
        if (initialWidgetId) {
            fetchWidget(initialWidgetId);
        } else {
            fetchWidgets();
        }
    }, [initialWidgetId, fetchWidget, fetchWidgets]);

    return {
        widgets,
        currentWidget,
        isLoading,
        isSaving,
        error,
        validationErrors,
        fetchWidgets,
        fetchWidget,
        createWidget,
        updateWidget,
        deleteWidget,
        toggleWidgetStatus,
        duplicateWidget,
        getEmbedCode,
        clearCurrentWidget,
        setCurrentWidgetById,
        clearErrors,
    };
} 