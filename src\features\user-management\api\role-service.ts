/**
 * Role API Service
 * 
 * This file contains all API calls related to role management
 */

import axios from 'axios'
import { API_BASE_URL } from '@/lib/constants'
import {
    Role,
    RoleCreateData,
    RoleUpdateData,
    RoleWithUserCount
} from '../types'
import { tryCatch } from '@/lib/error-handler'

/**
 * Base API endpoint for roles
 */
const ROLES_API = `${API_BASE_URL}/roles`

/**
 * Role API Service
 */
export const roleService = {
    /**
     * Get all roles
     */
    getAllRoles: async (): Promise<{ data: RoleWithUserCount[] }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(ROLES_API)
        }, 'getAllRoles')

        if (error || !response) {
            throw error || new Error('Failed to fetch roles')
        }

        return response.data
    },

    /**
     * Get a specific role by ID
     */
    getRole: async (id: number): Promise<{ data: Role }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${ROLES_API}/${id}`)
        }, 'getRole')

        if (error || !response) {
            throw error || new Error(`Failed to fetch role with ID ${id}`)
        }

        return response.data
    },

    /**
     * Create a new role
     */
    createRole: async (roleData: RoleCreateData): Promise<{ data: Role }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(ROLES_API, roleData)
        }, 'createRole')

        if (error || !response) {
            throw error || new Error('Failed to create role')
        }

        return response.data
    },

    /**
     * Update an existing role
     */
    updateRole: async (id: number, roleData: RoleUpdateData): Promise<{ data: Role }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.put(`${ROLES_API}/${id}`, roleData)
        }, 'updateRole')

        if (error || !response) {
            throw error || new Error(`Failed to update role with ID ${id}`)
        }

        return response.data
    },

    /**
     * Delete a role
     */
    deleteRole: async (id: number): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${ROLES_API}/${id}`)
        }, 'deleteRole')

        if (error || !response) {
            throw error || new Error(`Failed to delete role with ID ${id}`)
        }

        return { success: true }
    },

    /**
     * Assign permissions to a role
     */
    assignPermissions: async (roleId: number, permissions: number[]): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${ROLES_API}/${roleId}/permissions`, { permissions })
        }, 'assignPermissions')

        if (error || !response) {
            throw error || new Error(`Failed to assign permissions to role with ID ${roleId}`)
        }

        return { success: true }
    },

    /**
     * Get roles with specific permission
     */
    getRolesByPermission: async (permissionId: number): Promise<{ data: Role[] }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${ROLES_API}/permission/${permissionId}`)
        }, 'getRolesByPermission')

        if (error || !response) {
            throw error || new Error(`Failed to fetch roles with permission ID ${permissionId}`)
        }

        return response.data
    }
} 