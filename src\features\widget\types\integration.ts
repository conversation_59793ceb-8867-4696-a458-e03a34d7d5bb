/**
 * Integration Types
 * 
 * This file contains type definitions for widget integrations
 */

/**
 * Integration data structure
 */
export interface Integration {
    id: string;
    type: 'slack' | 'discord' | 'ms-teams' | 'zapier' | 'generic';
    name: string;
    description?: string;
    isActive: boolean;
    webhook?: string;
    apiKey?: string;
    secret?: string;
    events: IntegrationEvent[];
    metadata?: Record<string, any>;
    createdAt: string;
    updatedAt: string;
    widgetId: string;
}

/**
 * Integration event types
 */
export type IntegrationEvent =
    | 'message_sent'
    | 'conversation_started'
    | 'conversation_ended'
    | 'feedback_received'
    | 'error_occurred';

/**
 * Integration with test result
 */
export interface IntegrationWithTestResult extends Integration {
    testResult?: {
        success: boolean;
        message: string;
        timestamp: string;
    };
}

/**
 * Integration creation input
 */
export type CreateIntegrationInput = Omit<Integration, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Integration update input
 */
export type UpdateIntegrationInput = Partial<Omit<Integration, 'id' | 'createdAt' | 'updatedAt'>>;

/**
 * Integration test result
 */
export interface IntegrationTestResult {
    success: boolean;
    message: string;
    data?: any;
    statusCode?: number;
}

/**
 * Integration type details
 */
export interface IntegrationTypeDetails {
    type: Integration['type'];
    name: string;
    description: string;
    icon: string;
    color: string;
    webhookPlaceholder: string;
    webhookDocsUrl: string;
    supportedEvents: IntegrationEvent[];
    fields: {
        webhook: boolean;
        apiKey: boolean;
        secret: boolean;
    };
    setupInstructions: string[];
} 