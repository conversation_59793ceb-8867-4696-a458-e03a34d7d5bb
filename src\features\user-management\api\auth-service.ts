/**
 * Authentication API Service
 * 
 * This file contains all API calls related to authentication
 */

import axios from 'axios';
import { API_BASE_URL, STORAGE_KEYS } from '@/lib/constants';
import {
    LoginCredentials,
    RegistrationData,
    AuthResult,
    PasswordResetRequest,
    PasswordResetData,
    PasswordChangeData,
    User
} from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for authentication
 */
const AUTH_API = `${API_BASE_URL}/auth`;

/**
 * Authentication API Service
 */
export const authService = {
    /**
     * Login user
     */
    login: async (credentials: LoginCredentials): Promise<AuthResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/login`, credentials);
        }, 'login');

        if (error || !response) {
            throw error || new Error('Failed to login');
        }

        // Save token to local storage
        localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));

        return response.data;
    },

    /**
     * Register new user
     */
    register: async (data: RegistrationData): Promise<AuthResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/register`, data);
        }, 'register');

        if (error || !response) {
            throw error || new Error('Failed to register');
        }

        // Save token to local storage if auto-login
        localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));

        return response.data;
    },

    /**
     * Logout user
     */
    logout: async (): Promise<void> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/logout`);
        }, 'logout');

        // Clear local storage regardless of API response
        localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER);

        if (error) {
            console.warn('Logout API call failed, but local storage was cleared', error);
        }
    },

    /**
     * Request password reset
     */
    requestPasswordReset: async (data: PasswordResetRequest): Promise<{ success: boolean; message: string }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/password/reset-request`, data);
        }, 'requestPasswordReset');

        if (error || !response) {
            throw error || new Error('Failed to request password reset');
        }

        return response.data;
    },

    /**
     * Reset password
     */
    resetPassword: async (data: PasswordResetData): Promise<{ success: boolean; message: string }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/password/reset`, data);
        }, 'resetPassword');

        if (error || !response) {
            throw error || new Error('Failed to reset password');
        }

        return response.data;
    },

    /**
     * Change password
     */
    changePassword: async (data: PasswordChangeData): Promise<{ success: boolean; message: string }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/password/change`, data);
        }, 'changePassword');

        if (error || !response) {
            throw error || new Error('Failed to change password');
        }

        return response.data;
    },

    /**
     * Get current user
     */
    getCurrentUser: async (): Promise<User> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${AUTH_API}/me`);
        }, 'getCurrentUser');

        if (error || !response) {
            throw error || new Error('Failed to get current user');
        }

        // Update user in local storage
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data));

        return response.data;
    },

    /**
     * Verify email
     */
    verifyEmail: async (token: string): Promise<{ success: boolean; message: string }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/verify-email`, { token });
        }, 'verifyEmail');

        if (error || !response) {
            throw error || new Error('Failed to verify email');
        }

        return response.data;
    },

    /**
     * Refresh token
     */
    refreshToken: async (): Promise<AuthResult> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.post(`${AUTH_API}/refresh-token`);
        }, 'refreshToken');

        if (error || !response) {
            throw error || new Error('Failed to refresh token');
        }

        // Update token in local storage
        localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);

        return response.data;
    },

    /**
     * Check if user is authenticated
     */
    isAuthenticated: (): boolean => {
        return !!localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    },

    /**
     * Get token
     */
    getToken: (): string | null => {
        return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    },

    /**
     * Get user from local storage
     */
    getStoredUser: (): User | null => {
        const userJson = localStorage.getItem(STORAGE_KEYS.USER);
        if (!userJson) return null;

        try {
            return JSON.parse(userJson);
        } catch (error) {
            console.error('Failed to parse user data from local storage', error);
            return null;
        }
    }
};

export default authService; 