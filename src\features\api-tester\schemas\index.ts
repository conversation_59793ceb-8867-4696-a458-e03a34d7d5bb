/**
 * API Tester Schemas
 */

import { z } from 'zod'

export const apiTestRequestSchema = z.object({
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
  url: z.string().url(),
  headers: z.record(z.string()).optional(),
  body: z.any().optional(),
  parameters: z.record(z.any()).optional()
})

export const testConfigurationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  requests: z.array(apiTestRequestSchema).min(1, 'At least one request is required')
})

export type ApiTestRequestInput = z.infer<typeof apiTestRequestSchema>
export type TestConfigurationInput = z.infer<typeof testConfigurationSchema>
