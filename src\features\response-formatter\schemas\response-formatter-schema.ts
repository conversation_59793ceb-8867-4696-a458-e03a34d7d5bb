/**
 * Response Formatter Schemas
 * 
 * This file contains Zod validation schemas for response formatter functionality
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Format option schema
 */
export const formatOptionSchema = z.object({
    name: z.string().min(1, 'Option name is required'),
    value: z.string().min(1, 'Option value is required'),
    description: z.string().optional(),
    isDefault: z.boolean().default(false),
});

/**
 * Format section schema
 */
export const formatSectionSchema = z.object({
    id: z.string().optional(),
    title: z.string().min(1, 'Section title is required'),
    content: z.string().min(1, 'Section content is required'),
    order: z.number().int().nonnegative().default(0),
    isCollapsible: z.boolean().default(false),
    isCollapsed: z.boolean().default(false),
});

/**
 * Style settings schema
 */
export const styleSettingsSchema = z.object({
    headingStyle: z.enum(['normal', 'uppercase', 'lowercase', 'capitalize']).default('normal'),
    headingSize: z.enum(['small', 'medium', 'large']).default('medium'),
    fontStyle: z.enum(['normal', 'italic', 'bold', 'bold-italic']).default('normal'),
    listStyle: z.enum(['bullet', 'numbered', 'checkmark', 'none']).default('bullet'),
    includeEmoji: z.boolean().default(false),
    colorScheme: z.enum(['default', 'minimal', 'branded', 'custom']).default('default'),
    customCSS: z.string().optional(),
});

/**
 * Response formatter schema
 */
export const responseFormatterSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    type: z.enum(['standard', 'structured', 'bullet-list', 'numbered-list', 'table', 'card', 'custom']).default('standard'),
    template: z.string().optional(),
    formatOptions: z.array(formatOptionSchema).optional(),
    sections: z.array(formatSectionSchema).optional(),
    styleSettings: styleSettingsSchema.optional(),
    allowUserCustomization: z.boolean().default(false),
    isActive: z.boolean().default(true),
    isDefault: z.boolean().default(false),
    tags: z.array(z.string()).optional(),
    brandingId: z.string().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Format response input schema
 */
export const formatResponseInputSchema = z.object({
    formatterId: z.string(),
    content: z.string().min(1, 'Content is required'),
    options: z.record(z.string(), z.string()).optional(),
    customStyle: z.object({
        headingStyle: z.enum(['normal', 'uppercase', 'lowercase', 'capitalize']).optional(),
        listStyle: z.enum(['bullet', 'numbered', 'checkmark', 'none']).optional(),
        includeEmoji: z.boolean().optional(),
    }).optional(),
});

/**
 * Format response result schema
 */
export const formatResponseResultSchema = z.object({
    originalContent: z.string(),
    formattedContent: z.string(),
    formatterId: z.string(),
    html: z.string().optional(),
    plainText: z.string().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

export default {
    formatOptionSchema,
    formatSectionSchema,
    styleSettingsSchema,
    responseFormatterSchema,
    formatResponseInputSchema,
    formatResponseResultSchema,
};