/**
 * Guest User API Service
 * 
 * This file contains all API calls related to guest user functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import { GuestUser, ChatMessage } from '../types';
import { tryCatch } from '@/lib/error-handler';

/**
 * Base API endpoint for guest users
 */
const GUEST_USER_API = `${API_BASE_URL}/guest-users`;
const CHAT_API = `${API_BASE_URL}/chat`;

/**
 * Guest User API Service
 */
export const guestUserService = {
    /**
     * Get all guest users
     */
    getAllGuestUsers: async (): Promise<{ data: GuestUser[] }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(GUEST_USER_API);
        }, 'getAllGuestUsers');

        if (error || !response) {
            throw error || new Error('Failed to fetch guest users');
        }

        return response.data;
    },

    /**
     * Get a guest user by ID
     */
    getGuestUserDetails: async (id: number): Promise<{ data: GuestUser }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${GUEST_USER_API}/${id}`);
        }, 'getGuestUserDetails');

        if (error || !response) {
            throw error || new Error(`Failed to fetch guest user with ID ${id}`);
        }

        return response.data;
    },

    /**
     * Delete a guest user
     */
    deleteGuestUser: async (id: number): Promise<{ success: boolean }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.delete(`${GUEST_USER_API}/${id}`);
        }, 'deleteGuestUser');

        if (error || !response) {
            throw error || new Error(`Failed to delete guest user with ID ${id}`);
        }

        return { success: true };
    },

    /**
     * Get chat history for a guest user by session ID
     */
    getGuestUserChatHistory: async (sessionId: string): Promise<{ data: ChatMessage[] }> => {
        const [response, error] = await tryCatch(async () => {
            return await axios.get(`${CHAT_API}/history`, { params: { session_id: sessionId } });
        }, 'getGuestUserChatHistory');

        if (error || !response) {
            throw error || new Error(`Failed to fetch chat history for session ${sessionId}`);
        }

        return response.data;
    }
}; 