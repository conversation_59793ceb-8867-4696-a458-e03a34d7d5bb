/**
 * Smart Widget Builder Hook
 *
 * Custom hook for managing smart widget builder state and operations.
 * Provides centralized state management and business logic for the widget builder.
 */

import { useState, useCallback, useEffect } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import {
  widgetBuilderService,
  WebhookConfig,
  IntegrationConfig,
  AIModelConfig,
  PreChatFormConfig,
  PostChatSurveyConfig,
  DomainRestrictionConfig,
  MobileOptimizationConfig,
  ConversationPersistenceConfig,
  CustomCSSConfig,
  LogoUploadConfig
} from '@/features/widget-builder';
// widgetService is now part of widgetBuilderService

// Widget form data structure
export interface SmartWidgetFormData {
  // Basic widget info
  name: string;
  description?: string;

  // Feature toggles
  features: {
    preChat: boolean;
    postChat: boolean;
    webhooks: boolean;
    domainRestriction: boolean;
    conversationPersistence: boolean;
    mobileOptimization: boolean;
    customCSS: boolean;
    aiModelSelection: boolean;
    logoUpload: boolean;
    userRatings: boolean;
    analytics: boolean;
    autoOpen: boolean;
  };

  // Quick settings
  quickSettings: {
    primaryColor: string;
    theme: 'light' | 'dark' | 'auto';
    position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    size: 'small' | 'medium' | 'large';
    borderRadius: number;
    welcomeMessage: string;
  };

  // Advanced configurations
  advanced: {
    integrations: IntegrationConfig[];
    webhooks: WebhookConfig[];
    aiModel?: AIModelConfig;
    preChatForm?: PreChatFormConfig;
    postChatSurvey?: PostChatSurveyConfig;
    domainRestrictions?: DomainRestrictionConfig;
    mobileOptimization?: MobileOptimizationConfig;
    conversationPersistence?: ConversationPersistenceConfig;
    customCSS?: CustomCSSConfig;
    logoUpload?: LogoUploadConfig;
  };
}

// Default form values
const getDefaultFormData = (): SmartWidgetFormData => ({
  name: '',
  description: '',
  features: {
    preChat: false,
    postChat: false,
    webhooks: false,
    domainRestriction: false,
    conversationPersistence: false,
    mobileOptimization: false,
    customCSS: false,
    aiModelSelection: false,
    logoUpload: false,
    userRatings: false,
    analytics: false,
    autoOpen: false,
  },
  quickSettings: {
    primaryColor: '#4f46e5',
    theme: 'light',
    position: 'bottom-right',
    size: 'medium',
    borderRadius: 8,
    welcomeMessage: 'Hello! How can I help you today?',
  },
  advanced: {
    integrations: [],
    webhooks: [],
  },
});

export interface UseSmartWidgetBuilderOptions {
  widgetId?: string;
  onSaveSuccess?: (widgetId: string) => void;
  onSaveError?: (error: any) => void;
}

export function useSmartWidgetBuilder(options: UseSmartWidgetBuilderOptions = {}) {
  const { widgetId, onSaveSuccess, onSaveError } = options;
  const { toast } = useToast();
  const navigate = useNavigate();

  // Form management
  const form = useForm<SmartWidgetFormData>({
    defaultValues: getDefaultFormData(),
  });

  // State management
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [availableAIModels, setAvailableAIModels] = useState<AIModelConfig[]>([]);
  const [widgetTemplates, setWidgetTemplates] = useState<any[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Load widget data if editing existing widget
  useEffect(() => {
    if (widgetId) {
      loadWidgetData(widgetId);
    }
    loadAvailableAIModels();
    loadWidgetTemplates();
  }, [widgetId]);

  // Load existing widget data
  const loadWidgetData = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await widgetBuilderService.getWidgetByPublicId(id);
      const widget = response.data;

      // Transform widget data to form format
      const formData = transformWidgetToFormData(widget);
      form.reset(formData);
    } catch (error) {
      console.error('Failed to load widget data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load widget data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load available AI models
  const loadAvailableAIModels = async () => {
    try {
      const models = await widgetBuilderService.getAvailableAIModels();
      setAvailableAIModels(models);
    } catch (error) {
      console.error('Failed to load AI models:', error);
    }
  };

  // Load widget templates
  const loadWidgetTemplates = async () => {
    try {
      const templates = await widgetBuilderService.getWidgetTemplates();
      setWidgetTemplates(templates);
    } catch (error) {
      console.error('Failed to load widget templates:', error);
    }
  };

  // Transform widget data to form format
  const transformWidgetToFormData = (widget: any): SmartWidgetFormData => {
    // This would transform the API response to match our form structure
    // Implementation depends on the actual API response format
    return {
      ...getDefaultFormData(),
      name: widget.name || '',
      description: widget.description || '',
      // Map other fields based on API response structure
    };
  };

  // Validate form data
  const validateFormData = (data: SmartWidgetFormData): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!data.name.trim()) {
      errors.name = 'Widget name is required';
    }

    if (data.features.webhooks && data.advanced.webhooks?.length === 0) {
      errors.webhooks = 'At least one webhook must be configured when webhooks are enabled';
    }

    if (data.features.domainRestriction && (!data.advanced.domainRestrictions?.allowedDomains?.length)) {
      errors.domainRestriction = 'At least one domain must be specified when domain restrictions are enabled';
    }

    return errors;
  };

  // Save widget
  const saveWidget = async (data: SmartWidgetFormData) => {
    setIsSaving(true);
    setValidationErrors({});

    try {
      // Validate form data
      const errors = validateFormData(data);
      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        toast({
          title: 'Validation Error',
          description: 'Please fix the validation errors before saving',
          variant: 'destructive',
        });
        return;
      }

      // Transform form data to API format
      const widgetData = transformFormDataToWidget(data);

      let response;
      if (widgetId) {
        // Update existing widget
        const numericId = await getNumericWidgetId(widgetId);
        response = await widgetBuilderService.updateWidget(numericId, widgetData);
      } else {
        // Create new widget
        response = await widgetBuilderService.createWidget(widgetData);
      }

      const savedWidget = response.data;

      toast({
        title: 'Success',
        description: `Widget ${widgetId ? 'updated' : 'created'} successfully`,
        variant: 'default',
      });

      // Call success callback
      if (onSaveSuccess) {
        onSaveSuccess(savedWidget.widget_id || savedWidget.id);
      } else {
        // Default navigation to widget list
        navigate('/widgets');
      }

    } catch (error) {
      console.error('Failed to save widget:', error);
      toast({
        title: 'Error',
        description: `Failed to ${widgetId ? 'update' : 'create'} widget`,
        variant: 'destructive',
      });

      if (onSaveError) {
        onSaveError(error);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Transform form data to API format
  const transformFormDataToWidget = (data: SmartWidgetFormData) => {
    // Transform form data to match API expectations
    return {
      name: data.name,
      description: data.description,
      settings: {
        primaryColor: data.quickSettings.primaryColor,
        theme: data.quickSettings.theme,
        position: data.quickSettings.position,
        size: data.quickSettings.size,
        borderRadius: data.quickSettings.borderRadius,
        welcomeMessage: data.quickSettings.welcomeMessage,
        // Map other settings
      },
      features: data.features,
      advanced: data.advanced,
    };
  };

  // Get numeric widget ID from public ID
  const getNumericWidgetId = async (publicId: string): Promise<number> => {
    const response = await widgetBuilderService.getWidgetByPublicId(publicId);
    return response.data.id;
  };

  // Modal management
  const openModal = useCallback((modalName: string) => {
    setActiveModal(modalName);
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  // Feature toggle handlers
  const toggleFeature = useCallback((featureName: keyof SmartWidgetFormData['features']) => {
    const currentValue = form.getValues(`features.${featureName}`);
    form.setValue(`features.${featureName}`, !currentValue);
  }, [form]);

  // Apply template
  const applyTemplate = async (templateId: string) => {
    if (!widgetId) {
      toast({
        title: 'Error',
        description: 'Cannot apply template to unsaved widget',
        variant: 'destructive',
      });
      return;
    }

    try {
      await widgetBuilderService.applyTemplate(Number(widgetId), templateId);
      // Reload widget data after applying template
      await loadWidgetData(widgetId);
    } catch (error) {
      console.error('Failed to apply template:', error);
    }
  };

  return {
    // Form management
    form,

    // State
    isLoading,
    isSaving,
    activeModal,
    availableAIModels,
    widgetTemplates,
    validationErrors,

    // Actions
    saveWidget: (data: SmartWidgetFormData) => saveWidget(data),
    openModal,
    closeModal,
    toggleFeature,
    applyTemplate,

    // Data
    formData: form.watch(),

    // Utilities
    isEditing: !!widgetId,
    widgetId,
  };
}
