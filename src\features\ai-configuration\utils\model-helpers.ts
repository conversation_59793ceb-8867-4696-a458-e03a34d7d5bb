/**
 * AI Model Helper Utilities
 */

import { ModelProvider, ModelSettings } from '../types'

/**
 * Get default settings for a model provider
 */
export function getDefaultModelSettings(provider: string): Partial<ModelSettings> {
  const defaults: Record<string, Partial<ModelSettings>> = {
    openai: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true
    },
    anthropic: {
      temperature: 0.7,
      maxTokens: 4096,
      topP: 1,
      streaming: true
    },
    google: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.95,
      streaming: true
    },
    cohere: {
      temperature: 0.7,
      maxTokens: 2048,
      streaming: false
    }
  }

  return defaults[provider] || {}
}

/**
 * Validate model settings
 */
export function validateModelSettings(settings: ModelSettings): string[] {
  const errors: string[] = []

  if (settings.temperature !== undefined) {
    if (settings.temperature < 0 || settings.temperature > 2) {
      errors.push('Temperature must be between 0 and 2')
    }
  }

  if (settings.maxTokens !== undefined) {
    if (settings.maxTokens < 1 || settings.maxTokens > 100000) {
      errors.push('Max tokens must be between 1 and 100,000')
    }
  }

  if (settings.topP !== undefined) {
    if (settings.topP < 0 || settings.topP > 1) {
      errors.push('Top P must be between 0 and 1')
    }
  }

  return errors
}

/**
 * Format model name for display
 */
export function formatModelName(model: string): string {
  return model
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

/**
 * Get provider display name
 */
export function getProviderDisplayName(provider: string): string {
  const names: Record<string, string> = {
    openai: 'OpenAI',
    anthropic: 'Anthropic',
    google: 'Google AI',
    cohere: 'Cohere',
    huggingface: 'Hugging Face',
    azure: 'Azure OpenAI'
  }

  return names[provider] || provider
}
