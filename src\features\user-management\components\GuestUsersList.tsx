/**
 * Guest Users List Component
 * 
 * This component displays a list of guest users with functionality to view details and delete
 */

import { useState, useEffect } from "react"
import { useGuestUsers } from "../hooks/use-guest-users"
import { GuestUser } from "../types"
import { useToast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { Dialog } from "@/components/ui/dialog"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"

import {
    GuestUsersTable,
    GuestUserDetailsDialog,
    GuestUserDeleteDialog
} from "./guest-users"

export interface GuestUsersListProps {
    widgetId?: number
}

export function GuestUsersList({ widgetId }: GuestUsersListProps) {
    const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const { toast } = useToast()

    const {
        guestUsers,
        currentGuest,
        chatHistory,
        isLoading,
        isDeleting,
        isLoadingChat,
        error,
        fetchGuestUsers,
        fetchChatHistory,
        selectGuestUser,
        deleteGuestUser,
        clearCurrentGuest
    } = useGuestUsers()

    // Fetch guest users on mount
    useEffect(() => {
        fetchGuestUsers()
    }, [fetchGuestUsers])

    // Handle view details action
    const handleViewDetails = async (guest: GuestUser) => {
        selectGuestUser(guest)
        setDetailsDialogOpen(true)

        try {
            await fetchChatHistory(guest.session_id)
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to load chat history",
                variant: "destructive",
            })
        }
    }

    // Handle delete click action
    const handleDeleteClick = (guest: GuestUser) => {
        selectGuestUser(guest)
        setDeleteDialogOpen(true)
    }

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        if (!currentGuest) return

        try {
            const success = await deleteGuestUser(currentGuest.id)
            if (success) {
                toast({
                    title: "Success",
                    description: "Guest user deleted successfully",
                })
                setDeleteDialogOpen(false)
            }
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to delete guest user",
                variant: "destructive",
            })
        }
    }

    // Handle dialog close
    const handleDialogClose = () => {
        clearCurrentGuest()
    }

    // Show loading state
    if (isLoading && guestUsers.length === 0) {
        return (
            <div className="flex justify-center items-center h-64">
                <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
        )
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle>Guest Users</CardTitle>
                <Button onClick={fetchGuestUsers} variant="outline" size="sm">
                    Refresh
                </Button>
            </CardHeader>
            <CardContent>
                {guestUsers.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                        No guest users found
                    </div>
                ) : (
                    <GuestUsersTable
                        guestUsers={guestUsers}
                        onViewDetails={handleViewDetails}
                        onDeleteClick={handleDeleteClick}
                    />
                )}

                {/* Guest User Details Dialog */}
                <Dialog
                    open={detailsDialogOpen}
                    onOpenChange={(open) => {
                        setDetailsDialogOpen(open)
                        if (!open) handleDialogClose()
                    }}
                >
                    {currentGuest && (
                        <GuestUserDetailsDialog
                            guestUser={currentGuest}
                            chatHistory={chatHistory}
                            isLoadingChat={isLoadingChat}
                        />
                    )}
                </Dialog>

                {/* Delete Confirmation Dialog */}
                <Dialog
                    open={deleteDialogOpen}
                    onOpenChange={(open) => {
                        setDeleteDialogOpen(open)
                        if (!open) handleDialogClose()
                    }}
                >
                    {currentGuest && (
                        <GuestUserDeleteDialog
                            guestUser={currentGuest}
                            onDelete={handleDeleteConfirm}
                            onCancel={() => setDeleteDialogOpen(false)}
                            isDeleting={isDeleting}
                        />
                    )}
                </Dialog>
            </CardContent>
        </Card>
    )
} 