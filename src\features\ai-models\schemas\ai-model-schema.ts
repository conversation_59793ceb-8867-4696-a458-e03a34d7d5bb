/**
 * AI Model Schemas
 * 
 * This file contains Zod validation schemas for AI models
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Model parameters schema
 */
export const modelParametersSchema = z.object({
    temperature: z.number().min(0).max(2).default(0.7),
    maxTokens: z.number().int().min(1).max(8192).default(2048),
    topP: z.number().min(0).max(1).default(1),
    frequencyPenalty: z.number().min(0).max(2).default(0),
    presencePenalty: z.number().min(0).max(2).default(0),
});

/**
 * AI model schema
 */
export const aiModelSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    provider: z.enum(['openai', 'anthropic', 'gemini', 'mistral', 'custom']),
    apiKey: commonSchemas.nonEmptyString,
    apiEndpoint: z.string().url().optional(),
    modelId: commonSchemas.nonEmptyString,
    parameters: modelParametersSchema,
    isActive: z.boolean().default(true),
    isDefault: z.boolean().default(false),
    promptTemplateId: z.string().optional(),
    description: z.string().optional(),
    version: z.string().optional(),
    capabilities: z.array(z.enum([
        'chat',
        'completion',
        'embeddings',
        'images',
        'function-calling'
    ])).default(['chat']),
    maxConcurrentRequests: z.number().int().positive().default(10),
    systemPrompt: z.string().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * AI model test request schema
 */
export const testModelRequestSchema = z.object({
    modelId: z.string(),
    prompt: z.string().min(1, errorMessages.required),
    parameters: modelParametersSchema.partial().optional(),
});

/**
 * AI model test response schema
 */
export const testModelResponseSchema = z.object({
    success: z.boolean(),
    message: z.string().optional(),
    response: z.string().optional(),
    error: z.string().optional(),
    latency: z.number().optional(),
    usage: z.object({
        promptTokens: z.number().optional(),
        completionTokens: z.number().optional(),
        totalTokens: z.number().optional(),
    }).optional(),
});

export default aiModelSchema; 