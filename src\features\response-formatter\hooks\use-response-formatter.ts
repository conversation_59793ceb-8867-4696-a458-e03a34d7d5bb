/**
 * useResponseFormatter Hook
 * 
 * Custom hook for response formatter management
 */

import { useState, useCallback } from 'react'
import { responseFormatterService } from '../api/response-formatter-service'
import {
    ResponseFormatter,
    FormatterFilters,
    FormatResponseInput,
    FormatResponseResult
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useResponseFormatter() {
    const [formatters, setFormatters] = useState<ResponseFormatter[]>([])
    const [currentFormatter, setCurrentFormatter] = useState<ResponseFormatter | null>(null)
    const [formattedResponse, setFormattedResponse] = useState<FormatResponseResult | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isSaving, setIsSaving] = useState<boolean>(false)
    const [isFormatting, setIsFormatting] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch all formatters
     */
    const fetchFormatters = useCallback(async (filters?: FormatterFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedFormatters = await responseFormatterService.getFormatters(filters)
            setFormatters(fetchedFormatters)
            return fetchedFormatters
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single formatter
     */
    const fetchFormatter = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const formatter = await responseFormatterService.getFormatter(id)
            setCurrentFormatter(formatter)
            return formatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch default formatter
     */
    const fetchDefaultFormatter = useCallback(async (type?: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const formatter = await responseFormatterService.getDefaultFormatter(type)
            return formatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new formatter
     */
    const createFormatter = useCallback(async (formatter: Omit<ResponseFormatter, 'id'>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newFormatter = await responseFormatterService.createFormatter(formatter)
            setFormatters(prev => [...prev, newFormatter])
            setCurrentFormatter(newFormatter)
            return newFormatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a formatter
     */
    const updateFormatter = useCallback(async (id: string, formatter: Partial<ResponseFormatter>) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedFormatter = await responseFormatterService.updateFormatter(id, formatter)
            setFormatters(prev => prev.map(f => f.id === id ? updatedFormatter : f))

            if (currentFormatter?.id === id) {
                setCurrentFormatter(updatedFormatter)
            }

            return updatedFormatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentFormatter])

    /**
     * Delete a formatter
     */
    const deleteFormatter = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await responseFormatterService.deleteFormatter(id)
            setFormatters(prev => prev.filter(f => f.id !== id))

            if (currentFormatter?.id === id) {
                setCurrentFormatter(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentFormatter])

    /**
     * Set a formatter as default
     */
    const setAsDefaultFormatter = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedFormatter = await responseFormatterService.setDefaultFormatter(id)

            // Update formatters in list
            setFormatters(prev => prev.map(f => {
                if (f.id === id) {
                    return updatedFormatter
                }
                // Set all other formatters of the same type to non-default
                if (f.type === updatedFormatter.type && f.isDefault) {
                    return { ...f, isDefault: false }
                }
                return f
            }))

            if (currentFormatter?.id === id) {
                setCurrentFormatter(updatedFormatter)
            }

            return updatedFormatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentFormatter])

    /**
     * Toggle formatter status
     */
    const toggleFormatterStatus = useCallback(async (id: string, isActive: boolean) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedFormatter = await responseFormatterService.toggleFormatterStatus(id, isActive)
            setFormatters(prev => prev.map(f => f.id === id ? updatedFormatter : f))

            if (currentFormatter?.id === id) {
                setCurrentFormatter(updatedFormatter)
            }

            return updatedFormatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentFormatter])

    /**
     * Format a response
     */
    const formatResponse = useCallback(async (input: FormatResponseInput) => {
        setIsFormatting(true)
        setError(null)

        try {
            const result = await responseFormatterService.formatResponse(input)
            setFormattedResponse(result)
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsFormatting(false)
        }
    }, [])

    /**
     * Preview a formatter
     */
    const previewFormatter = useCallback(async (id: string, content: string) => {
        setIsFormatting(true)
        setError(null)

        try {
            const result = await responseFormatterService.previewFormatter(id, content)
            setFormattedResponse(result)
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsFormatting(false)
        }
    }, [])

    /**
     * Clone a formatter
     */
    const cloneFormatter = useCallback(async (id: string, updates?: Partial<ResponseFormatter>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newFormatter = await responseFormatterService.cloneFormatter(id, updates)
            setFormatters(prev => [...prev, newFormatter])
            setCurrentFormatter(newFormatter)
            return newFormatter
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Clear current formatter
     */
    const clearCurrentFormatter = useCallback(() => {
        setCurrentFormatter(null)
    }, [])

    /**
     * Clear formatted response
     */
    const clearFormattedResponse = useCallback(() => {
        setFormattedResponse(null)
    }, [])

    /**
     * Clear error
     */
    const clearError = useCallback(() => {
        setError(null)
    }, [])

    return {
        formatters,
        currentFormatter,
        formattedResponse,
        isLoading,
        isSaving,
        isFormatting,
        error,
        fetchFormatters,
        fetchFormatter,
        fetchDefaultFormatter,
        createFormatter,
        updateFormatter,
        deleteFormatter,
        setAsDefaultFormatter,
        toggleFormatterStatus,
        formatResponse,
        previewFormatter,
        cloneFormatter,
        clearCurrentFormatter,
        clearFormattedResponse,
        clearError
    }
}