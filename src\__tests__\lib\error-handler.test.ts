/**
 * Error <PERSON>ler Tests
 * 
 * Tests for the error handling utilities
 */

import {
    toAppError,
    formatValidationErrors,
    logError,
    tryCatch,
    ErrorType,
    AppError
} from '@/lib/error-handler';
import { z } from 'zod';

// Mock console.error for testing
const originalConsoleError = console.error;

beforeEach(() => {
    console.error = jest.fn();
});

afterEach(() => {
    console.error = originalConsoleError;
});

describe('Error Handler', () => {
    describe('toAppError', () => {
        it('should handle regular Error objects', () => {
            const error = new Error('Test error');
            const appError = toAppError(error);

            expect(appError.type).toBe(ErrorType.UNKNOWN);
            expect(appError.message).toBe('Test error');
            expect(appError.originalError).toBe(error);
        });

        it('should handle string errors', () => {
            const appError = toAppError('Test error string');

            expect(appError.type).toBe(ErrorType.UNKNOWN);
            expect(appError.message).toBe('Test error string');
        });

        it('should handle Zod validation errors', () => {
            const schema = z.object({
                name: z.string(),
                email: z.string().email(),
            });

            try {
                schema.parse({ name: 'Test', email: 'invalid-email' });
            } catch (error) {
                const appError = toAppError(error);

                expect(appError.type).toBe(ErrorType.VALIDATION);
                expect(appError.details).toBeDefined();
                expect(Object.keys(appError.details || {}).length).toBeGreaterThan(0);
            }
        });

        it('should handle unknown errors', () => {
            const appError = toAppError(null);

            expect(appError.type).toBe(ErrorType.UNKNOWN);
            expect(appError.message).toBe('Something went wrong. Please try again.');
        });
    });

    describe('formatValidationErrors', () => {
        it('should format validation errors correctly', () => {
            const appError: AppError = {
                type: ErrorType.VALIDATION,
                message: 'Validation error',
                details: {
                    name: ['Name is required'],
                    email: ['Invalid email format'],
                    nested: {
                        field: 'Nested error'
                    }
                }
            };

            const formatted = formatValidationErrors(appError);

            expect(formatted.name).toBe('Name is required');
            expect(formatted.email).toBe('Invalid email format');
            expect(formatted['nested.field']).toBe('Nested error');
        });

        it('should return empty object when no details', () => {
            const appError: AppError = {
                type: ErrorType.VALIDATION,
                message: 'Validation error'
            };

            const formatted = formatValidationErrors(appError);

            expect(formatted).toEqual({});
        });
    });

    describe('logError', () => {
        it('should log error with context', () => {
            const error = new Error('Test error');
            logError(error, 'TestContext');

            expect(console.error).toHaveBeenCalled();
            const consoleArgs = (console.error as jest.Mock).mock.calls[0];
            expect(consoleArgs[0]).toContain('[UNKNOWN] [TestContext]');
            expect(consoleArgs[0]).toContain('Test error');
        });

        it('should log error without context', () => {
            const error = new Error('Test error');
            logError(error);

            expect(console.error).toHaveBeenCalled();
            const consoleArgs = (console.error as jest.Mock).mock.calls[0];
            expect(consoleArgs[0]).toContain('[UNKNOWN]');
            expect(consoleArgs[0]).toContain('Test error');
        });
    });

    describe('tryCatch', () => {
        it('should return result on success', async () => {
            const [result, error] = await tryCatch(async () => {
                return 'success';
            });

            expect(result).toBe('success');
            expect(error).toBeNull();
        });

        it('should return error on failure', async () => {
            const testError = new Error('Test error');
            const [result, error] = await tryCatch(async () => {
                throw testError;
            });

            expect(result).toBeNull();
            expect(error).not.toBeNull();
            expect(error?.message).toBe('Test error');
        });

        it('should log error with context', async () => {
            const testError = new Error('Test error');
            const [result, error] = await tryCatch(async () => {
                throw testError;
            }, 'TestContext');

            expect(console.error).toHaveBeenCalled();
            const consoleArgs = (console.error as jest.Mock).mock.calls[0];
            expect(consoleArgs[0]).toContain('[TestContext]');
        });
    });
}); 