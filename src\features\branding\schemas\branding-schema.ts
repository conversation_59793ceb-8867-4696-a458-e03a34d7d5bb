/**
 * Branding Schemas
 * 
 * This file contains Zod validation schemas for branding functionality
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Color schema
 */
export const colorSchema = z.object({
    primary: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color'),
    secondary: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color'),
    accent: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color').optional(),
    text: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color'),
    background: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color'),
    success: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color').optional(),
    warning: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color').optional(),
    error: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Must be a valid hex color').optional(),
});

/**
 * Typography schema
 */
export const typographySchema = z.object({
    fontFamily: z.string(),
    fontSize: z.object({
        base: z.number().positive(),
        heading1: z.number().positive(),
        heading2: z.number().positive(),
        heading3: z.number().positive(),
        small: z.number().positive(),
    }),
    fontWeight: z.object({
        normal: z.number().positive(),
        medium: z.number().positive(),
        bold: z.number().positive(),
    }),
    lineHeight: z.number().positive(),
});

/**
 * Logo schema
 */
export const logoSchema = z.object({
    primary: z.string().url('Must be a valid URL'),
    alternative: z.string().url('Must be a valid URL').optional(),
    favicon: z.string().url('Must be a valid URL'),
    width: z.number().positive().optional(),
    height: z.number().positive().optional(),
});

/**
 * Branding schema
 */
export const brandingSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    colors: colorSchema,
    typography: typographySchema,
    logo: logoSchema,
    tagline: z.string().optional(),
    voiceTone: z.object({
        formal: z.number().min(1).max(5),
        friendly: z.number().min(1).max(5),
        technical: z.number().min(1).max(5),
        persuasive: z.number().min(1).max(5),
    }).optional(),
    isDefault: z.boolean().default(false),
    customCSS: z.string().optional(),
    customJS: z.string().optional(),
});

/**
 * Message format schema
 */
export const messageFormatSchema = z.object({
    brandId: z.string(),
    name: commonSchemas.nonEmptyString,
    template: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    variables: z.array(z.string()).optional(),
    examples: z.array(z.string()).optional(),
    type: z.enum(['greeting', 'response', 'followup', 'error', 'custom']),
    isDefault: z.boolean().default(false),
});

export default {
    colorSchema,
    typographySchema,
    logoSchema,
    brandingSchema,
    messageFormatSchema,
}; 