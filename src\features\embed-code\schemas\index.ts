/**
 * Embed Code Schemas
 */

import { z } from 'zod'

export const embedConfigurationSchema = z.object({
  embedType: z.enum(['inline', 'popup', 'floating']),
  position: z.enum(['bottom-right', 'bottom-left', 'top-right', 'top-left']).optional(),
  allowedDomains: z.array(z.string().url()).min(1, 'At least one domain is required'),
  customCss: z.string().optional(),
  autoOpen: z.boolean().default(false),
  openDelay: z.number().min(0).max(30000).optional(),
  triggerSelector: z.string().optional(),
  zIndex: z.number().min(1).max(999999).optional(),
  responsive: z.boolean().default(true),
  mobileBreakpoint: z.number().min(320).max(1024).optional(),
  analytics: z.boolean().default(true),
  cookieConsent: z.boolean().default(false),
  gdprCompliant: z.boolean().default(false),
  customAttributes: z.record(z.string()).optional()
})

export type EmbedConfigurationInput = z.infer<typeof embedConfigurationSchema>
