/**
 * Response Formatter Types
 * 
 * This file exports all type definitions for the response formatter feature
 */

import { z } from 'zod';
import {
    formatOptionSchema,
    formatSectionSchema,
    styleSettingsSchema,
    responseFormatterSchema,
    formatResponseInputSchema,
    formatResponseResultSchema
} from '../schemas/response-formatter-schema';

/**
 * Format option type
 */
export type FormatOption = z.infer<typeof formatOptionSchema>;

/**
 * Format section type
 */
export type FormatSection = z.infer<typeof formatSectionSchema>;

/**
 * Style settings type
 */
export type StyleSettings = z.infer<typeof styleSettingsSchema>;

/**
 * Heading style type
 */
export type HeadingStyle = 'normal' | 'uppercase' | 'lowercase' | 'capitalize';

/**
 * Heading size type
 */
export type HeadingSize = 'small' | 'medium' | 'large';

/**
 * Font style type
 */
export type FontStyle = 'normal' | 'italic' | 'bold' | 'bold-italic';

/**
 * List style type
 */
export type ListStyle = 'bullet' | 'numbered' | 'checkmark' | 'none';

/**
 * Color scheme type
 */
export type ColorScheme = 'default' | 'minimal' | 'branded' | 'custom';

/**
 * Response formatter type
 */
export type ResponseFormatter = z.infer<typeof responseFormatterSchema>;

/**
 * Formatter type enum
 */
export type FormatterType = 'standard' | 'structured' | 'bullet-list' | 'numbered-list' | 'table' | 'card' | 'custom';

/**
 * Format response input type
 */
export type FormatResponseInput = z.infer<typeof formatResponseInputSchema>;

/**
 * Format response result type
 */
export type FormatResponseResult = z.infer<typeof formatResponseResultSchema>;

/**
 * Custom style options
 */
export interface CustomStyleOptions {
    headingStyle?: HeadingStyle;
    listStyle?: ListStyle;
    includeEmoji?: boolean;
}

/**
 * Formatter filters
 */
export interface FormatterFilters {
    search?: string;
    type?: FormatterType;
    isActive?: boolean;
    isDefault?: boolean;
    tags?: string[];
    brandingId?: string;
    sortBy?: keyof ResponseFormatter;
    sortDirection?: 'asc' | 'desc';
} 