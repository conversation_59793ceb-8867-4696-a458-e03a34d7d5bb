/**
 * Widget Form Schemas
 * 
 * This file contains Zod validation schemas for widget forms
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * Appearance schema for widget customization
 */
export const appearanceSchema = z.object({
    theme: z.object({
        primary: commonSchemas.hexColor,
        secondary: commonSchemas.hexColor.optional(),
        background: commonSchemas.hexColor,
        text: commonSchemas.hexColor,
    }),
    branding: z.object({
        logo: z.string().url().optional(),
        name: z.string().min(1, errorMessages.required),
        showBranding: z.boolean().default(true),
    }),
    layout: z.object({
        position: z.enum(['left', 'right', 'bottom']),
        width: z.number().min(300).max(500),
        height: z.number().min(400).max(800),
        borderRadius: z.number().min(0).max(24),
        avatarUrl: z.string().url().optional(),
    }),
    typography: z.object({
        fontFamily: z.string().optional(),
        fontSize: z.number().min(12).max(18),
    }),
});

/**
 * Behavior schema for widget functionality
 */
export const behaviorSchema = z.object({
    initialMessage: z.string().optional(),
    welcomeMessage: z.string().min(1, errorMessages.required),
    showTimestamp: z.boolean().default(true),
    autoOpen: z.boolean().default(false),
    autoOpenDelay: z.number().min(0).max(60),
    persistHistory: z.boolean().default(true),
    attachments: z.object({
        enabled: z.boolean().default(true),
        maxSize: z.number().min(1).max(20),
        allowedTypes: z.array(z.string()),
    }),
    feedback: z.object({
        enabled: z.boolean().default(true),
        showRatings: z.boolean().default(true),
        showComments: z.boolean().default(true),
    }),
});

/**
 * Integration schema for external services
 */
export const integrationSchema = z.object({
    enabled: z.boolean().default(false),
    type: z.enum(['slack', 'discord', 'ms-teams', 'zapier', 'generic']),
    webhook: z.string().url().optional(),
    apiKey: z.string().optional(),
    events: z.array(z.enum([
        'message_sent',
        'conversation_started',
        'conversation_ended',
        'feedback_received',
        'error_occurred'
    ])),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Security schema for widget protection
 */
export const securitySchema = z.object({
    domains: z.array(z.string()),
    restrictByDomain: z.boolean().default(true),
    dataSanitization: z.boolean().default(true),
    maxRequestsPerMinute: z.number().min(1).max(100),
    storageConsent: z.boolean().default(true),
    privacyPolicyUrl: z.string().url().optional(),
    termsUrl: z.string().url().optional(),
});

/**
 * Widget schema - main schema for widget configuration
 */
export const widgetSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    description: z.string().optional(),
    version: z.string().optional(),
    isActive: z.boolean().default(true),
    projectId: z.number(),
    appearance: appearanceSchema,
    behavior: behaviorSchema,
    integrations: z.array(integrationSchema).optional(),
    security: securitySchema,
    advanced: z.object({
        customCSS: z.string().optional(),
        customJS: z.string().optional(),
        seo: z.object({
            noIndex: z.boolean().default(false),
        }).optional(),
    }).optional(),
});

/**
 * Widget type based on the schema
 */
export type WidgetFormValues = z.infer<typeof widgetSchema>;

export default widgetSchema;

export const widgetPositionSchema = z.object({
    placement: z.enum(['bottom-right', 'bottom-left', 'top-right', 'top-left']),
    offsetX: z.number().min(0).max(100),
    offsetY: z.number().min(0).max(100)
});

export const widgetStyleSchema = z.object({
    primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
    textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
    fontFamily: z.string(),
    buttonStyle: z.enum(['rounded', 'square', 'pill']),
    customCSS: z.string().optional()
});

export const widgetSecuritySchema = z.object({
    allowedDomains: z.array(z.string().url()),
    enableCSP: z.boolean(),
    enableSRI: z.boolean(),
    webhookUrl: z.string().url().optional(),
    sanitizationLevel: z.enum(['strict', 'moderate', 'basic'])
});

export const widgetBehaviorSchema = z.object({
    autoOpen: z.boolean(),
    openDelay: z.number().min(0),
    persistState: z.boolean(),
    enableHistory: z.boolean(),
    enableAttachments: z.boolean(),
    maxAttachmentSize: z.number().min(0)
});

export const widgetCreateSchema = widgetSchema.omit({
    id: true,
    version: true,
    createdAt: true,
    updatedAt: true
});

export const widgetUpdateSchema = widgetCreateSchema.partial();

export type Widget = z.infer<typeof widgetSchema>;
export type WidgetCreate = z.infer<typeof widgetCreateSchema>;
export type WidgetUpdate = z.infer<typeof widgetUpdateSchema>; 