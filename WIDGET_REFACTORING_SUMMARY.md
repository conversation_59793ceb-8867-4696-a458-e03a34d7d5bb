# Widget Database Schema and Backend Architecture Refactoring

## Overview

This refactoring improves the widget system's database schema and backend architecture for better code organization, maintainability, and scalability while preserving all existing functionality.

## Database Schema Changes

### New Normalized Tables

#### 1. `widget_behaviors` Table
- **Purpose**: Stores behavior-related settings separately from main widget data
- **Key Fields**:
  - `auto_open`, `auto_open_delay` - Auto-open behavior
  - `start_minimized` - Initial state
  - `show_typing_indicator` - UI preferences
  - `enable_user_ratings`, `collect_user_data` - User interaction settings
  - `pre_chat_enabled`, `post_chat_enabled` - Form settings
  - `close_after_inactivity`, `inactivity_timeout` - Session management
  - `advanced_settings` - JSON field for future extensions

#### 2. `widget_webhooks` Table
- **Purpose**: Manages webhook/integration configurations with detailed tracking
- **Key Fields**:
  - `url`, `method`, `headers` - HTTP configuration
  - Event triggers: `on_chat_start`, `on_chat_end`, `on_message_sent`, etc.
  - Security: `secret_key`, `verify_ssl`, `timeout_seconds`
  - Monitoring: `success_count`, `failure_count`, `last_triggered_at`
  - Integration metadata: `integration_type`, `integration_config`

#### 3. `widget_logos` Table
- **Purpose**: Handles logo/image assets with metadata and optimization settings
- **Key Fields**:
  - `logo_data` - Base64 encoded image or URL (LONGTEXT)
  - `logo_type` - 'url' or 'base64'
  - Image metadata: `mime_type`, `file_size`, `width`, `height`
  - Display settings: `display_width`, `display_height`, `position`
  - Optimization: `quality`, `auto_optimize`, `format_preference`

### Relationships
- All new tables have foreign key relationships to the main `widgets` table
- Cascade delete ensures data integrity
- Proper indexing for performance optimization

## Backend Architecture Improvements

### Service Layer Architecture

#### 1. `WidgetService` (Main Service)
- **Responsibilities**: Core widget CRUD operations, data transformation
- **Key Methods**:
  - `getUserWidgets()`, `getUserWidget()`, `getPublicWidget()`
  - `createWidget()`, `updateWidget()`, `deleteWidget()`
  - `transformForApi()` - Converts normalized data to API format
  - `isDomainAllowed()` - Domain validation logic

#### 2. `WidgetBehaviorService`
- **Responsibilities**: Behavior configuration management
- **Key Methods**:
  - `updateBehavior()`, `getBehavior()`, `deleteBehavior()`
  - `validateBehaviorSettings()` - Input validation
  - `getPublicBehaviorSettings()` - Public API data
  - Helper methods for specific behavior checks

#### 3. `WidgetLogoService`
- **Responsibilities**: Logo/image management and optimization
- **Key Methods**:
  - `updateLogo()`, `getLogo()`, `deleteLogo()`
  - `processImageUpload()` - Handle base64 image processing
  - `processLogoUrl()` - Handle URL-based logos
  - `validateLogoData()` - Comprehensive validation
  - `getOptimizedLogo()` - Public display optimization

#### 4. `WebhookService` (Enhanced)
- **Responsibilities**: Webhook/integration handling with retry logic
- **Key Methods**:
  - `createOrUpdateWebhook()`, `deleteWebhook()`
  - `triggerNormalizedWebhooks()` - Event-based webhook triggering
  - `testNormalizedWebhook()` - Configuration testing
  - HTTP retry logic with exponential backoff
  - Security signature generation and verification

### Controller Refactoring

#### `WidgetController` Improvements
- **Dependency Injection**: All services injected via constructor
- **Separation of Concerns**: Controllers only handle HTTP requests/responses
- **Business Logic**: Moved to service classes
- **Error Handling**: Consistent error responses across all endpoints
- **Data Transformation**: Unified API response format

## Backward Compatibility

### Data Migration
- **Migration File**: `2024_12_19_000005_migrate_existing_widget_data.php`
- **Process**: Automatically migrates existing widget data to normalized structure
- **Safety**: Preserves original data in main widgets table
- **Chunked Processing**: Handles large datasets efficiently

### API Compatibility
- **Response Format**: Maintains existing API response structure
- **Field Mapping**: Automatic transformation between old and new formats
- **Legacy Support**: Continues to support legacy webhook configurations
- **Frontend Compatibility**: No changes required to existing frontend code

## Key Benefits

### 1. **Improved Maintainability**
- Clear separation of concerns
- Modular service architecture
- Easier testing and debugging
- Reduced code duplication

### 2. **Better Performance**
- Normalized database structure
- Proper indexing
- Query optimization
- Caching support

### 3. **Enhanced Scalability**
- Service-based architecture
- Dependency injection
- Modular components
- Easy feature extension

### 4. **Better Data Integrity**
- Foreign key constraints
- Proper validation
- Transaction support
- Cascade operations

### 5. **Advanced Features**
- Webhook retry logic
- Image optimization
- Detailed monitoring
- Security enhancements

## Implementation Status

### ✅ Completed
- [x] Database schema design and migrations
- [x] Model creation with relationships
- [x] Service layer implementation
- [x] Controller refactoring
- [x] Dependency injection setup
- [x] Data migration script
- [x] Backward compatibility preservation

### 🔄 Next Steps (Optional)
- [ ] Run migrations in production
- [ ] Performance monitoring
- [ ] Additional webhook integrations
- [ ] Image optimization enhancements
- [ ] Advanced analytics features

## Usage Examples

### Creating a Widget (Service Layer)
```php
$widget = $this->widgetService->createWidget($user, [
    'name' => 'My Widget',
    'settings' => [...],
    'logo_url' => 'data:image/jpeg;base64,...',
    // Automatically handles behavior, logo, and webhook creation
]);
```

### Triggering Webhooks
```php
$this->webhookService->triggerNormalizedWebhooks($widgetId, 'message_sent', [
    'message' => 'Hello world',
    'user_id' => 123
]);
```

### Getting Public Widget Data
```php
$widget = $this->widgetService->getPublicWidget($widgetId);
$behaviorSettings = $this->behaviorService->getPublicBehaviorSettings($widget->id);
$logoSettings = $this->logoService->getOptimizedLogo($widget->id);
```

## Conclusion

This refactoring creates a robust, scalable foundation for the widget system while maintaining complete backward compatibility. The normalized database structure and service-oriented architecture provide a solid base for future enhancements and feature development.
