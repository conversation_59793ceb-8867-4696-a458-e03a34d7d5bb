/**
 * User Schemas
 * 
 * This file contains Zod validation schemas for user management
 */

import { z } from 'zod';
import { createFormSchema, errorMessages, commonSchemas } from '@/lib/zod-schemas';

/**
 * User schema
 */
export const userSchema = createFormSchema({
    name: commonSchemas.nonEmptyString,
    email: commonSchemas.email,
    role: z.enum(['admin', 'manager', 'user']).default('user'),
    isActive: z.boolean().default(true),
    avatar: z.string().url().optional(),
    bio: z.string().optional(),
    phone: commonSchemas.phone.optional(),
    password: z.string().min(8, errorMessages.min('Password', 8)).optional(),
    lastLogin: z.date().optional(),
    twoFactorEnabled: z.boolean().default(false),
    preferences: z.object({
        theme: z.enum(['light', 'dark', 'system']).default('system'),
        language: z.string().default('en'),
        notifications: z.object({
            email: z.boolean().default(true),
            inApp: z.boolean().default(true),
            marketing: z.boolean().default(false),
        }).optional(),
    }).optional(),
    permissions: z.array(z.string()).optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * User login schema
 */
export const loginSchema = z.object({
    email: commonSchemas.email,
    password: z.string().min(1, errorMessages.required),
    rememberMe: z.boolean().default(false),
});

/**
 * User registration schema
 */
export const registrationSchema = z.object({
    name: commonSchemas.nonEmptyString,
    email: commonSchemas.email,
    password: commonSchemas.password,
    confirmPassword: z.string().min(1, errorMessages.required),
    acceptTerms: z.boolean().refine(val => val === true, {
        message: 'You must accept the terms and conditions',
    }),
}).refine((data) => data.password === data.confirmPassword, {
    message: errorMessages.passwordMatch,
    path: ['confirmPassword'],
});

/**
 * Password reset request schema
 */
export const passwordResetRequestSchema = z.object({
    email: commonSchemas.email,
});

/**
 * Password reset schema
 */
export const passwordResetSchema = z.object({
    token: z.string().min(1, errorMessages.required),
    password: commonSchemas.password,
    confirmPassword: z.string().min(1, errorMessages.required),
}).refine((data) => data.password === data.confirmPassword, {
    message: errorMessages.passwordMatch,
    path: ['confirmPassword'],
});

/**
 * Password change schema
 */
export const passwordChangeSchema = z.object({
    currentPassword: z.string().min(1, errorMessages.required),
    newPassword: commonSchemas.password,
    confirmPassword: z.string().min(1, errorMessages.required),
}).refine((data) => data.newPassword === data.confirmPassword, {
    message: errorMessages.passwordMatch,
    path: ['confirmPassword'],
});

/**
 * User update schema
 */
export const userUpdateSchema = userSchema.partial().omit({ password: true });

export default {
    userSchema,
    loginSchema,
    registrationSchema,
    passwordResetRequestSchema,
    passwordResetSchema,
    passwordChangeSchema,
    userUpdateSchema,
}; 