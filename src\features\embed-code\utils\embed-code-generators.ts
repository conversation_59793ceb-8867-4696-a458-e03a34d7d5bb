/**
 * Embed Code Generator Utilities
 */

import { EmbedConfiguration } from '../types'

/**
 * Generate HTML embed code
 */
export function generateHtmlEmbedCode(widgetId: number, config: EmbedConfiguration): string {
  const baseUrl = window.location.origin
  const attributes = Object.entries(config.customAttributes || {})
    .map(([key, value]) => `data-${key}="${value}"`)
    .join(' ')

  return `<!-- Widget Embed Code -->
<div id="widget-${widgetId}" 
     data-widget-id="${widgetId}"
     data-embed-type="${config.embedType}"
     ${config.position ? `data-position="${config.position}"` : ''}
     ${config.autoOpen ? 'data-auto-open="true"' : ''}
     ${config.openDelay ? `data-open-delay="${config.openDelay}"` : ''}
     ${attributes}>
</div>
<script src="${baseUrl}/widget.js" async></script>`
}

/**
 * Generate JavaScript embed code
 */
export function generateJavaScriptEmbedCode(widgetId: number, config: EmbedConfiguration): string {
  const baseUrl = window.location.origin
  
  return `(function() {
  var script = document.createElement('script');
  script.src = '${baseUrl}/widget.js';
  script.async = true;
  script.onload = function() {
    window.ChatWidget.init({
      widgetId: ${widgetId},
      embedType: '${config.embedType}',
      ${config.position ? `position: '${config.position}',` : ''}
      ${config.autoOpen ? `autoOpen: true,` : ''}
      ${config.openDelay ? `openDelay: ${config.openDelay},` : ''}
      ${config.responsive ? `responsive: true,` : ''}
      ${config.zIndex ? `zIndex: ${config.zIndex},` : ''}
      ${config.customCss ? `customCss: \`${config.customCss}\`,` : ''}
      ${config.triggerSelector ? `triggerSelector: '${config.triggerSelector}',` : ''}
      customAttributes: ${JSON.stringify(config.customAttributes || {})}
    });
  };
  document.head.appendChild(script);
})();`
}

/**
 * Generate CSS for embed
 */
export function generateEmbedCss(config: EmbedConfiguration): string {
  let css = `/* Widget Embed Styles */
#widget-${config.widgetId} {
  ${config.zIndex ? `z-index: ${config.zIndex};` : ''}
}

.chat-widget {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  ${config.responsive ? `
  @media (max-width: ${config.mobileBreakpoint || 768}px) {
    width: 100% !important;
    height: 100% !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }` : ''}
}`

  if (config.customCss) {
    css += `\n\n/* Custom CSS */\n${config.customCss}`
  }

  return css
}

/**
 * Generate installation instructions
 */
export function generateInstallationInstructions(config: EmbedConfiguration): string {
  const steps = [
    '1. Copy the embed code below',
    '2. Paste it into your website\'s HTML, just before the closing </body> tag',
    '3. Save and publish your website'
  ]

  if (config.embedType === 'popup') {
    steps.push('4. The chat widget will appear as a popup when triggered')
  } else if (config.embedType === 'floating') {
    steps.push('4. The chat widget will appear as a floating button')
  } else {
    steps.push('4. The chat widget will appear inline where you placed the code')
  }

  if (config.allowedDomains.length > 0) {
    steps.push(`5. Make sure your domain (${config.allowedDomains.join(', ')}) is in the allowed domains list`)
  }

  return steps.join('\n')
}
