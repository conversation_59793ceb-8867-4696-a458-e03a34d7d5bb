import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Link,
  MessageSquare,
  Mail,
  Slack,
  Zap,
  Plus,
  CheckCircle2,
  ExternalLink,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

// Import services and utilities
import IntegrationsTab from '@/components/widget-builder/IntegrationsTab';
import { widgetBuilderService, WebhookConfig } from '@/features/widget-builder';
import { validateWebhookUrl } from '@/features/widget-builder/utils';



interface WebhookModalProps {
  form: UseFormReturn<any>;
  onClose: () => void;
  widgetId?: string;
}

const popularIntegrations = [
  {
    id: 'slack',
    name: 'Slack',
    description: 'Send new messages and notifications to your Slack channels',
    icon: Slack,
    color: 'bg-purple-100 text-purple-600',
    setupUrl: 'https://api.slack.com/messaging/webhooks',
    benefit: 'Never miss a customer message - get instant notifications in Slack',
  },
  {
    id: 'email',
    name: 'Email Notifications',
    description: 'Receive chat notifications via email',
    icon: Mail,
    color: 'bg-blue-100 text-blue-600',
    setupUrl: '#',
    benefit: 'Get notified even when you\'re away from your computer',
  },
  {
    id: 'zapier',
    name: 'Zapier',
    description: 'Connect to 5000+ apps through Zapier automation',
    icon: Zap,
    color: 'bg-orange-100 text-orange-600',
    setupUrl: 'https://zapier.com/apps/webhook/integrations',
    benefit: 'Automate workflows and connect to your entire tech stack',
  },
  {
    id: 'custom',
    name: 'Custom Webhook',
    description: 'Send data to any custom endpoint or API',
    icon: Link,
    color: 'bg-gray-100 text-gray-600',
    setupUrl: '#',
    benefit: 'Full control over how and where your chat data is sent',
  },
];

/**
 * Webhook Modal Component
 *
 * Provides a user-friendly interface for setting up webhooks and integrations
 * with popular tools and custom endpoints.
 */
const WebhookModal = ({ form, onClose, widgetId }: WebhookModalProps) => {
  const [activeTab, setActiveTab] = useState('popular');
  const [selectedIntegration, setSelectedIntegration] = useState<string | null>(null);
  const [webhookSettings, setWebhookSettings] = useState({
    enabled: true,
    events: {
      newMessage: true,
      chatStart: false,
      chatEnd: false,
      userRating: true,
    }
  });

  // Webhook testing state
  const [webhookUrls, setWebhookUrls] = useState({
    slack: '',
    email: '',
    custom: '',
    zapier: '',
  });
  const [webhookSecret, setWebhookSecret] = useState('');
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string; details?: any } | null>(null);

  const handleIntegrationSelect = (integrationId: string) => {
    setSelectedIntegration(integrationId);
    setTestResult(null); // Clear previous test results
    form.setValue('features.webhooks', true);
  };

  const testWebhook = async () => {
    if (!selectedIntegration) {
      toast.error('Please select an integration to test');
      return;
    }

    const webhookUrl = webhookUrls[selectedIntegration as keyof typeof webhookUrls];
    if (!webhookUrl.trim()) {
      toast.error('Please enter a webhook URL');
      return;
    }

    // Validate webhook URL format
    if (!validateWebhookUrl(webhookUrl)) {
      toast.error('Please enter a valid webhook URL (http:// or https://)');
      return;
    }

    setIsTestingWebhook(true);
    setTestResult(null);

    try {
      const webhookConfig: WebhookConfig = {
        url: webhookUrl,
        method: 'POST',
        headers: selectedIntegration === 'slack' ? {
          'Content-Type': 'application/json'
        } : {},
        secret: webhookSecret || undefined,
        eventType: 'test',
      };

      if (widgetId) {
        // Widget exists - use server-side testing
        const response = await widgetBuilderService.testWebhook(Number(widgetId), webhookConfig);

        setTestResult({
          success: true,
          message: 'Webhook test successful!',
          details: response.data.result,
        });
      } else {
        // Widget doesn't exist yet - do a simple URL validation test
        const testPayload = {
          text: "🧪 Test message from Widget Builder",
          timestamp: new Date().toISOString(),
          event_type: "test",
          widget_name: "Test Widget"
        };

        const response = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testPayload),
        });

        if (response.ok) {
          setTestResult({
            success: true,
            message: 'Webhook URL is reachable and accepting requests!',
            details: { status: response.status, statusText: response.statusText },
          });
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      toast.success('Webhook test completed successfully');
    } catch (error: any) {
      const errorMessage = error.message || 'Webhook test failed';

      setTestResult({
        success: false,
        message: errorMessage,
        details: error,
      });

      toast.error(errorMessage);
    } finally {
      setIsTestingWebhook(false);
    }
  };

  const handleSaveAndClose = () => {
    // Set the feature toggle
    form.setValue('features.webhooks', webhookSettings.enabled);

    if (activeTab === 'popular' && webhookSettings.enabled && selectedIntegration) {
      // Save simple integration from Popular tab
      const currentIntegrations = form.getValues('advanced.integrations') || [];

      // Create new integration object
      const newIntegration = {
        id: `${selectedIntegration}-${Date.now()}`,
        type: selectedIntegration,
        name: popularIntegrations.find(i => i.id === selectedIntegration)?.name || selectedIntegration,
        url: webhookUrls[selectedIntegration as keyof typeof webhookUrls] || '',
        active: true,
        events: Object.entries(webhookSettings.events)
          .filter(([_, enabled]) => enabled)
          .map(([event, _]) => event),
        secret: selectedIntegration === 'custom' ? webhookSecret : undefined,
        created_at: new Date().toISOString()
      };

      // Add to integrations array
      const updatedIntegrations = [...currentIntegrations, newIntegration];
      form.setValue('advanced.integrations', updatedIntegrations);

      toast.success(`${newIntegration.name} integration configured successfully!`);
    }
    // Advanced tab integrations are handled by the IntegrationsTab component directly

    onClose();
  };

  return (
    <div className="space-y-6">
      {/* Settings Toggle */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Webhook Integrations</CardTitle>
              <CardDescription>
                Automatically send chat data to your favorite tools and services
              </CardDescription>
            </div>
            <Switch
              checked={webhookSettings.enabled}
              onCheckedChange={(checked) => {
                setWebhookSettings({ ...webhookSettings, enabled: checked });
                form.setValue('features.webhooks', checked);
              }}
            />
          </div>
        </CardHeader>

        {webhookSettings.enabled && (
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium mb-3 block">
                Send notifications for:
              </Label>
              <div className="space-y-2">
                {Object.entries({
                  newMessage: 'New chat messages',
                  chatStart: 'When chat sessions start',
                  chatEnd: 'When chat sessions end',
                  userRating: 'User ratings and feedback',
                }).map(([key, label]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <Switch
                      id={key}
                      checked={webhookSettings.events[key as keyof typeof webhookSettings.events]}
                      onCheckedChange={(checked) =>
                        setWebhookSettings({
                          ...webhookSettings,
                          events: { ...webhookSettings.events, [key]: checked }
                        })
                      }
                    />
                    <Label htmlFor={key} className="text-sm">
                      {label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {webhookSettings.enabled && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="popular">Popular Integrations</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Setup</TabsTrigger>
          </TabsList>

          <TabsContent value="popular" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {popularIntegrations.map((integration) => {
                const Icon = integration.icon;
                const isSelected = selectedIntegration === integration.id;

                return (
                  <Card
                    key={integration.id}
                    className={`
                      cursor-pointer transition-all hover:shadow-md
                      ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''}
                    `}
                    onClick={() => handleIntegrationSelect(integration.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${integration.color}`}>
                          <Icon className="w-5 h-5" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-base flex items-center space-x-2">
                            <span>{integration.name}</span>
                            {isSelected && (
                              <CheckCircle2 className="w-4 h-4 text-green-600" />
                            )}
                          </CardTitle>
                        </div>
                        {integration.setupUrl !== '#' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(integration.setupUrl, '_blank');
                            }}
                          >
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-sm mb-3">
                        {integration.description}
                      </CardDescription>
                      <div className="bg-green-50 border border-green-200 rounded-md p-2">
                        <p className="text-sm text-green-700">
                          💡 {integration.benefit}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {selectedIntegration && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Setup</CardTitle>
                  <CardDescription>
                    Get started with {popularIntegrations.find(i => i.id === selectedIntegration)?.name} in minutes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedIntegration === 'slack' && (
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="slack-webhook" className="text-sm font-medium">
                          Slack Webhook URL
                        </Label>
                        <Input
                          id="slack-webhook"
                          placeholder="https://hooks.slack.com/services/..."
                          className="mt-1"
                          value={webhookUrls.slack}
                          onChange={(e) => setWebhookUrls({ ...webhookUrls, slack: e.target.value })}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Get this URL from your Slack app settings
                        </p>
                      </div>
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          <strong>How to get your Slack webhook URL:</strong><br />
                          1. Go to your Slack workspace settings<br />
                          2. Create a new app or use an existing one<br />
                          3. Enable "Incoming Webhooks" and create a new webhook<br />
                          4. Copy the webhook URL and paste it above
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {selectedIntegration === 'email' && (
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="email-address" className="text-sm font-medium">
                          Email Address
                        </Label>
                        <Input
                          id="email-address"
                          type="email"
                          placeholder="<EMAIL>"
                          className="mt-1"
                          value={webhookUrls.email}
                          onChange={(e) => setWebhookUrls({ ...webhookUrls, email: e.target.value })}
                        />
                      </div>
                      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 dark:bg-blue-950/50 dark:border-blue-800">
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          📧 Email notifications will be sent from our system to the address above.
                          Make sure to whitelist our sending domain to avoid spam filters.
                        </p>
                      </div>
                    </div>
                  )}

                  {selectedIntegration === 'custom' && (
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="custom-webhook" className="text-sm font-medium">
                          Webhook URL
                        </Label>
                        <Input
                          id="custom-webhook"
                          placeholder="https://your-api.com/webhook"
                          className="mt-1"
                          value={webhookUrls.custom}
                          onChange={(e) => setWebhookUrls({ ...webhookUrls, custom: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="webhook-secret" className="text-sm font-medium">
                          Secret Key (Optional)
                        </Label>
                        <Input
                          id="webhook-secret"
                          placeholder="your-secret-key"
                          className="mt-1"
                          value={webhookSecret}
                          onChange={(e) => setWebhookSecret(e.target.value)}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Used to verify webhook authenticity
                        </p>
                      </div>
                    </div>
                  )}

                  <Button
                    className="w-full"
                    onClick={testWebhook}
                    disabled={isTestingWebhook || !webhookUrls[selectedIntegration as keyof typeof webhookUrls]?.trim()}
                  >
                    {isTestingWebhook ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Testing Connection...
                      </>
                    ) : (
                      'Test Connection'
                    )}
                  </Button>

                  {/* Test Results */}
                  {testResult && (
                    <Alert variant={testResult.success ? "default" : "destructive"}>
                      <CheckCircle2 className={`h-4 w-4 ${testResult.success ? 'text-green-600' : 'text-red-600'}`} />
                      <AlertDescription>
                        <div className="space-y-2">
                          <p className="font-medium">{testResult.message}</p>
                          {testResult.details && (
                            <details className="text-xs">
                              <summary className="cursor-pointer">View Details</summary>
                              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                                {JSON.stringify(testResult.details, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Advanced Integration Setup</CardTitle>
                <CardDescription>
                  Use our full integration manager for complex configurations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <IntegrationsTab
                  field={{
                    value: form.getValues('advanced.integrations') || [],
                    onChange: (integrations) => form.setValue('advanced.integrations', integrations),
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default WebhookModal;
