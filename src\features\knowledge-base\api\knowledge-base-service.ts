/**
 * Knowledge Base API Service
 * 
 * This file contains all API calls related to knowledge base functionality
 */

import axios from 'axios';
import { API_BASE_URL } from '@/lib/constants';
import {
    Document,
    Project,
    ScrapedUrl,
    DatabaseConnection,
    SearchResult,
    KnowledgeBaseContext
} from '../types';

/**
 * Base API endpoint for knowledge base
 */
const KNOWLEDGE_BASE_API = `${API_BASE_URL}/knowledge-base`;

/**
 * Knowledge Base API Service
 */
export const knowledgeBaseService = {
    /**
     * Get all projects
     */
    getProjects: async (): Promise<Project[]> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/projects`);
            return response.data;
        } catch (error) {
            console.error('Failed to fetch projects:', error);
            throw new Error('Failed to fetch projects');
        }
    },

    /**
     * Get a project by ID
     */
    getProject: async (projectId: number): Promise<Project> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/projects/${projectId}`);
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch project with ID ${projectId}:`, error);
            throw new Error(`Failed to fetch project with ID ${projectId}`);
        }
    },

    /**
     * Create a new project
     */
    createProject: async (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> => {
        try {
            const response = await axios.post(`${KNOWLEDGE_BASE_API}/projects`, project);
            return response.data;
        } catch (error) {
            console.error('Failed to create project:', error);
            throw new Error('Failed to create project');
        }
    },

    /**
     * Update a project
     */
    updateProject: async (projectId: number, project: Partial<Project>): Promise<Project> => {
        try {
            const response = await axios.put(`${KNOWLEDGE_BASE_API}/projects/${projectId}`, project);
            return response.data;
        } catch (error) {
            console.error(`Failed to update project with ID ${projectId}:`, error);
            throw new Error(`Failed to update project with ID ${projectId}`);
        }
    },

    /**
     * Delete a project
     */
    deleteProject: async (projectId: number): Promise<{ success: boolean }> => {
        try {
            await axios.delete(`${KNOWLEDGE_BASE_API}/projects/${projectId}`);
            return { success: true };
        } catch (error) {
            console.error(`Failed to delete project with ID ${projectId}:`, error);
            throw new Error(`Failed to delete project with ID ${projectId}`);
        }
    },

    /**
     * Upload a document
     */
    uploadDocument: async (projectId: number, file: File): Promise<Document> => {
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('projectId', projectId.toString());

            const response = await axios.post(`${KNOWLEDGE_BASE_API}/documents/upload`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            return response.data;
        } catch (error) {
            console.error('Failed to upload document:', error);
            throw new Error('Failed to upload document');
        }
    },

    /**
     * Get all documents for a project
     */
    getDocuments: async (projectId: number): Promise<Document[]> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/documents`, {
                params: { projectId },
            });
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch documents for project ${projectId}:`, error);
            throw new Error(`Failed to fetch documents for project ${projectId}`);
        }
    },

    /**
     * Get a document by ID
     */
    getDocument: async (documentId: string): Promise<Document> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/documents/${documentId}`);
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch document with ID ${documentId}:`, error);
            throw new Error(`Failed to fetch document with ID ${documentId}`);
        }
    },

    /**
     * Delete a document
     */
    deleteDocument: async (documentId: string): Promise<{ success: boolean }> => {
        try {
            await axios.delete(`${KNOWLEDGE_BASE_API}/documents/${documentId}`);
            return { success: true };
        } catch (error) {
            console.error(`Failed to delete document with ID ${documentId}:`, error);
            throw new Error(`Failed to delete document with ID ${documentId}`);
        }
    },

    /**
     * Generate embeddings for a document
     */
    generateEmbeddings: async (documentId: string): Promise<{ success: boolean }> => {
        try {
            const response = await axios.post(`${KNOWLEDGE_BASE_API}/documents/${documentId}/embeddings`);
            return response.data;
        } catch (error) {
            console.error(`Failed to generate embeddings for document ${documentId}:`, error);
            throw new Error(`Failed to generate embeddings for document ${documentId}`);
        }
    },

    /**
     * Add a web scrape URL
     */
    addScrapedUrl: async (projectId: number, url: string): Promise<ScrapedUrl> => {
        try {
            const response = await axios.post(`${KNOWLEDGE_BASE_API}/scrape`, {
                projectId,
                url,
            });
            return response.data;
        } catch (error) {
            console.error(`Failed to add scraped URL for project ${projectId}:`, error);
            throw new Error(`Failed to add scraped URL for project ${projectId}`);
        }
    },

    /**
     * Get all scraped URLs for a project
     */
    getScrapedUrls: async (projectId: number): Promise<ScrapedUrl[]> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/scrape`, {
                params: { projectId },
            });
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch scraped URLs for project ${projectId}:`, error);
            throw new Error(`Failed to fetch scraped URLs for project ${projectId}`);
        }
    },

    /**
     * Delete a scraped URL
     */
    deleteScrapedUrl: async (urlId: number): Promise<{ success: boolean }> => {
        try {
            await axios.delete(`${KNOWLEDGE_BASE_API}/scrape/${urlId}`);
            return { success: true };
        } catch (error) {
            console.error(`Failed to delete scraped URL with ID ${urlId}:`, error);
            throw new Error(`Failed to delete scraped URL with ID ${urlId}`);
        }
    },

    /**
     * Add a database connection
     */
    addDatabaseConnection: async (connection: Omit<DatabaseConnection, 'id' | 'tables'>): Promise<DatabaseConnection> => {
        try {
            const response = await axios.post(`${KNOWLEDGE_BASE_API}/database-connections`, connection);
            return response.data;
        } catch (error) {
            console.error(`Failed to add database connection for project ${connection.projectId}:`, error);
            throw new Error(`Failed to add database connection for project ${connection.projectId}`);
        }
    },

    /**
     * Get all database connections for a project
     */
    getDatabaseConnections: async (projectId: number): Promise<DatabaseConnection[]> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/database-connections`, {
                params: { projectId },
            });
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch database connections for project ${projectId}:`, error);
            throw new Error(`Failed to fetch database connections for project ${projectId}`);
        }
    },

    /**
     * Get tables from a database connection
     */
    getDatabaseTables: async (connectionId: number): Promise<string[]> => {
        try {
            const response = await axios.get(`${KNOWLEDGE_BASE_API}/database-connections/${connectionId}/tables`);
            return response.data;
        } catch (error) {
            console.error(`Failed to fetch tables for database connection ${connectionId}:`, error);
            throw new Error(`Failed to fetch tables for database connection ${connectionId}`);
        }
    },

    /**
     * Delete a database connection
     */
    deleteDatabaseConnection: async (connectionId: number): Promise<{ success: boolean }> => {
        try {
            await axios.delete(`${KNOWLEDGE_BASE_API}/database-connections/${connectionId}`);
            return { success: true };
        } catch (error) {
            console.error(`Failed to delete database connection with ID ${connectionId}:`, error);
            throw new Error(`Failed to delete database connection with ID ${connectionId}`);
        }
    },

    /**
     * Search knowledge base
     */
    search: async (
        query: string,
        context: KnowledgeBaseContext
    ): Promise<SearchResult[]> => {
        try {
            const response = await axios.post(`${KNOWLEDGE_BASE_API}/search`, {
                query,
                ...context,
            });
            return response.data;
        } catch (error) {
            console.error('Failed to search knowledge base:', error);
            throw new Error('Failed to search knowledge base');
        }
    },
};

export default knowledgeBaseService; 