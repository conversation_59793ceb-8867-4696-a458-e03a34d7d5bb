/**
 * AI Model Types
 * 
 * This file exports all type definitions for the AI models feature
 */

import { z } from 'zod';
import {
  aiModelSchema,
  modelParametersSchema,
  testModelRequestSchema,
  testModelResponseSchema
} from '../schemas/ai-model-schema';

/**
 * AI Model Parameters type
 */
export type ModelParameters = z.infer<typeof modelParametersSchema>;

/**
 * AI Model type
 */
export type AIModel = z.infer<typeof aiModelSchema>;

/**
 * AI Provider type
 */
export type AIProvider = AIModel['provider'];

/**
 * Model capability type
 */
export type ModelCapability = 'chat' | 'completion' | 'embeddings' | 'images' | 'function-calling';

/**
 * AI model with usage statistics
 */
export interface AIModelWithStats extends AIModel {
  stats: {
    totalRequests: number;
    totalTokensUsed: number;
    averageLatency: number;
    errorRate: number;
    lastUsed: string;
  };
}

/**
 * AI model test request
 */
export type TestModelRequest = z.infer<typeof testModelRequestSchema>;

/**
 * AI model test response
 */
export type TestModelResponse = z.infer<typeof testModelResponseSchema>;

/**
 * AI model filters
 */
export interface AIModelFilters {
  search?: string;
  isActive?: boolean;
  provider?: AIProvider;
  capability?: ModelCapability;
  sortBy?: keyof AIModel;
  sortDirection?: 'asc' | 'desc';
}

/**
 * Create AI model input
 */
export type CreateAIModelInput = Omit<AIModel, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Update AI model input
 */
export type UpdateAIModelInput = Partial<Omit<AIModel, 'id' | 'createdAt' | 'updatedAt'>>;

/**
 * AI model response
 */
export interface AIModelResponse {
  id: string;
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  latency: number;
  timestamp: string;
} 