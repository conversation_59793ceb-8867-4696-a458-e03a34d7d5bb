/**
 * API Tester Types
 */

export interface ApiRoute {
  id: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  path: string
  name: string
  description?: string
  parameters?: ApiParameter[]
  headers?: Record<string, string>
  middleware?: string[]
  authenticated?: boolean
}

export interface ApiParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  required: boolean
  description?: string
  example?: any
  validation?: string[]
}

export interface ApiTestRequest {
  method: string
  url: string
  headers?: Record<string, string>
  body?: any
  parameters?: Record<string, any>
}

export interface ApiTestResponse {
  success: boolean
  status: number
  statusText: string
  headers: Record<string, string>
  data: any
  duration: number
  error?: string
}

export interface TestConfiguration {
  id: string
  name: string
  description?: string
  requests: ApiTestRequest[]
  createdAt: string
  updatedAt: string
}
