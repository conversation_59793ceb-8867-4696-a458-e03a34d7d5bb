/**
 * User Management Types
 * 
 * This file exports all type definitions for the user management feature
 */

import { z } from 'zod';
import {
  userSchema,
  loginSchema,
  registrationSchema,
  passwordResetRequestSchema,
  passwordResetSchema,
  passwordChangeSchema,
  userUpdateSchema,
  roleSchema,
  roleCreateSchema,
  roleUpdateSchema,
  roleWithUserCountSchema,
  rolePermissionAssignmentSchema,
  permissionSchema,
  permissionCreateSchema,
  permissionUpdateSchema,
  permissionTypeEnum
} from '../schemas';

/**
 * User type
 */
export type User = z.infer<typeof userSchema>;

/**
 * User role type
 */
export type UserRole = 'admin' | 'manager' | 'user';

/**
 * Theme preference type
 */
export type ThemePreference = 'light' | 'dark' | 'system';

/**
 * Login credentials type
 */
export type LoginCredentials = z.infer<typeof loginSchema>;

/**
 * Registration data type
 */
export type RegistrationData = z.infer<typeof registrationSchema>;

/**
 * Password reset request type
 */
export type PasswordResetRequest = z.infer<typeof passwordResetRequestSchema>;

/**
 * Password reset data type
 */
export type PasswordResetData = z.infer<typeof passwordResetSchema>;

/**
 * Password change data type
 */
export type PasswordChangeData = z.infer<typeof passwordChangeSchema>;

/**
 * User update data type
 */
export type UserUpdateData = z.infer<typeof userUpdateSchema>;

/**
 * Authentication result type
 */
export interface AuthResult {
  user: User;
  token: string;
  expiresAt: string;
  refreshToken?: string;
}

/**
 * User filter options
 */
export interface UserFilters {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  sortBy?: keyof User;
  sortDirection?: 'asc' | 'desc';
}

/**
 * User pagination result
 */
export interface UserPagination {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Guest user type
 */
export interface GuestUser {
  id: number;
  fullname: string;
  email: string | null;
  phone: string;
  session_id: string;
  widget_id: number;
  widget_name: string;
  created_at: string;
}

/**
 * Chat message type
 */
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  created_at: string;
}

/**
 * Role type
 */
export type Role = z.infer<typeof roleSchema>;

/**
 * Role create data type
 */
export type RoleCreateData = z.infer<typeof roleCreateSchema>;

/**
 * Role update data type
 */
export type RoleUpdateData = z.infer<typeof roleUpdateSchema>;

/**
 * Role with user count type
 */
export type RoleWithUserCount = z.infer<typeof roleWithUserCountSchema>;

/**
 * Role permission assignment type
 */
export type RolePermissionAssignment = z.infer<typeof rolePermissionAssignmentSchema>;

/**
 * Permission type
 */
export type Permission = z.infer<typeof permissionSchema>;

/**
 * Permission create data type
 */
export type PermissionCreateData = z.infer<typeof permissionCreateSchema>;

/**
 * Permission update data type
 */
export type PermissionUpdateData = z.infer<typeof permissionUpdateSchema>;

/**
 * Permission type enum
 */
export type PermissionType = z.infer<typeof permissionTypeEnum>; 