/**
 * API Tester Service
 *
 * Service for testing API endpoints and managing API test configurations
 */

import api from '@/utils/api'
import { ApiRoute, ApiTestRequest, ApiTestResponse } from '../types'

export const apiTesterService = {
  /**
   * Get all available API routes
   */
  getApiRoutes: async (): Promise<ApiRoute[]> => {
    try {
      const response = await api.get('/api/test/routes')
      return response.data.data
    } catch (error) {
      console.error('Error fetching API routes:', error)
      throw error
    }
  },

  /**
   * Test an API endpoint
   */
  testEndpoint: async (request: ApiTestRequest): Promise<ApiTestResponse> => {
    try {
      const response = await api.post('/api/test/endpoint', request)
      return response.data
    } catch (error) {
      console.error('Error testing endpoint:', error)
      throw error
    }
  },

  /**
   * Get API documentation
   */
  getApiDocumentation: async (): Promise<any> => {
    try {
      const response = await api.get('/api/test/documentation')
      return response.data
    } catch (error) {
      console.error('Error fetching API documentation:', error)
      throw error
    }
  },

  /**
   * Save test configuration
   */
  saveTestConfiguration: async (config: any): Promise<any> => {
    try {
      const response = await api.post('/api/test/configurations', config)
      return response.data
    } catch (error) {
      console.error('Error saving test configuration:', error)
      throw error
    }
  },

  /**
   * Get saved test configurations
   */
  getTestConfigurations: async (): Promise<any[]> => {
    try {
      const response = await api.get('/api/test/configurations')
      return response.data.data
    } catch (error) {
      console.error('Error fetching test configurations:', error)
      throw error
    }
  },

  /**
   * Get all available API routes
   */
  getApiRoutes: async (): Promise<any[]> => {
    try {
      // Mock data for now - replace with actual API call when backend is ready
      return [
        {
          uri: 'api/widgets',
          methods: ['GET', 'POST'],
          name: 'widgets.index',
          controller: 'WidgetController@index',
          category: 'Widgets'
        },
        {
          uri: 'api/widgets/{id}',
          methods: ['GET', 'PUT', 'DELETE'],
          name: 'widgets.show',
          controller: 'WidgetController@show',
          category: 'Widgets'
        },
        {
          uri: 'api/ai-models',
          methods: ['GET', 'POST'],
          name: 'ai-models.index',
          controller: 'AIModelController@index',
          category: 'AI Models'
        },
        {
          uri: 'api/chat/session/init',
          methods: ['POST'],
          name: 'chat.init',
          controller: 'ChatController@initSession',
          category: 'Chat'
        }
      ]
    } catch (error) {
      console.error('Error fetching API routes:', error)
      throw error
    }
  },

  /**
   * Generate example data for API testing
   */
  generateExampleData: (endpoint: string, method: string): any => {
    const randomId = Math.floor(Math.random() * 1000)
    const timestamp = new Date().toISOString()

    // Widget examples
    if (endpoint.includes('widgets')) {
      if (method.toLowerCase() === 'post') {
        return {
          name: `Test Widget ${randomId}`,
          description: `Test widget created at ${new Date().toLocaleTimeString()}`,
          settings: {
            primaryColor: '#3b82f6',
            position: 'bottom-right',
            autoOpen: false
          }
        }
      }
      if (method.toLowerCase() === 'put') {
        return {
          name: `Updated Widget ${randomId}`,
          settings: {
            primaryColor: '#ef4444',
            position: 'bottom-left'
          }
        }
      }
    }

    // AI Model examples
    if (endpoint.includes('ai-models')) {
      if (method.toLowerCase() === 'post') {
        return {
          name: `Test Model ${randomId}`,
          provider: 'openai',
          api_key: 'sk-test-key-here',
          settings: {
            temperature: 0.7,
            max_tokens: 2048,
            model_name: 'gpt-3.5-turbo'
          }
        }
      }
    }

    // Chat examples
    if (endpoint.includes('chat')) {
      if (endpoint.includes('session/init')) {
        return {
          widget_id: `widget_${randomId}`,
          visitor_id: `visitor_${randomId}_${Date.now()}`,
          timestamp: timestamp
        }
      }
      if (endpoint.includes('message')) {
        return {
          session_id: `session_${randomId}_${Date.now()}`,
          message: `Test message from API tester at ${new Date().toLocaleTimeString()}`,
          metadata: {
            source: 'api_tester',
            page: 'test_page',
            timestamp: timestamp
          }
        }
      }
    }

    return {}
  }
}
