/**
 * API Tester Service
 * 
 * Service for testing API endpoints and managing API test configurations
 */

import api from '@/utils/api'
import { ApiRoute, ApiTestRequest, ApiTestResponse } from '../types'

export const apiTesterService = {
  /**
   * Get all available API routes
   */
  getApiRoutes: async (): Promise<ApiRoute[]> => {
    try {
      const response = await api.get('/api/test/routes')
      return response.data.data
    } catch (error) {
      console.error('Error fetching API routes:', error)
      throw error
    }
  },

  /**
   * Test an API endpoint
   */
  testEndpoint: async (request: ApiTestRequest): Promise<ApiTestResponse> => {
    try {
      const response = await api.post('/api/test/endpoint', request)
      return response.data
    } catch (error) {
      console.error('Error testing endpoint:', error)
      throw error
    }
  },

  /**
   * Get API documentation
   */
  getApiDocumentation: async (): Promise<any> => {
    try {
      const response = await api.get('/api/test/documentation')
      return response.data
    } catch (error) {
      console.error('Error fetching API documentation:', error)
      throw error
    }
  },

  /**
   * Save test configuration
   */
  saveTestConfiguration: async (config: any): Promise<any> => {
    try {
      const response = await api.post('/api/test/configurations', config)
      return response.data
    } catch (error) {
      console.error('Error saving test configuration:', error)
      throw error
    }
  },

  /**
   * Get saved test configurations
   */
  getTestConfigurations: async (): Promise<any[]> => {
    try {
      const response = await api.get('/api/test/configurations')
      return response.data.data
    } catch (error) {
      console.error('Error fetching test configurations:', error)
      throw error
    }
  }
}
