/**
 * Widget API Service
 * 
 * This file contains all API calls related to widget functionality
 */

import { Widget, WidgetCreate, WidgetUpdate } from '../schemas/widget-schema'
import { api } from '@/lib/api'

const WIDGET_API_PATH = '/api/widgets'

export const widgetService = {
    async getAll(): Promise<Widget[]> {
        const response = await api.get(WIDGET_API_PATH)
        return response.data
    },

    async getById(id: string): Promise<Widget> {
        const response = await api.get(`${WIDGET_API_PATH}/${id}`)
        return response.data
    },

    async create(widget: WidgetCreate): Promise<Widget> {
        const response = await api.post(WIDGET_API_PATH, widget)
        return response.data
    },

    async update(id: string, widget: WidgetUpdate): Promise<Widget> {
        const response = await api.patch(`${WIDGET_API_PATH}/${id}`, widget)
        return response.data
    },

    async delete(id: string): Promise<void> {
        await api.delete(`${WIDGET_API_PATH}/${id}`)
    },

    async generateEmbedCode(id: string): Promise<string> {
        const response = await api.get(`${WIDGET_API_PATH}/${id}/embed`)
        return response.data.code
    },

    async toggleActive(id: string, isActive: boolean): Promise<Widget> {
        const response = await api.patch(`${WIDGET_API_PATH}/${id}/toggle`, { isActive })
        return response.data
    },

    async validateDomain(domain: string): Promise<{ isValid: boolean; message?: string }> {
        const response = await api.post(`${WIDGET_API_PATH}/validate-domain`, { domain })
        return response.data
    }
}

export default widgetService; 