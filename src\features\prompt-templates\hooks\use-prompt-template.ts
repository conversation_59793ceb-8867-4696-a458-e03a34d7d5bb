/**
 * usePromptTemplate Hook
 * 
 * Custom hook for prompt template management
 */

import { useState, useCallback } from 'react'
import { promptTemplateService } from '../api/prompt-template-service'
import {
    PromptTemplate,
    TemplateFilters,
    PromptTemplateUsage,
    TemplateEvaluation,
    ProcessedTemplate
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function usePromptTemplate() {
    const [templates, setTemplates] = useState<PromptTemplate[]>([])
    const [currentTemplate, setCurrentTemplate] = useState<PromptTemplate | null>(null)
    const [processedTemplate, setProcessedTemplate] = useState<ProcessedTemplate | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isSaving, setIsSaving] = useState<boolean>(false)
    const [isProcessing, setIsProcessing] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch all templates
     */
    const fetchTemplates = useCallback(async (filters?: TemplateFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedTemplates = await promptTemplateService.getTemplates(filters)
            setTemplates(fetchedTemplates)
            return fetchedTemplates
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single template
     */
    const fetchTemplate = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const template = await promptTemplateService.getTemplate(id)
            setCurrentTemplate(template)
            return template
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch templates for an AI model
     */
    const fetchTemplatesForModel = useCallback(async (modelId: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedTemplates = await promptTemplateService.getTemplatesForModel(modelId)
            setTemplates(fetchedTemplates)
            return fetchedTemplates
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Get default template for a category
     */
    const fetchDefaultTemplate = useCallback(async (category: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const template = await promptTemplateService.getDefaultTemplate(category)
            return template
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new template
     */
    const createTemplate = useCallback(async (template: Omit<PromptTemplate, 'id'>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newTemplate = await promptTemplateService.createTemplate(template)
            setTemplates(prev => [...prev, newTemplate])
            setCurrentTemplate(newTemplate)
            return newTemplate
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a template
     */
    const updateTemplate = useCallback(async (id: string, template: Partial<PromptTemplate>) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedTemplate = await promptTemplateService.updateTemplate(id, template)
            setTemplates(prev => prev.map(t => t.id === id ? updatedTemplate : t))

            if (currentTemplate?.id === id) {
                setCurrentTemplate(updatedTemplate)
            }

            return updatedTemplate
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentTemplate])

    /**
     * Delete a template
     */
    const deleteTemplate = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await promptTemplateService.deleteTemplate(id)
            setTemplates(prev => prev.filter(t => t.id !== id))

            if (currentTemplate?.id === id) {
                setCurrentTemplate(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentTemplate])

    /**
     * Set a template as default
     */
    const setAsDefaultTemplate = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedTemplate = await promptTemplateService.setDefaultTemplate(id)

            // Update templates in list
            setTemplates(prev => prev.map(t => {
                if (t.id === id) {
                    return updatedTemplate
                }
                // Set all other templates of the same category to non-default
                if (t.category === updatedTemplate.category && t.isDefault) {
                    return { ...t, isDefault: false }
                }
                return t
            }))

            if (currentTemplate?.id === id) {
                setCurrentTemplate(updatedTemplate)
            }

            return updatedTemplate
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentTemplate])

    /**
     * Toggle template status
     */
    const toggleTemplateStatus = useCallback(async (id: string, isActive: boolean) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedTemplate = await promptTemplateService.toggleTemplateStatus(id, isActive)
            setTemplates(prev => prev.map(t => t.id === id ? updatedTemplate : t))

            if (currentTemplate?.id === id) {
                setCurrentTemplate(updatedTemplate)
            }

            return updatedTemplate
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentTemplate])

    /**
     * Process a template with variables
     */
    const processTemplate = useCallback(async (usage: PromptTemplateUsage) => {
        setIsProcessing(true)
        setError(null)

        try {
            const result = await promptTemplateService.processTemplate(usage)
            setProcessedTemplate(result)
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsProcessing(false)
        }
    }, [])

    /**
     * Submit template evaluation
     */
    const evaluateTemplate = useCallback(async (evaluation: Omit<TemplateEvaluation, 'id' | 'timestamp'>) => {
        setIsLoading(true)
        setError(null)

        try {
            return await promptTemplateService.evaluateTemplate(evaluation)
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Clone a template
     */
    const cloneTemplate = useCallback(async (id: string, updates?: Partial<PromptTemplate>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newTemplate = await promptTemplateService.cloneTemplate(id, updates)
            setTemplates(prev => [...prev, newTemplate])
            setCurrentTemplate(newTemplate)
            return newTemplate
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Clear current template
     */
    const clearCurrentTemplate = useCallback(() => {
        setCurrentTemplate(null)
    }, [])

    /**
     * Clear processed template
     */
    const clearProcessedTemplate = useCallback(() => {
        setProcessedTemplate(null)
    }, [])

    /**
     * Clear error
     */
    const clearError = useCallback(() => {
        setError(null)
    }, [])

    return {
        templates,
        currentTemplate,
        processedTemplate,
        isLoading,
        isSaving,
        isProcessing,
        error,
        fetchTemplates,
        fetchTemplate,
        fetchTemplatesForModel,
        fetchDefaultTemplate,
        createTemplate,
        updateTemplate,
        deleteTemplate,
        setAsDefaultTemplate,
        toggleTemplateStatus,
        processTemplate,
        evaluateTemplate,
        cloneTemplate,
        clearCurrentTemplate,
        clearProcessedTemplate,
        clearError
    }
} 