/**
 * useContextRule Hook
 * 
 * Custom hook for context rule management
 */

import { useState, useCallback } from 'react'
import { contextRuleService } from '../api/context-rule-service'
import {
    ContextRule,
    RuleFilters,
    RuleEvaluationInput,
    ContextMatchResult,
    ContextData
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useContextRule() {
    const [rules, setRules] = useState<ContextRule[]>([])
    const [currentRule, setCurrentRule] = useState<ContextRule | null>(null)
    const [contextData, setContextData] = useState<ContextData>({})
    const [matchResults, setMatchResults] = useState<ContextMatchResult[]>([])
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isSaving, setIsSaving] = useState<boolean>(false)
    const [isEvaluating, setIsEvaluating] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)

    /**
     * Fetch all rules
     */
    const fetchRules = useCallback(async (filters?: RuleFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedRules = await contextRuleService.getRules(filters)
            setRules(fetchedRules)
            return fetchedRules
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single rule
     */
    const fetchRule = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const rule = await contextRuleService.getRule(id)
            setCurrentRule(rule)
            return rule
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new rule
     */
    const createRule = useCallback(async (rule: Omit<ContextRule, 'id'>) => {
        setIsSaving(true)
        setError(null)

        try {
            const newRule = await contextRuleService.createRule(rule)
            setRules(prev => [...prev, newRule])
            setCurrentRule(newRule)
            return newRule
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a rule
     */
    const updateRule = useCallback(async (id: string, rule: Partial<ContextRule>) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedRule = await contextRuleService.updateRule(id, rule)
            setRules(prev => prev.map(r => r.id === id ? updatedRule : r))

            if (currentRule?.id === id) {
                setCurrentRule(updatedRule)
            }

            return updatedRule
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentRule])

    /**
     * Delete a rule
     */
    const deleteRule = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await contextRuleService.deleteRule(id)
            setRules(prev => prev.filter(r => r.id !== id))

            if (currentRule?.id === id) {
                setCurrentRule(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentRule])

    /**
     * Toggle rule status
     */
    const toggleRuleStatus = useCallback(async (id: string, isActive: boolean) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedRule = await contextRuleService.toggleRuleStatus(id, isActive)
            setRules(prev => prev.map(r => r.id === id ? updatedRule : r))

            if (currentRule?.id === id) {
                setCurrentRule(updatedRule)
            }

            return updatedRule
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentRule])

    /**
     * Evaluate rules
     */
    const evaluateRules = useCallback(async (input: RuleEvaluationInput) => {
        setIsEvaluating(true)
        setError(null)

        try {
            const results = await contextRuleService.evaluateRules(input)
            setMatchResults(results)
            return results
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsEvaluating(false)
        }
    }, [])

    /**
     * Get context data
     */
    const getContextData = useCallback(async (sessionId: string, scope?: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const data = await contextRuleService.getContextData(sessionId, scope)
            setContextData(data)
            return data
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return {}
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Update context data
     */
    const updateContextData = useCallback(async (sessionId: string, data: ContextData, scope?: string) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedData = await contextRuleService.updateContextData(sessionId, data, scope)
            setContextData(updatedData)
            return updatedData
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Clear context data
     */
    const clearContextData = useCallback(async (sessionId: string, scope?: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await contextRuleService.clearContextData(sessionId, scope)
            setContextData({})
            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Test a rule
     */
    const testRule = useCallback(async (rule: ContextRule, input: RuleEvaluationInput) => {
        setIsEvaluating(true)
        setError(null)

        try {
            const result = await contextRuleService.testRule(rule, input)
            return result
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsEvaluating(false)
        }
    }, [])

    /**
     * Clear current rule
     */
    const clearCurrentRule = useCallback(() => {
        setCurrentRule(null)
    }, [])

    /**
     * Clear match results
     */
    const clearMatchResults = useCallback(() => {
        setMatchResults([])
    }, [])

    /**
     * Clear error
     */
    const clearError = useCallback(() => {
        setError(null)
    }, [])

    return {
        rules,
        currentRule,
        contextData,
        matchResults,
        isLoading,
        isSaving,
        isEvaluating,
        error,
        fetchRules,
        fetchRule,
        createRule,
        updateRule,
        deleteRule,
        toggleRuleStatus,
        evaluateRules,
        getContextData,
        updateContextData,
        clearContextData,
        testRule,
        clearCurrentRule,
        clearMatchResults,
        clearError
    }
} 