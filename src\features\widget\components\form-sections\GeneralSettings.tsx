/**
 * General Settings Form Section
 * 
 * Basic widget configuration form fields
 */

import { UseFormReturn } from 'react-hook-form'
import { Widget } from '../../types'
import { FormField, FormLabel, FormControl, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'

interface GeneralSettingsProps {
  form: UseFormReturn<Widget>
}

export function GeneralSettings({ form }: GeneralSettingsProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-lg font-medium">General Settings</h2>
      <p className="text-sm text-gray-500">
        Basic information about your widget
      </p>

      <div className="grid grid-cols-1 gap-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Widget Name</FormLabel>
              <FormControl>
                <Input placeholder="My Chat Widget" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="A brief description of this widget" 
                  rows={3} 
                  {...field} 
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Active Status</FormLabel>
                <p className="text-sm text-gray-500">
                  Toggle to enable or disable this widget
                </p>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}

export default GeneralSettings 