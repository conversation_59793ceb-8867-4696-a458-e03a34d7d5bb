'use client'

import { Info } from 'lucide-react'
import { ControllerRenderProps } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { useIntegrations, Integration } from '@/features/widget'
import { IntegrationForm, IntegrationCard, EmptyIntegrations } from './integrations'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

/**
 * Declare global types for Google Analytics tracking
 */
declare global {
    interface Window {
        gtag?: (command: string, action: string, params?: Record<string, any>) => void;
    }
}

interface IntegrationsTabProps {
    field: ControllerRenderProps<any, any>
    webhookUrl?: string
}

/**
 * IntegrationsTab Component
 * Provides UI for managing webhook integrations for the chat widget
 */
export default function IntegrationsTab({ field, webhookUrl }: IntegrationsTabProps) {
    const {
        integrations,
        currentIntegration,
        isEditing,
        isTestingConnection,
        testResult,
        handleAddIntegration,
        handleEditIntegration,
        handleDeleteIntegration,
        handleToggleActive,
        handleSaveIntegration,
        handleCancelEdit,
        handleTestConnection,
        handleIntegrationChange,
        handleEventToggle
    } = useIntegrations({ field, webhookUrl });

    // If we're editing, show the edit form
    if (isEditing && currentIntegration) {
        return (
            <IntegrationForm
                integration={currentIntegration}
                isTestingConnection={isTestingConnection}
                testResult={testResult}
                onCancel={handleCancelEdit}
                onSave={handleSaveIntegration}
                onTest={handleTestConnection}
                onFieldChange={handleIntegrationChange}
                onEventToggle={handleEventToggle}
            />
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Integrations</h3>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button>Add Integration</Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Integration Types</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleAddIntegration('slack')}>
                            Slack
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleAddIntegration('ms-teams')}>
                            Microsoft Teams
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleAddIntegration('discord')}>
                            Discord
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleAddIntegration('zapier')}>
                            Zapier
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleAddIntegration('generic')}>
                            Custom Webhook
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {integrations.length === 0 ? (
                <EmptyIntegrations onAddIntegration={handleAddIntegration} />
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {integrations.map(integration => (
                        <IntegrationCard
                            key={integration.id}
                            integration={integration}
                            onEdit={handleEditIntegration}
                            onToggleActive={handleToggleActive}
                            onDelete={handleDeleteIntegration}
                        />
                    ))}
                </div>
            )}

            <div className="text-sm text-muted-foreground">
                <p className="flex items-center gap-1 mb-1">
                    <Info className="h-4 w-4" />
                    Integrations allow you to receive chat notifications in your favorite platforms.
                </p>
                <p>Get notified when users send messages, provide ratings, or start/end chat sessions.</p>
            </div>
        </div>
    )
}