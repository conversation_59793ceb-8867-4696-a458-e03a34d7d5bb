/**
 * useBranding Hook
 * 
 * Custom hook for branding management
 */

import { useState, useCallback, useEffect } from 'react'
import { brandingService } from '../api/branding-service'
import {
    Branding,
    BrandingFilters,
    CreateBrandingInput,
    UpdateBrandingInput,
    MessageFormat,
    MessageFormatFilters,
    CreateMessageFormatInput,
    UpdateMessageFormatInput,
    FormatMessageInput
} from '../types'
import { toAppError, AppError } from '@/lib/error-handler'

export function useBranding() {
    const [brandings, setBrandings] = useState<Branding[]>([])
    const [currentBranding, setCurrentBranding] = useState<Branding | null>(null)
    const [defaultBranding, setDefaultBranding] = useState<Branding | null>(null)
    const [messageFormats, setMessageFormats] = useState<MessageFormat[]>([])
    const [currentFormat, setCurrentFormat] = useState<MessageFormat | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isSaving, setIsSaving] = useState<boolean>(false)
    const [error, setError] = useState<AppError | null>(null)
    const [cssVariables, setCssVariables] = useState<string>('')

    /**
     * Fetch all brandings
     */
    const fetchBrandings = useCallback(async (filters?: BrandingFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const fetchedBrandings = await brandingService.getBrandings(filters)
            setBrandings(fetchedBrandings)
            return fetchedBrandings
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single branding
     */
    const fetchBranding = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const branding = await brandingService.getBranding(id)
            setCurrentBranding(branding)
            return branding
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch the default branding
     */
    const fetchDefaultBranding = useCallback(async () => {
        setIsLoading(true)
        setError(null)

        try {
            const branding = await brandingService.getDefaultBranding()
            setDefaultBranding(branding)
            return branding
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a new branding
     */
    const createBranding = useCallback(async (branding: CreateBrandingInput) => {
        setIsSaving(true)
        setError(null)

        try {
            const newBranding = await brandingService.createBranding(branding)
            setBrandings(prev => [...prev, newBranding])
            setCurrentBranding(newBranding)

            if (newBranding.isDefault) {
                setDefaultBranding(newBranding)
            }

            return newBranding
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a branding
     */
    const updateBranding = useCallback(async (id: string, branding: UpdateBrandingInput) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedBranding = await brandingService.updateBranding(id, branding)
            setBrandings(prev => prev.map(b => b.id === id ? updatedBranding : b))

            if (currentBranding?.id === id) {
                setCurrentBranding(updatedBranding)
            }

            if (updatedBranding.isDefault) {
                setDefaultBranding(updatedBranding)
            } else if (defaultBranding?.id === id && !updatedBranding.isDefault) {
                // If this was default and is no longer default, clear default branding
                setDefaultBranding(null)
            }

            return updatedBranding
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentBranding, defaultBranding])

    /**
     * Delete a branding
     */
    const deleteBranding = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await brandingService.deleteBranding(id)
            setBrandings(prev => prev.filter(b => b.id !== id))

            if (currentBranding?.id === id) {
                setCurrentBranding(null)
            }

            if (defaultBranding?.id === id) {
                setDefaultBranding(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentBranding, defaultBranding])

    /**
     * Set branding as default
     */
    const setAsDefaultBranding = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const updatedBranding = await brandingService.setDefaultBranding(id)

            // Update branding in list
            setBrandings(prev => prev.map(b => {
                if (b.id === id) {
                    return updatedBranding
                }
                // Set all other brandings to non-default
                if (b.isDefault) {
                    return { ...b, isDefault: false }
                }
                return b
            }))

            // Update current branding if it's the one being changed
            if (currentBranding?.id === id) {
                setCurrentBranding(updatedBranding)
            }

            // Update default branding
            setDefaultBranding(updatedBranding)

            return updatedBranding
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [currentBranding])

    /**
     * Generate CSS variables
     */
    const generateCssVars = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const css = await brandingService.generateCssVariables(id)
            setCssVariables(css)
            return css
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return ''
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch all message formats
     */
    const fetchMessageFormats = useCallback(async (filters?: MessageFormatFilters) => {
        setIsLoading(true)
        setError(null)

        try {
            const formats = await brandingService.getMessageFormats(filters)
            setMessageFormats(formats)
            return formats
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return []
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Fetch a single message format
     */
    const fetchMessageFormat = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            const format = await brandingService.getMessageFormat(id)
            setCurrentFormat(format)
            return format
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Create a message format
     */
    const createMessageFormat = useCallback(async (format: CreateMessageFormatInput) => {
        setIsSaving(true)
        setError(null)

        try {
            const newFormat = await brandingService.createMessageFormat(format)
            setMessageFormats(prev => [...prev, newFormat])
            setCurrentFormat(newFormat)
            return newFormat
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [])

    /**
     * Update a message format
     */
    const updateMessageFormat = useCallback(async (id: string, format: UpdateMessageFormatInput) => {
        setIsSaving(true)
        setError(null)

        try {
            const updatedFormat = await brandingService.updateMessageFormat(id, format)
            setMessageFormats(prev => prev.map(f => f.id === id ? updatedFormat : f))

            if (currentFormat?.id === id) {
                setCurrentFormat(updatedFormat)
            }

            return updatedFormat
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return null
        } finally {
            setIsSaving(false)
        }
    }, [currentFormat])

    /**
     * Delete a message format
     */
    const deleteMessageFormat = useCallback(async (id: string) => {
        setIsLoading(true)
        setError(null)

        try {
            await brandingService.deleteMessageFormat(id)
            setMessageFormats(prev => prev.filter(f => f.id !== id))

            if (currentFormat?.id === id) {
                setCurrentFormat(null)
            }

            return true
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return false
        } finally {
            setIsLoading(false)
        }
    }, [currentFormat])

    /**
     * Format a message
     */
    const formatMessage = useCallback(async (data: FormatMessageInput) => {
        setIsLoading(true)
        setError(null)

        try {
            return await brandingService.formatMessage(data)
        } catch (err) {
            const appError = toAppError(err)
            setError(appError)
            return ''
        } finally {
            setIsLoading(false)
        }
    }, [])

    /**
     * Clear current branding
     */
    const clearCurrentBranding = useCallback(() => {
        setCurrentBranding(null)
    }, [])

    /**
     * Clear current format
     */
    const clearCurrentFormat = useCallback(() => {
        setCurrentFormat(null)
    }, [])

    /**
     * Clear error
     */
    const clearError = useCallback(() => {
        setError(null)
    }, [])

    // Fetch default branding on mount
    useEffect(() => {
        fetchDefaultBranding()
    }, [fetchDefaultBranding])

    return {
        brandings,
        currentBranding,
        defaultBranding,
        messageFormats,
        currentFormat,
        isLoading,
        isSaving,
        error,
        cssVariables,
        fetchBrandings,
        fetchBranding,
        fetchDefaultBranding,
        createBranding,
        updateBranding,
        deleteBranding,
        setAsDefaultBranding,
        generateCssVars,
        fetchMessageFormats,
        fetchMessageFormat,
        createMessageFormat,
        updateMessageFormat,
        deleteMessageFormat,
        formatMessage,
        clearCurrentBranding,
        clearCurrentFormat,
        clearError
    }
} 