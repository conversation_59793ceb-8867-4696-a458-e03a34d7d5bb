"use client"

import { useState, useEffect } from "react"
import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import {
  AlertCircle,
  CheckCircle2,
  HelpCircle,
  Info,
  User,
  Mail,
  Phone,
  MessageSquare
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { PreChatFormField as FormFieldType } from "@/types/widget"
import {
  TextField,
  EmailField,
  PhoneField,
  SelectField,
  CheckboxField,
  TextareaField
} from "./form-fields"

interface EnhancedPreChatFormProps {
  onSubmit: (data: Record<string, any>) => void
  onCancel: () => void
  fields?: FormFieldType[]
  title?: string
  description?: string
  primaryColor?: string
  showInitialMessage?: boolean
  isLoading?: boolean
  className?: string
  showCancelButton?: boolean
  isRequired?: boolean
}

/**
 * Enhanced Pre-Chat Form Component
 *
 * A user-friendly form that collects visitor information before starting a chat.
 * Designed for non-technical users with clear instructions and visual cues.
 */
export function EnhancedPreChatForm({
  onSubmit,
  onCancel,
  fields = [],
  title = "Welcome! Let's get to know you",
  description = "Please share a few details so we can provide you with better assistance.",
  primaryColor = "#7E69AB",
  showInitialMessage = true,
  isLoading = false,
  className,
  showCancelButton = true,
  isRequired = false
}: EnhancedPreChatFormProps) {
  const [formSubmitted, setFormSubmitted] = useState(false)

  // Create a dynamic schema based on the fields
  const createFormSchema = () => {
    const schemaFields: Record<string, any> = {}

    // Add default fields if no custom fields provided
    if (fields.length === 0) {
      schemaFields.name = z.string().min(1, "Please enter your name")
      schemaFields.email = z.string().email("Please enter a valid email address")

      // Enhanced phone validation
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      schemaFields.phone = z.string()
        .min(10, "Phone number must be at least 10 digits")
        .max(17, "Phone number must be no more than 17 characters")
        .regex(phoneRegex, "Please enter a valid phone number (numbers only, optional + prefix)")

      if (showInitialMessage) {
        schemaFields.message = z.string().optional()
      }
    } else {
      // Add fields from props
      fields.forEach(field => {
        if (field.type === 'text') {
          const schema = field.isRequired
            ? z.string().min(1, field.errorMessage || `Please enter your ${field.label.toLowerCase()}`)
            : z.string().optional()

          schemaFields[field.name] = schema
        }
        else if (field.type === 'email') {
          const schema = field.isRequired
            ? z.string().email(field.errorMessage || "Please enter a valid email address")
            : z.string().email("Please enter a valid email address").optional()

          schemaFields[field.name] = schema
        }
        else if (field.type === 'phone') {
          const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
          const schema = field.isRequired
            ? z.string()
              .min(10, "Phone number must be at least 10 digits")
              .max(17, "Phone number must be no more than 17 characters")
              .regex(phoneRegex, field.errorMessage || "Please enter a valid phone number (numbers only, optional + prefix)")
            : z.string()
              .regex(phoneRegex, "Please enter a valid phone number (numbers only, optional + prefix)")
              .optional()

          schemaFields[field.name] = schema
        }
        else if (field.type === 'select') {
          const schema = field.isRequired
            ? z.string().min(1, field.errorMessage || "Please select an option")
            : z.string().optional()

          schemaFields[field.name] = schema
        }
        else if (field.type === 'checkbox') {
          schemaFields[field.name] = z.boolean().optional()
        }
      })
    }

    return z.object(schemaFields)
  }

  const formSchema = createFormSchema()

  // Set up the form with react-hook-form and zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })

  // Get default values for the form
  function getDefaultValues() {
    const defaults: Record<string, any> = {}

    if (fields.length === 0) {
      defaults.name = ""
      defaults.email = ""
      defaults.phone = ""

      if (showInitialMessage) {
        defaults.message = ""
      }
    } else {
      fields.forEach(field => {
        if (field.type === 'checkbox') {
          defaults[field.name] = false
        } else {
          defaults[field.name] = ""
        }
      })
    }

    return defaults
  }

  // Handle form submission
  const handleSubmit = (data: z.infer<typeof formSchema>) => {
    setFormSubmitted(true)
    onSubmit(data)
  }

  // Render default fields if no custom fields provided
  const renderDefaultFields = () => (
    <div className="space-y-4">
      <TextField
        form={form}
        name="name"
        label="Name"
        placeholder="Your name"
        icon={<User className="h-4 w-4 text-muted-foreground" />}
        required
      />

      <EmailField
        form={form}
        name="email"
        label="Email"
        placeholder="<EMAIL>"
        icon={<Mail className="h-4 w-4 text-muted-foreground" />}
        required
      />

      <PhoneField
        form={form}
        name="phone"
        label="Phone Number"
        placeholder="1234567890 or +1234567890"
        description="Enter numbers only (10-15 digits, optional + prefix)"
        icon={<Phone className="h-4 w-4 text-muted-foreground" />}
        required
      />

      {showInitialMessage && (
        <TextareaField
          form={form}
          name="message"
          label="What can we help you with?"
          placeholder="Type your question or describe what you need help with"
          icon={<MessageSquare className="h-4 w-4 text-muted-foreground" />}
        />
      )}
    </div>
  )

  // Render custom fields from props
  const renderCustomFields = () => (
    <div className="space-y-4">
      {fields.map((field) => {
        switch (field.type) {
          case 'text':
            return (
              <TextField
                key={field.id}
                form={form}
                name={field.name}
                label={field.label}
                placeholder={field.placeholder}
                required={field.isRequired}
                icon={<User className="h-4 w-4 text-muted-foreground" />}
              />
            )
          case 'email':
            return (
              <EmailField
                key={field.id}
                form={form}
                name={field.name}
                label={field.label}
                placeholder={field.placeholder}
                required={field.isRequired}
                icon={<Mail className="h-4 w-4 text-muted-foreground" />}
              />
            )
          case 'phone':
            return (
              <PhoneField
                key={field.id}
                form={form}
                name={field.name}
                label={field.label}
                placeholder={field.placeholder}
                required={field.isRequired}
                icon={<Phone className="h-4 w-4 text-muted-foreground" />}
              />
            )
          case 'select':
            return (
              <SelectField
                key={field.id}
                form={form}
                name={field.name}
                label={field.label}
                placeholder={field.placeholder || "Select an option"}
                options={field.options || []}
                required={field.isRequired}
              />
            )
          case 'checkbox':
            return (
              <CheckboxField
                key={field.id}
                form={form}
                name={field.name}
                label={field.label}
                required={field.isRequired}
              />
            )
          default:
            return null
        }
      })}
    </div>
  )

  return (
    <Card className={cn("border shadow-sm w-full max-w-md mx-auto", className)}>
      <CardHeader className="pb-4" style={{ borderBottom: "1px solid #f1f5f9" }}>
        <CardTitle className="text-xl font-semibold" style={{ color: primaryColor }}>
          {title}
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          {description}
        </CardDescription>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="pt-6 pb-4">
            <Alert variant="outline" className="mb-5 bg-blue-50/50 border-blue-100">
              <Info className="h-4 w-4 text-blue-500" />
              <AlertDescription className="text-sm text-blue-700">
                Your information helps us provide personalized assistance.
              </AlertDescription>
            </Alert>

            {fields.length > 0 ? renderCustomFields() : renderDefaultFields()}

            {formSubmitted && (
              <Alert variant="default" className="mt-4 bg-green-50 border-green-100 text-green-700">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-sm">
                  Thank you! Starting your chat session...
                </AlertDescription>
              </Alert>
            )}
          </CardContent>

          <CardFooter className="flex justify-between border-t bg-muted/20 p-4">
            <div className="flex items-center">
              {isRequired && (
                <Alert variant="outline" className="border-amber-200 bg-amber-50/50 p-2 mr-3">
                  <AlertCircle className="h-3 w-3 text-amber-600" />
                  <AlertDescription className="text-xs text-amber-700 ml-1">
                    Required to continue
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <div className="flex gap-2">
              {showCancelButton && !isRequired && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || formSubmitted}
                  className="min-w-[100px]"
                >
                  Skip for now
                </Button>
              )}

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="submit"
                      disabled={isLoading || formSubmitted}
                      style={{ backgroundColor: primaryColor }}
                      className="relative min-w-[120px]"
                    >
                      {isLoading ? (
                        <>
                          <span className="mr-2 h-4 w-4 rounded-full border-2 border-b-transparent border-white animate-spin inline-block"></span>
                          Processing...
                        </>
                      ) : formSubmitted ? (
                        <>
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                          Starting Chat
                        </>
                      ) : (
                        "Start Chat"
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isRequired ? "Complete form to continue" : "Begin your conversation"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
