'use client'

import { Settings } from 'lucide-react'
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Integration } from '@/features/widget/types/integration'

interface EmptyIntegrationsProps {
    onAddIntegration: (type: Integration['type']) => void
}

export function EmptyIntegrations({ onAddIntegration }: EmptyIntegrationsProps) {
    return (
        <Card className="border-dashed">
            <CardContent className="pt-6 flex flex-col items-center justify-center text-center space-y-2 p-10">
                <div className="rounded-full bg-muted h-10 w-10 flex items-center justify-center">
                    <Settings className="h-5 w-5 text-muted-foreground" />
                </div>
                <CardTitle className="text-base">No integrations configured</CardTitle>
                <CardDescription>
                    Add integrations to send chat notifications to your favorite platforms.
                </CardDescription>
                <Button
                    className="mt-4"
                    variant="outline"
                    onClick={() => onAddIntegration('slack')}
                >
                    Add Your First Integration
                </Button>
            </CardContent>
        </Card>
    )
} 