/**
 * AI Configuration Schemas
 */

import { z } from 'zod'

export const modelSettingsSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(100000).optional(),
  topP: z.number().min(0).max(1).optional(),
  frequencyPenalty: z.number().min(-2).max(2).optional(),
  presencePenalty: z.number().min(-2).max(2).optional(),
  systemPrompt: z.string().optional(),
  contextWindow: z.number().min(1).optional(),
  streaming: z.boolean().optional(),
  apiKey: z.string().optional(),
  baseUrl: z.string().url().optional(),
  customHeaders: z.record(z.string()).optional()
})

export const aiConfigurationSchema = z.object({
  provider: z.string().min(1, 'Provider is required'),
  model: z.string().min(1, 'Model is required'),
  settings: modelSettingsSchema,
  isActive: z.boolean().default(true)
})

export type ModelSettingsInput = z.infer<typeof modelSettingsSchema>
export type AiConfigurationInput = z.infer<typeof aiConfigurationSchema>
