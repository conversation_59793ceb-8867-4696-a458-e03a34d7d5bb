/**
 * Prompt Template Types
 * 
 * This file exports all type definitions for the prompt template feature
 */

import { z } from 'zod';
import {
    promptVariableSchema,
    promptTemplateSchema,
    promptTemplateUsageSchema,
    templateEvaluationSchema
} from '../schemas/prompt-template-schema';

/**
 * Prompt variable type
 */
export type PromptVariable = z.infer<typeof promptVariableSchema>;

/**
 * Variable type enum
 */
export type VariableType = 'string' | 'number' | 'boolean' | 'array';

/**
 * Prompt template type
 */
export type PromptTemplate = z.infer<typeof promptTemplateSchema>;

/**
 * Template category type
 */
export type TemplateCategory = 'system' | 'user' | 'assistant' | 'function' | 'custom';

/**
 * Prompt template usage type
 */
export type PromptTemplateUsage = z.infer<typeof promptTemplateUsageSchema>;

/**
 * Template evaluation type
 */
export type TemplateEvaluation = z.infer<typeof templateEvaluationSchema>;

/**
 * Template performance metrics
 */
export interface TemplatePerformance {
    tokensUsed: number;
    processingTimeMs: number;
}

/**
 * Template filters
 */
export interface TemplateFilters {
    search?: string;
    category?: TemplateCategory;
    isActive?: boolean;
    isDefault?: boolean;
    aiModelId?: string;
    tags?: string[];
    sortBy?: keyof PromptTemplate;
    sortDirection?: 'asc' | 'desc';
}

/**
 * Processed template result
 */
export interface ProcessedTemplate {
    content: string;
    tokensEstimate: number;
    missingVariables: string[];
} 